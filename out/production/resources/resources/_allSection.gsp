<asset:stylesheet href="wonderslate/audioPlayer.css" async="true"/>
<style>
.tests-access .unlock-tests{
    z-index: 0 !important;
}
</style>
<div class="modal fade" id="addedTodo">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <i class="material-icons">check_circle_outline</i>
                <p>To-Do Added Successfully</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button"  class="btn btn-success" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="hostedVideoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="videoClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <video width="320" height="240" controls id="hostedVideo">

                </video>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="hostedAudioModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="audioClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <audio width="320" height="240" controls id="hostedAudio" controlsList="nodownload">

                </audio>
            </div>
        </div>
    </div>
</div>

<!--------  Audio Modal --------->
<div class="modal fade modal-modifier" id="listenCAModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier readCAModalModifier">
            <div class="modal-body modal-body-modifier text-center mcqOptionModalBody">
                <button type="button" class="close mr-2 audioCloseBtn" data-dismiss="modal" aria-label="Close" style="text-align: end">
                    <span aria-hidden="true" class="text-white">x</span>
                </button>
                <div class="readCAModalWrap" id="listenCAContent">
                    <div class="player">
                        <div class="details">
                            <div class="now-playing">PLAYING x OF y</div>
                            <div class="track-art"></div>
                            <div class="track-name">Track Name</div>
                            <div class="track-artist">Track Artist</div>
                        </div>
                        <div class="buttons">
                            <div class="prev-track" onclick="prevTrack()">
                                <i class="fa fa-step-backward fa-2x"></i>
                            </div>
                            <div class="playpause-track" onclick="playpauseTrack()">
                                <i class="fa fa-play-circle fa-3x"></i>
                            </div>
                            <div class="next-track" onclick="nextTrack()">
                                <i class="fa fa-step-forward fa-2x"></i>
                            </div>
                        </div>

                        <div class="slider_container">
                            <div class="current-time">00:00</div>
                            <input type="range" min="1" max="100"
                                   value="0" class="seek_slider" onchange="seekTo()">
                            <div class="total-duration">00:00</div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var chapterDetailsData;
    var chapterName;
    var chapterGrade;
    var chapterSyllabus;
    var chapterDesc;
    var chapterSubject;
    var chapterLevel;
    var flashCardsPresent = false;
    var flashCards = [];
    var resourceTypes = [];
    var suggestedVideos;
    var testSeriesBook=false;
    var boughtTestSeries = false;
    var hasMCQs = false;
    var previewChapter = false;
    var prepjoySite ="${session['prepjoySite']}";
    var player;
    var masterChapterID;
    var showResourceList=false;

    let now_playing = document.querySelector(".now-playing");
    let track_art = document.querySelector(".track-art");
    let track_name = document.querySelector(".track-name");
    let track_artist = document.querySelector(".track-artist");

    let playpause_btn = document.querySelector(".playpause-track");
    let next_btn = document.querySelector(".next-track");
    let prev_btn = document.querySelector(".prev-track");

    let seek_slider = document.querySelector(".seek_slider");
    let volume_slider = document.querySelector(".volume_slider");
    let curr_time = document.querySelector(".current-time");
    let total_duration = document.querySelector(".total-duration");

    // Specify globally used values
    let track_index = 0;
    let isPlaying = false;
    let updateTimer;

    let curr_track = document.createElement('audio');
    let track_list = [];

    function chapterDetailsRecieved(data){
        chapterDetailsData = data.results;
        suggestedVideos = JSON.parse(data.suggestedVideos);
        seenResources = data.seenResouces;
        testSeriesBook = data.testSeriesBook;
        boughtTestSeries = data.boughtTestSeries;
        previewChapter = data.previewChapter;
        masterChapterID = data.chapterId;
         expiryDate=data.expiryDate;
        if(expiryDate!=null) {
            document.getElementById('expiryDate').innerHTML = "Expires on " + moment.utc(expiryDate,'YYYY/MM/DD').local().format("DD-MM-YYYY");
            $('#expiryDate').show();
        }
        populateAllSection(chapterDetailsData);
        var chpList =  document.querySelectorAll('.read-book-chapters-wrapper li');
        chpList.forEach(item=>{
            item.classList.remove('orangeText');
        });
        document.getElementById("chapterName"+data.chapterId).classList.add('orangeText');
        if ($(window).width() < 768){
            if (showResourceList){
                document.querySelector('.side-chapters').classList.add('d-none');
                document.querySelector('.resource-contents .tab-content').classList.remove('d-none');
            }
        }
    }
    function populateAllSection(cData){
        flashCardsPresent=false;
        chapterReadPresent=true;
        hasMCQs = false;

        flashCards = [];

        //remove the chapter name highlighting as it should be section name
        $("#chapterName"+previousChapterId).children('a').removeClass('orangeText');
        var dataTypeLabel="";


        var cStr = "<div class=\"container\">\n" +

            "<div class=\"all-container ebook-access mb-4\"><h6>eBook</h6>";
        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];
            if(data.resType=="Multiple Choice Questions") hasMCQs=true;
            if(i==0){
                chapterName = data.topicName;
                chapterGrade = data.grade;
                chapterSyllabus = data.syllabus;
                chapterSubject = data.subject;
                chapterLevel = data.level;
                chapterDesc = data.chapterDesc;
            }
            if(data.resSubType!=null&&data.resSubType!=""){
                if(!resourceTypes.includes(data.resSubType)) resourceTypes.push(data.resSubType);
            }else{
                if(!resourceTypes.includes(data.resType)) resourceTypes.push(data.resType);
            }
            if(data.resSubType!=null&&data.resSubType!="") dataTypeLabel = data.resSubType;
            else dataTypeLabel = "Lesson";
            cStr +="<div class=\"container-wrapper\" id='res_"+i+"'>\n" +
                "<div class='d-flex justify-content-between align-items-center'>";

            cStr += "        <div class=\"media\">\n" ;
            if(data.resType=="Notes") {
                if(data.sharing==null) {
                    if (data.resLink.includes(".pdf")) {
                        //alert('pdf');
                        cStr += "<a class=\"mb-0 readnow\" href='javascript:displayPdfReadingMaterial(\"" + data.resLink + "\",\""+data.videoPlayer+"\",\""+data.id+"\",\""+data.zoomLevel+"\",\""+data.resName+"\",\""+data.topicId+"\")' id='resAction_"+data.id+"'>\n";
                        cStr += "<div class='box blue'>" +
                            "<div>" +
                            "<i class=\"align-self-center\">\n" +
                            "            </i>\n" + "<p>"+dataTypeLabel+"</p>" +
                            "</div>" +
                            "</div>";

                    } else {
                        cStr += "                         <a class=\"mb-0 readnow\" href='javascript:displayReadingMaterial(" + data.id + ","+data.topicId+")' id='resAction_"+data.id+"'>";
                        cStr += "<div class='box blue'>" +
                            "<div>" +
                            "<i class=\"align-self-center\">\n" +
                            "            </i>\n" + "<p>"+dataTypeLabel+"</p>" +
                            "</div>" +
                            "</div>";


                    }

                }else{
                    if (data.quizMode == "file") {
                        if (data.resLink.includes(".pdf")) {
                            cStr += "<a href='javascript:displayPDFNotes(" + data.id + ",\"" + data.resName + "\")' class=\"mb-0 readnow\" id='resAction_"+data.id+"'>";
                        }
                        else {
                            cStr += "<a href='javascript:downloadFile( " + data.id + ")' class=\"mb-0 readnow\" id='resAction_"+data.id+"'>";

                        }
                        cStr += "<div class='box blue'>" +
                            "<div>" +
                            "<i class=\"align-self-center\">\n" +
                            "            </i>\n" + "<p>"+dataTypeLabel+"</p>" +
                            "</div>" +
                            "</div>";


                    } else {
                        cStr += "<a href='javascript:displayReadingMaterial(" + data.id + ","+data.topicId+")' class=\"mb-0 readnow\" id='resAction_"+data.id+"'>";
                        cStr += "<div class='box blue'>" +
                            "<div>" +
                            "<i class=\"align-self-center\">\n" +
                            "            </i>\n" + "<p>"+dataTypeLabel+"</p>" +
                            "</div>" +
                            "</div>";

                    }

                }


            }
            else if(data.resType=="Videos" || data.resType=="Reference Videos" ) {   //videos
                if("video"==data.quizMode) {
                    cStr += "<a class=\"mb-0 readnow\" href='#'>" ;
                    cStr +=" <div class='box pink'>" +
                        "<div>"+
                        "<i class=\"align-self-center\"></i>"+"<p>Lecture</p>"+
                        "</div>"+"</div>";
                    //"<a href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ")' id='audioPlayButton' class='listen-btn readnow'>Listen</a>\n";
                } else if (!data.resLink.includes("drive.google.com")){
                    cStr += "<a class=\"mb-0 readnow\" href='#'>";
                    cStr +=" <div class='box pink'>" +
                        "<div>"+
                        "<i class=\"align-self-center\"></i>"+"<p>Lecture</p>"+
                        "</div>"+"</div>";
                    //"<a href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ")' id='audioPlayButton' class='listen-btn readnow'>Listen</a>\n";
                }else if(data.resLink.includes("drive.google.com")){
                    cStr += "<a class=\"mb-0 readnow\" href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ",\""+data.resName.replace(/'/g, "&#39;")+"\")'>";
                    cStr +=" <div class='box pink'>" +
                        "<div>"+
                        "<i class=\"align-self-center\"></i>"+"<p>Audio</p>"+
                        "</div>"+"</div>";
                }

            }
            else if(data.resType=="Uploaded Media"  ) {   //videos

                if((""+data.resLink).indexOf(".mp4")>-1) {
                    cStr += "<a class=\"mb-0 readnow\" href='javascript:playHostedMediaVideo(" + data.id + ")' id='resAction_"+data.id+"'>";
                    cStr += " <div class='box pink'>" +
                        "<div>" +
                        "<i class=\"align-self-center\"></i>" + "<p>Video</p>" +
                        "</div>" + "</div>";
                }else{
                    cStr += "<a class=\"mb-0 readnow\" href='javascript:playHostedMediaAudio(" + data.id + ")' id='resAction_"+data.id+"'>";
                    cStr += " <div class='box pink'>" +
                        "<div>" +
                        "<i class=\"align-self-center\"></i>" + "<p>Audio</p>" +
                        "</div>" + "</div>";
                }
            }
            else   if(data.resType=="Reference Web Links") {   //weblinks
                cStr += "<a class=\"mb-0 readnow\" href='javascript:openWebRef(" + data.id + ",\""+data.resLink.replace('#', ':')+"\")' id='resAction_"+data.id+"'>";

                cStr +=" <div class='box green'>   " +
                    "<div>"+
                    "        <i class=\"align-self-center\">\n" +
                    "                \n" +
                    "            </i>\n"+
                    "<p>Link</p>"+
                    "</div>"+
                    "</div>";
            }
            else   if(data.resType=="QA") {   //Question and Answers
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:showQAndA(" + data.resLink + ")' id='resAction_"+data.id+"'>\n";
                cStr +="   <div class='box violet'> " +
                    "<div>"+
                    "<i class=\"align-self-center\">\n" +
                    "                  \n" +
                    "            </i>\n"+
                    "<p>Q & A</p>"+
                    "</div>"+
                    "</div>";
            }
            else   if(data.resType=="KeyValues") {
                if (data.canEdit == "true"){
                    flashCardsPresent = true;
                    flashCards.push({'resId': data.id, 'resName': data.resName});
                }
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:reviseNow(" + data.id + ",\""+data.resName.replace(/'/g,"&#39;")+"\")' id='resAction_"+data.id+"'>";
                cStr +="   <div class='box yellow'>" +
                    "<div>"+
                    " <i class=\"align-self-center\">\n" +
                    "                \n" +
                    "            </i>\n"+
                    "<p>Flash Card</p>"+
                    "</div>"+"</div>";
            }
            else if(data.resType=="Multiple Choice Questions"&&!testSeriesBook){
                cStr += "                    <a class=\"mb-0 readnow\">";
                cStr +="   <div class='box violet'>"+
                    "<div>"+
                    " <i class=\"align-self-center\">\n" +
                    "                \n" +
                    "            </i>\n"+
                    "<p>MCQ</p>"+
                    "</div>"+
                    "</div>";
            }
            if(data.resType!="Multiple Choice Questions"||(data.resType=="Multiple Choice Questions"&&!testSeriesBook)){
            cStr += "            <div class=\"media-body\">\n" +
                "                <p class=\"title\">"+data.resName.replace(/'/g,"&#39;")+"</p>\n" ;
            cStr += "            </div>\n" +
                "</a>";
            }
            if(data.resType=="Multiple Choice Questions"&&!testSeriesBook) {
                cStr += "<div class='d-flex w-100'>";
                if (data.testStartDate){
                    testSeriesQuiz=true;
                    var displayName="Take test";
                    var testAlreadyTaken=false;
                    if(seenResources.includes(","+data.id+",")) {
                        testAlreadyTaken=true;
                        displayName="Re attempt";
                    }

                    if("true"==data.testEnded) cStr += "<a class='testStarts btn watch'><span>Test already ended</span></a>";
                    else if("true"==data.testStarted){
                        cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.resLink + "&resId=" + data.id + "' class='btn watch'><span>"+displayName+"</span></a>";

                    }else cStr += "<a class='testStarts btn watch'><span class='d-flex align-items-center' style='gap: 10px'>Test Starts On"+
                        "<p>"+
                        "<p class='testDate'>"+moment.utc(data.testStartDate).format("D MMM YYYY")+"</p>"+
                        "<p class='seperator'>|</p>"+
                        "<p class='testTime'>"+moment.utc(data.testStartDate).format("h:mm a")+"</p>"+
                        "</p>"+
                        "</span>"+
                        "</a>";
                    if(data.testResultAnnounced=="true")
                        cStr += "<a href='javascript:getRankDetailsForTestSeries(" + data.id + ")' class='btn watch'>Show Rank</a>";
                    else if(data.testResultDate){
                        cStr += "<a class='testStarts btn watch'>Results on"+
                            "<p class='ml-1'>"+
                            "<p class='testDate '>"+moment.utc(data.testResultDate).format("D MMM YYYY")+"</p>"+
                            "<p class='seperator mr-1 ml-1'>|</p>"+
                            "<p class='testTime'>"+moment.utc(data.testResultDate).format("h:mm a")+"</p>"+
                            "</p>"+
                            "</a>";
                    }
                }else{
                    cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType="+"&source=web"+"&siteName="+"${session['siteName']}"+"&learn=false&pubDesk=false&dailyTest=false' target='_blank' class='btn watch p-0'><span>Play</span></a>";
                    cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=practice"+"&source=web"+"&siteName="+"${session['siteName']}"+"&learn=false&pubDesk=false&dailyTest=false' target='_blank' class='btn watch p-0'><span>Practice</span></a>";
                    cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=testSeries"+"&source=web"+"&siteName="+"${session['siteName']}"+"&learn=false&pubDesk=false&dailyTest=false' target='_blank'  class='btn watch p-0'><span>Test</span></a>";
                    <sec:ifNotLoggedIn>
                    cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' target='_blank' class='btn listen p-0'><span>Study</span></a>";
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>
                    cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' target='_blank' class='btn watch p-0'><span>Study</span></a>";
                    cStr += "<a href='/prepjoy/history?siteId=" + "${session['siteId']}" + "&resId=" + data.id + "&quizType=regular' class='btn listen' target='_blank'><span>History</span></a>";
                    </sec:ifLoggedIn>
                }
                cStr += "</div>";
            }
            if(data.resType=="Videos" || data.resType=="Reference Videos" && !data.resLink.includes("drive.google.com")) {
                if (data.resLink == "") {
                    cStr += "<div class='d-flex p-2 justify-content-center text-danger bg-light w-100 rounded'><span style='font-size:13px;'>Video is available only on app.</span></div>";
                } else {
                    cStr += "<div class='d-flex w-100'>";
                    if ("video" == data.quizMode) {
                        cStr += "<a href='javascript:playS3Video(" + data.id + ",\"" + data.resLink + "\")' id='resAction_" + data.id + "' class='btn watch'><span>Watch Now</span></a>";

                    } else {
                        cStr += "<a href='javascript:playVideo(\"" + data.resLink + "\"," + data.id + ")' id='resAction_" + data.id + "' class='btn watch'><span>Watch Now</span></a>";
                    }
                    if(!data.resLink.includes("vimeo.com")) {
                        cStr += "<a href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ",\""+data.resName.replace(/'/g, "&#39;")+"\")' id='audioPlayButton_" + data.id + "'  class='btn listen'><span>Listen Now</span></a>" ;

                    }
                    cStr +=   "</div>";
                }

            }
            cStr +=  "</div>"+
                "    </div>\n";
            cStr += "</div>";

        }

        cStr +="</div></div>";

        if(testSeriesBook&&hasMCQs) {
            cStr += "<div class=\"container\">\n" +

                "<div class=\"all-container tests-access position-relative\"><h6>Test Series</h6>";
            if(!boughtTestSeries&&!previewChapter){
                cStr +=" <div class=\"buy unlock-tests\">\n" +
                            "<div class='unlock-info'>" +
                                "<i class='material-icons-round lock-icon'>lock</i>" +
                                "<h5>Online Test Series</h5>\n" +
                                "<p>@ just</p>" +
                                "<div class=\"unlock-price\">\n" +
                                    "<h3><span>&#x20b9</span> "+Math.floor(${upgradePrice!=null?upgradePrice:0})+"</p>\n" +
                                "</div>\n" +
                                "<a class=\"btn-book-buy mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mt-2\" href=\"javascript:addToCartFromDtl('${book.id}','upgrade')\">Unlock</a>\n" +
                            "</div>"+
                        "</div>";
                for (var i = 0; i < 3; ++i) {
                    cStr += "<div class='container-wrapper'>" +
                        "<div class='d-flex justify-content-between align-items-center'>" +
                        "<div class='media'>" +
                        "<a class='mb-0 readnow'>" +
                        "<div class='box violet'>" +
                        "<div><i class='align-self-center'></i><p>MCQ</p></div>" +
                        "</div>" +
                        "<div class='media-body'><p class='title'>Multiple Choice Questions</p></div>" +
                        "</a>" +
                        "<div class='d-flex w-100'>" +
                        "<a href='javascript:void(0);' class='btn watch p-0'><span>Play</span></a>" +
                        "<a href='javascript:void(0);' class='btn watch p-0'><span>Practice</span></a>" +
                        "<a href='javascript:void(0);' class='btn watch p-0'><span>Test</span></a>" +
                        "<a href='javascript:void(0);' class='btn watch p-0'><span>Study</span></a>" +
                        "<a href='javascript:void(0);' class='btn listen'><span>History</span></a>" +
                        "</div> " +
                        "</div> </div>" +
                        "</div>";
                }
            } else {
                for (var i = 0; i < cData.length; ++i) {
                    var showShare = false;
                    var data = cData[i];

                    if (data.resType == "Multiple Choice Questions") {

                        cStr += "<div class=\"container-wrapper\" id='res_" + i + "'>\n" +
                            "<div class='d-flex justify-content-between align-items-center'>";

                        cStr += "        <div class=\"media\">\n";

                        cStr += "                    <a class=\"mb-0 readnow\">";
                        cStr += "   <div class='box violet'>" +
                            "<div>" +
                            " <i class=\"align-self-center\">\n" +
                            "                \n" +
                            "            </i>\n" +
                            "<p>MCQ</p>" +
                            "</div>" +
                            "</div>";
                        cStr += "            <div class=\"media-body\">\n" +
                            "                <p class=\"title\">" + data.resName.replace(/'/g, "&#39;") + "</p>\n";
                        cStr += "            </div>\n" +
                            "</a>";

                        cStr += "<div class='d-flex w-100'>";
                        if (data.testStartDate) {
                            testSeriesQuiz = true;
                            var displayName = "Take test";
                            var testAlreadyTaken = false;
                            if (seenResources.includes("," + data.id + ",")) {
                                testAlreadyTaken = true;
                                displayName = "Re attempt";
                            }

                            if ("true" == data.testEnded) cStr += "<a class='testStarts btn watch'><span>Test already ended</span></a>";
                            else if ("true" == data.testStarted) {
                                cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.resLink + "&resId=" + data.id + "' class='btn watch'><span>" + displayName + "</span></a>";

                            } else cStr += "<a class='testStarts btn watch'><span class='d-flex align-items-center' style='gap: 10px'>Test Starts On" +
                                "<p>" +
                                "<p class='testDate'>" + moment.utc(data.testStartDate).format("D MMM YYYY") + "</p>" +
                                "<p class='seperator'>|</p>" +
                                "<p class='testTime'>" + moment.utc(data.testStartDate).format("h:mm a") + "</p>" +
                                "</p>" +
                                "</span>" +
                                "</a>";
                            if (data.testResultAnnounced == "true")
                                cStr += "<a href='javascript:getRankDetailsForTestSeries(" + data.id + ")' class='btn watch'>Show Rank</a>";
                            else if (data.testResultDate) {
                                cStr += "<a class='testStarts btn watch'>Results on" +
                                    "<p class='ml-1'>" +
                                    "<p class='testDate '>" + moment.utc(data.testResultDate).format("D MMM YYYY") + "</p>" +
                                    "<p class='seperator mr-1 ml-1'>|</p>" +
                                    "<p class='testTime'>" + moment.utc(data.testResultDate).format("h:mm a") + "</p>" +
                                    "</p>" +
                                    "</a>";
                            }
                        } else {
                            cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=" + "&source=web" + "&siteName=" + "${session['siteName']}" + "&learn=false&pubDesk=false&dailyTest=false' target='_blank' class='btn watch p-0'><span>Play</span></a>";
                            cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=practice" + "&source=web" + "&siteName=" + "${session['siteName']}" + "&learn=false&pubDesk=false&dailyTest=false' target='_blank' class='btn watch p-0'><span>Practice</span></a>";
                            cStr += "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=testSeries" + "&source=web" + "&siteName=" + "${session['siteName']}" + "&learn=false&pubDesk=false&dailyTest=false' target='_blank'  class='btn watch p-0'><span>Test</span></a>";
                            <sec:ifNotLoggedIn>
                            cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' target='_blank' class='btn listen p-0'><span>Study</span></a>";
                            </sec:ifNotLoggedIn>
                            <sec:ifLoggedIn>
                            cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' target='_blank' class='btn watch p-0'><span>Study</span></a>";
                            cStr += "<a href='/prepjoy/history?siteId=" + "${session['siteId']}" + "&resId=" + data.id + "&quizType=regular' class='btn listen' target='_blank'><span>History</span></a>";
                            </sec:ifLoggedIn>
                            }
                        cStr += "</div>";

                        cStr += "</div>" +
                            "    </div>\n";
                        cStr += "</div>";

                    }

                }
            }
            cStr +="</div></div>";
        }

        if(suggestedVideos.length > 0) {
            cStr += "<div class=\"container\">\n" +

                "<div class=\"all-container\">";
            cStr += "<div class='suggested-videos mt-4 pt-2'><h6>Suggested Videos</h6>";
            for (let j = 0; j < suggestedVideos.length; ++j) {
                let videos = suggestedVideos[j];
                let imgSrc = "https://i.ytimg.com/vi/" + videos.videoId + "/mqdefault.jpg";
                cStr += "<div class='video-wrapper'>" +
                            "<div class='d-flex'>" +
                                "<a href='javascript:playVideo(\"" + videos.videoId + "\"," + 1 + ")'>" +
                                    "<div class='video-img-wrapper'>" +
                                        "<img src='" + imgSrc + "' class='video-img' alt=''/>" +
                                    "</div>" +
                                    "<p>" + videos.videoTitle + "</p>" +
                                "</a>" +
                            "</div>" +
                        "</div>";
            }
            cStr += "</div>";
            cStr +="</div></div>";
        }

        document.getElementById('content-data-all').innerHTML = cStr;
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }

        // Removing tests href link
        // if(!boughtTestSeries) {
        //     const testLinksSelector = document.querySelectorAll('.tests-access .container-wrapper a');
        //     for (let k = 0; k < testLinksSelector.length; ++k) {
        //         testLinksSelector[k].setAttribute('href', 'javascript:void(0);');
        //     }
        // }

        // Removing empty resources
        const emptyDivs = document.querySelectorAll('.ebook-access .container-wrapper .media');

        for (let l = 0; l < emptyDivs.length; ++l) {
            if(emptyDivs[l].firstElementChild == null) {
                emptyDivs[l].parentElement.parentElement.remove();
            }
        }

        const ebookResources = document.querySelectorAll('.ebook-access .container-wrapper');
        if (ebookResources.length == 0) {
            document.querySelector('.ebook-access h6').classList.add("d-none");
        }

        //add adding video thingy

        $("#content-data-all").show();

        //  hideTextFormatter("something");
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }

        if(paramResId>-1){
            openResource();
        }
        if($(window).width() < 768) {
            if (fromNotes) {
                $('.resource-contents > .tab-content,.add-tabs').removeClass('d-none');
                $('.side-chapters').addClass('d-none');
                $('#content-data-all').show().removeClass('d-none');
                $('.epub-action').hide();
            }
        }


        document.querySelector('.resourcePageShimmer').classList.add('d-none');
        document.body.style.overflow = 'auto';
        document.querySelector('.side-chapters').classList.remove('d-none');
        document.querySelector('.side-chapters').classList.add('d-lg-flex');
        document.querySelector('.resource-contents').classList.remove('d-none');
        var lastReadPDF = localStorage.getItem('lastReadPDF');

        if (lastReadPDF!=null && lastReadPDF!=undefined && lastReadPDF!=""){
            lastReadPDF = JSON.parse(lastReadPDF)
        }
    }

    function openResource(){
        if($(window).width() < 768) {
            $('.resource-contents > .tab-content').removeClass('d-none');
            $('.side-chapters').addClass('d-none');
            $('#content-data-all').show().removeClass('d-none');
        }
        if(resViewedFrom=="audios"){
        document.getElementById("audioPlayButton_"+paramResId).click();
        } else {
        document.getElementById("resAction_"+paramResId).click();
        }
    }
</script>
<script type="text/javascript" src="https://www.youtube.com/iframe_api"></script>

<script>
    function playAudiOnly(videoLink,id,name) {
        if (videoLink.includes("drive.google.com")){
            let formattedAudioLink;
            formattedAudioLink = "https://docs.google.com/uc?export=open&id="+videoLink.split("d/")[1].split('/')[0];
            track_list.push({
                name:name,
                artist:"",
                image:"",
                path:formattedAudioLink
            });
            loadTrack(track_index);
            playTrack();
            $('#listenCAModal').modal('show');
        }else{
            var htmlStr = "<div style=\"display:flex;justify-content:center;align-items:center;\">\n" +
                "    <div>\n" +
                "        <div data-video=\""+videoLink+"\" data-autoplay=\"0\" data-loop=\"1\" data-controls='1' id=\"youtube-audio\"></div>\n" +
                "        <div style=\"clear:both;margin:10px;text-align:center\">\n" +
                // "            <p>The audio player is created with the YouTube API.</p>\n" +
                // "            <p>Read Tutorial: <a href=\"http://www.labnol.org/internet/youtube-audio-player/26740/\">YouTube Audio Player</a></p>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</div>\n"
            $("#LoadingAudio").html(htmlStr);
            $("#PlayAudiOnlyModal").modal('show');
            onYouTubeIframeAPIReady();
            if(loggedInUser) updateUserView(id,"all","audios");
            else updateView(id,"all","audios");
        }
    }

    function onYouTubeIframeAPIReady() {

        var ctrlq = document.getElementById("youtube-audio");
        if(!ctrlq){
            return;
        }

        ctrlq.innerHTML = '<i class="material-icons-round" id="youtube-icon" style="font-size: 3rem"></i><div id="youtube-player"></div>';
        ctrlq.style.cssText = 'margin:1.4em auto;cursor:pointer;cursor:hand;display:none';
        ctrlq.onclick = toggleAudio;

        player = new YT.Player('youtube-player', {
            height: '0',
            width: '0',
            videoId: ctrlq.dataset.video,
            playerVars: {
                autoplay: ctrlq.dataset.autoplay,
                loop: ctrlq.dataset.loop,
                controls: '1',
            },
            events: {
                'onReady': onPlayerReady,
                'onStateChange': onPlayerStateChange
            }
        });

    }

    function togglePlayButton(play) {
        var iconElement = document.getElementById("youtube-icon");
        play ? iconElement.innerHTML = 'pause' : iconElement.innerHTML = 'play_arrow';
    }

    function toggleAudio() {
        if ( player.getPlayerState() == 1 || player.getPlayerState() == 3 ) {
            player.pauseVideo();
            togglePlayButton(false);
        } else {
            player.playVideo();
            togglePlayButton(true);
        }
    }

    function onPlayerReady(event) {
        player.setPlaybackQuality("small");
        document.getElementById("youtube-audio").style.display = "block";
        togglePlayButton(player.getPlayerState() !== 5);
    }

    function onPlayerStateChange(event) {
        if (event.data === 0) {
            togglePlayButton(false);
        }
    }

    $(document).ready(function () {
        $("#PlayAudiOnlyModal").click(function(ev){
            if(ev.target != this) return;
            $('#PlayAudiOnlyModal').modal('hide');
            // document.getElementById('PlayingAudiOnly').pause();
            player.pauseVideo();
            togglePlayButton(false);
        });
    });



</script>

<script>
    <sec:ifLoggedIn>
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }

    function updateUserViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateUserView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }




    </sec:ifLoggedIn>

    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }

    function initCallGoogle(id,loadedType){
        var bookTitle = "${bookName}";
        var seoFriendlyTitle = "${title}";
        <%if("books".equals(grailsApplication.config.grails.appServer.default)&&"1".equals(""+session["siteId"])) {%>
        var currentUrl = window.location.href;
        //add the resId
        if(currentUrl.indexOf("resId=")==-1) {
            if(!currentUrl.includes("passUrl")) currentUrl = currentUrl + "&resId=" + id;
        } else{
            if(!currentUrl.includes("passUrl")) currentUrl = currentUrl.substr(0,currentUrl.indexOf("&resId"))+"&resId="+id;
        }
        history.pushState({
            id: 'homepage'
        }, '', currentUrl);
        if(seoFriendlyTitle.indexOf("Wonderslate")>0) {
            var wonderslateIndex =   seoFriendlyTitle.indexOf(" - Wonderslate");
            seoFriendlyTitle = seoFriendlyTitle.substr(0,wonderslateIndex)+": "+loadedType+" - Wonderslate";
            document.title = seoFriendlyTitle;
        }

        callGoogle(window.location.pathname+"/"+window.location.search);

        <%}
       %>

    }



    function openResourceScreen(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        $("#htmlreadingcontent").show();
        $("#content-data-all").hide();

    }

    function setBackButton(){
        var backString ="<a href='javascript:backToAll()' class='read-back-btn pr-3'><div id='backtoMain' class=\"icon material-icons\">\n" +
            "keyboard_backspace\n" +
            "</div>" +
            "<div class=\"mdl-tooltip\" data-mdl-for=\"backtoMain\">\n" +
            "Back to <strong>Main</strong>\n" +
            "</div>"+
            "</a>";

        return backString;
    }
    function getRankDetailsForTestSeries(resId){
        <g:remoteFunction controller="pubdesk" action="getRankDetails" onSuccess='displayRankDetails(data)'
                params="'testSeriesBook=true&resId='+resId" />
    }
    function displayRankDetails(data){
        var htmlStr="";
        if("ok"==data.status){
            var rankDetails = data.testRanks;
            var noOfRanks=0;
            if(rankDetails.length>10) noOfRanks=10;
            else noOfRanks=rankDetails.length;
            for(i=0;i<noOfRanks;i++){
                htmlStr +="<tr>\n" +
                    "                        <td>"+rankDetails[i].rank+"</td>\n" +
                    "                        <td>"+rankDetails[i].name+"</td>\n" +
                    "                        <td>"+ parseFloat(""+rankDetails[i].marks).toFixed(2) +"</td>\n" +
                    "                    </tr>";
            }


        } else {
            htmlStr="<tr><td colspan='3'>Results are not yet announced</td></tr>";
        }

        document.getElementById("rankDetails").innerHTML=htmlStr;
        document.getElementById("totalParticipants").innerHTML = "Attempted by <b>" + (data.totalParticipants==null||data.totalParticipants=="null"?"0":data.totalParticipants)+ "</b> students";
        if(data.userRank!=null&&!data.userRank=="") {
            document.getElementById("userRank").innerHTML = data.userRank==null||data.userRank=="null"?"-NA-":data.userRank;
            document.getElementById("userScore").innerHTML = "<span>" + parseFloat(""+data.userScore).toFixed(2) +"</span>";
            if(data.userAnswers!=null&&!(""==data.userAnswers)&&!("null"==data.userAnswers)){
                document.getElementById("detailedResults").innerHTML="<a href='/funlearn/quiz?fromMode=library&quizMode=results&resId="+data.resId+"&quizRecorderId="+data.quizRecorderId+"'>Show Detailed Result</a>";
            }

        }
        $('#rank-dialog').modal('show');
    }
    function filterByType(field){
        for(i=0;i<chapterDetailsData.length;i++){
            if(field.selectedIndex==0) $("#res_"+i).show();
            else{
                if(field[field.selectedIndex].value==chapterDetailsData[i].resType||field[field.selectedIndex].value==chapterDetailsData[i].resType) $("#res_"+i).show();
                else $("#res_"+i).hide();
            }
        }
    }

    //PR
    var addToDoId;
    var batchId="${params.batchId}";
    var noOfToPendingItems=0;
    <%if(session.getAttribute("userPendingTodoCount")!=null&&!"0".equals(session.getAttribute("userPendingTodoCount"))){%>
    noOfToPendingItems=parseInt("${session.getAttribute("userPendingTodoCount")}");
    <%}%>
    function addToDo(resId) {
        addToDoId=resId;
        <g:remoteFunction controller="ToDo" action="addToDoTask" params="'resId='+resId+'&batchId='+batchId" onSuccess='addToDoSuccess();' />
    }
    function addToDoSuccess() {
        $('#btn'+addToDoId).addClass('btn-success').find('.material-icons').text('check');
        $('#btn'+addToDoId).find('span:last-child').text('Added');
        noOfToPendingItems = noOfToPendingItems+1;
        document.getElementById("todoLink").innerHTML=" <small>"+noOfToPendingItems+"</small>";
        $('#addedTodo').modal('show');

    }

    function playHostedMediaVideo(id){
        var video = document.getElementById('hostedVideo');
        video.src='/funlearn/getMedia?id='+id;

        video.play();
        $("#hostedVideoModal").modal('show');
        if(loggedInUser) updateUserView(id,"all","uploaded_video");
        else updateView(id,"all","uploaded_video");
    }
    function playHostedMediaAudio(id){
        var audio = document.getElementById('hostedAudio');
        audio.src= '/funlearn/getMedia?id='+id;
        audio.play();
        $("#hostedAudioModal").modal('show');
        if(loggedInUser) updateUserView(id,"all","uploaded_audio");
        else updateView(id,"all","uploaded_audio");
    }

    $("#videoClose").click(function () {
        var vid = document.getElementById("hostedVideo");
        vid.pause();
    });
    $("#audioClose").click(function () {
        var aid = document.getElementById("hostedAudio");
        aid.pause();
    });

    function closeResourceScreen(){
        $("#content-data-all").show();
        $("#htmlreadingcontent").hide();
        $('#allAddButton').show();
        $('.epub-action').hide();
        $('.mobile-back-button,.wl-back-button').attr('onclick','mobileBackToChapters()');
        $('.mdl-js-layout,.add-tabs').show();
    }
    function backToAll(){
        $('.nav-tabs a[href="#all"]').tab('show');
        $('.nav-tabs a[href="#allchapters"]').tab('show');
        $('#content-data-all').show();
        $('#htmlreadingcontent').hide();
        $('.mobile-back-button,.wl-back-button').attr('onclick','mobileBackToChapters()');
        $('.epub-action').hide();
        $('.nav-tabs li:last-child a').removeClass('is-active');
        $('.nav-tabs li:first-child a').addClass('is-active');
        $('#successModal').modal('hide');
    }
    function mobileBackToChapters(){
        $('.mobile-back-button,.wl-back-button').attr('onclick','history.back()');
        $('.resource-contents > .tab-content,.add-tabs').addClass('d-none');
        $('.side-chapters').removeClass('d-none');
        $('.ebookChapters').addClass('d-none').removeClass('d-block');
        $('.nav-tabs a[href="#allchapters"]').tab('show');
        $('.mdl-tabs.side-column li:last-child a').removeClass('is-active');
        $('.mdl-tabs.side-column li:first-child a').addClass('is-active');
        $('.related-book-wrapper').hide();
    }

    function displayResource(data){
        var lastReadPDF = localStorage.getItem('lastReadPDF');
        chapterIdForPDF = data.chapterId;

        var chpList =  document.querySelectorAll('.read-book-chapters-wrapper li');

        chpList.forEach(item=>{
            item.classList.remove('orangeText');
        });
        document.getElementById("chapterName"+chapterIdForPDF).classList.add('orangeText');

        if (lastReadPDF!=null && lastReadPDF!=undefined && lastReadPDF!=""){
            lastReadPDF = JSON.parse(lastReadPDF)
        }
        var response = data.results;
        chapterResponse = response;
        var notesCount=0;
        var notesObjArr = [];
        var chapterId = data.chapterId;
        response.map(item=>{
            if(item.resType=='Notes'){
                notesCount++;
                notesObjArr.push(item);
            }
        });
        if (notesCount===1){
            notesObjArr.map(item=>{
                if(item.sharing==null) {
                    if (item.resLink.includes(".pdf")) {
                        displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName);
                    } else {
                        displayReadingMaterial(item.id);
                    }
                }else{
                    if (item.quizMode == "file") {
                        if (item.resLink.includes(".pdf")) {
                            displayPDFNotes(item.id,item.resName);
                        }else {
                            downloadFile(item.id);
                        }
                    } else {
                        displayReadingMaterial(item.id);
                    }
                }

                document.querySelector('.resourcePageShimmer').classList.add('d-none');
                document.body.style.overflow = 'auto';
                setTimeout(function (){
                    if ($(window).width() < 768){
                        document.querySelector('.side-chapters').classList.add('d-none');
                        document.querySelector('.resource-contents .tab-content').classList.remove('d-none');
                    }else{
                        document.querySelector('.side-chapters').classList.remove('d-none');
                    }
                    document.querySelector('.resource-contents').classList.remove('d-none');
                },1000);
            })
        }else if(notesCount>1){
            notesObjArr.map(item=>{
                if (lastReadPDF !=null){
                    if (item.id == lastReadPDF.resId){
                        if(item.sharing==null) {
                            if (item.resLink.includes(".pdf")) {
                                displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName);
                            } else {
                                displayReadingMaterial(item.id,item.resName);
                            }
                        }else{
                            if (item.quizMode == "file") {
                                if (item.resLink.includes(".pdf")) {
                                    displayPDFNotes(item.id,item.resName);
                                }else {
                                    downloadFile(item.id);
                                }
                            } else {
                                displayReadingMaterial(item.id);
                            }
                        }
                    }
                }
            });
            document.querySelector('.resourcePageShimmer').classList.add('d-none');
            document.body.style.overflow = 'auto';
            if ($(window).width() < 768){
                if (showResourceList){
                    document.querySelector('.side-chapters').classList.add('d-none');
                    document.querySelector('.resource-contents .tab-content').classList.remove('d-none');
                }
            }else{
                document.querySelector('.side-chapters').classList.remove('d-none');
            }
            document.querySelector('.resource-contents').classList.remove('d-none');
        }else if (notesCount<1){
            if (showResourceList){
                if ($(window).width() < 768){
                    if (showResourceList){
                        document.querySelector('.side-chapters').classList.add('d-none');
                        document.querySelector('.resource-contents .tab-content').classList.remove('d-none');
                    }
                }
            }
        }
    }


    function loadTrack(track_index) {
        clearInterval(updateTimer);
        resetValues();
        curr_track.src = track_list[track_index].path;
        curr_track.load();

        track_art.style.backgroundImage =
            "url(" + track_list[track_index].image + ")";
        track_name.textContent = track_list[track_index].name;
        track_artist.textContent = track_list[track_index].artist;
        now_playing.textContent =
            "PLAYING " + (track_index + 1) + " OF " + track_list.length;
        updateTimer = setInterval(seekUpdate, 1000);

        document.querySelector('.track-art').innerHTML = "<p><img src='/assets/wonderslate/cd_dark.svg' width='150' class='rotateInfinite'/></p>";


        curr_track.addEventListener("ended", nextTrack);
        random_bg_color();
    }

    function random_bg_color() {
        let red = Math.floor(Math.random() * 256) + 64;
        let green = Math.floor(Math.random() * 256) + 64;
        let blue = Math.floor(Math.random() * 256) + 64;

        let bgColor = "rgb(" + red + ", " + green + ", " + blue + ")";

        // document.body.style.background = bgColor;
    }

    function resetValues() {
        curr_time.textContent = "00:00";
        total_duration.textContent = "00:00";
        seek_slider.value = 0;
    }

    function playpauseTrack() {
        if (!isPlaying) playTrack();
        else pauseTrack();
    }

    function playTrack() {
        curr_track.play();
        isPlaying = true;
        playpause_btn.innerHTML = '<i class="fa fa-pause-circle fa-4x"></i>';
    }

    function pauseTrack() {
        curr_track.pause();
        isPlaying = false;
        playpause_btn.innerHTML = '<i class="fa fa-play-circle fa-4x"></i>';
    }

    function nextTrack() {
        if (track_index < track_list.length - 1)
            track_index += 1;
        else track_index = 0;
        loadTrack(track_index);
        playTrack();
    }

    function prevTrack() {
        if (track_index > 0)
            track_index -= 1;
        else track_index = track_list.length - 1;
        loadTrack(track_index);
        playTrack();
    }
    function seekTo() {
        seekto = curr_track.duration * (seek_slider.value / 100);
        curr_track.currentTime = seekto;
    }

    function setVolume() {
        curr_track.volume = volume_slider.value / 100;
    }

    function seekUpdate() {
        let seekPosition = 0;
        if (!isNaN(curr_track.duration)) {
            seekPosition = curr_track.currentTime * (100 / curr_track.duration);
            seek_slider.value = seekPosition;

            let currentMinutes = Math.floor(curr_track.currentTime / 60);
            let currentSeconds = Math.floor(curr_track.currentTime - currentMinutes * 60);
            let durationMinutes = Math.floor(curr_track.duration / 60);
            let durationSeconds = Math.floor(curr_track.duration - durationMinutes * 60);

            if (currentSeconds < 10) { currentSeconds = "0" + currentSeconds; }
            if (durationSeconds < 10) { durationSeconds = "0" + durationSeconds; }
            if (currentMinutes < 10) { currentMinutes = "0" + currentMinutes; }
            if (durationMinutes < 10) { durationMinutes = "0" + durationMinutes; }
            curr_time.textContent = currentMinutes + ":" + currentSeconds;
            total_duration.textContent = durationMinutes + ":" + durationSeconds;
        }
    }

    document.querySelector('.audioCloseBtn').addEventListener('click',function (){
        pauseTrack();
        track_list = [];
    })
</script>
