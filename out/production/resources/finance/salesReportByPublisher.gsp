<%@ page import="java.text.SimpleDateFormat" %>


<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
%{--<asset:javascript src="moment.min.js"/>--}%

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    var loggedIn=false;
</script>
<style>

.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}

.form-group .btn-group {
    width: 100%; }
.form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000;
    font-weight: normal; }
.form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
.form-group .btn-group .multiselect-container li {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.form-group .btn-group .multiselect-container a {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">
                        Report for Publisher Sales</h3>

                    <div class="d-flex align-items-center">
                        <div class="form-group">
                            <label for="startDate">From Date</label><br>
                            <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date"   autocomplete="off" >
                        </div>

                        <div class="form-group ml-4">
                            <label for="endDate">To Date</label><br>
                            <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date"  autocomplete="off" >
                        </div>
                        <% if(session["userdetails"].publisherId==null) {%>
                        <div class="form-group col-md-3">
                            <label for="publisherId"><strong>Publisher</strong></label>
                            <g:select id="publisherId" class="form-control w-100" optionKey="id" optionValue="name"
                                      value="${publisherId!=null?publisherId:""}" name="publisherId" from="${publishers}" noSelection="['':'All']"/>
                            <small class="text-muted text-danger" id=publisherIdalert" style="display: none">
                                Select the publisher
                            </small>
                        </div>
                        <%  } %>
                    </div>

                </div>
                <button class="btn btn-primary"  onclick="submitUsageReport()">Submit</button>
                <div style="margin-top: 10px;">
                    <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                    <div id="successmsg" style="display: none"></div>
                    <div id="publisherCalculation" style="display: none">
                    </div>
                    <div style="float: right; display: none;" id="download">
                        <div class="form-group">
                            <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                        </div>
                    </div>
                    <div id="batchUsers" style="display: none"></div>
                </div>

            </div>


        </div>
    </div>



</div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>


<script>

    const siteId1 = "${session["siteId"]}";
    var eBookSalesWS=0;
    var eBookSalesWhiteLabel=0;
    var rechargeSales=0;
    var printbookSales=0;
    var totalDeliveryCosts=0;
    var publisherOnWS=70;
    var publisherOnWhiteLabel=70;
    var publisherPrintbook=90;


    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });




    function submitUsageReport() {

        var StartDate = document.getElementById("startDate").value
        var EndDate = document.getElementById("endDate").value
        var inValidStartDate = false;
        if (document.getElementById("startDate").value != "" &&  document.getElementById("endDate").value != ""&& document.getElementById("publisherId").selectedIndex >0){
            var  startDate1 = new Date(StartDate.split('-')[2],StartDate.split('-')[1],StartDate.split('-')[0]);
            var  endDate1 = new Date(EndDate.split('-')[2],EndDate.split('-')[1],EndDate.split('-')[0]);
            if(endDate1.getTime() < startDate1.getTime()) inValidStartDate = true;
        }
        if (document.getElementById("startDate").value === "" || document.getElementById("endDate").value === ""|| document.getElementById("publisherId").selectedIndex ==0) {
            document.getElementById("errormsg").innerHTML = "Please Select From Date, To Date & Publisher."
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
            return
        }

        if (inValidStartDate){
            document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
            $("#publisherCalculation").hide();


        } else {
            $("#errormsg").hide();
            $('#download').hide();
            $("#publisherCalculation").hide();
            eBookSalesWS=0;
            eBookSalesWhiteLabel=0;
            rechargeSales=0;
            printbookSales=0;
            totalDeliveryCosts=0
            var publisherId = document.getElementById("publisherId").value;
            <g:remoteFunction controller="finance" action="salesReportByPublisher" params="'startDate='+StartDate+'&mode=submit&endDate='+EndDate+'&publisherId='+publisherId" onSuccess = "showReports(data);"/>
        }
    }


    function showReports(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>PO No</th>\n" +
            "                            <th>Po Date</th>\n" +
            "                            <th>Payment Id</th>\n" +
            "                            <th>BookType</th>\n" +
            "                            <th>Amount</th>\n" +
            "                            <th>Delivery Charges</th>\n" +
            "                            <th>Title</th>\n" +
            "                            <th>Book Id</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.results;
            var cartMstId=0;
            var deliveryCost=0;
            publisherOnWS=data.ebookShareOnWS;
             publisherOnWhiteLabel=data.ebookShareOnWhitelabel;
             publisherPrintbook=data.printBookShare;
            for(var i=0;i<report.length;i++){
                if(cartMstId!=report[i].cart_mst_id) {
                    cartMstId = report[i].cart_mst_id;
                    deliveryCost = report[i].deliver_costs;
                    totalDeliveryCosts += deliveryCost;
                }else{
                    deliveryCost = 0;
                }
                if(report[i].book_type=="printbook"||report[i].book_type=="combo") {
                    printbookSales += report[i].amount;
                }else if(report[i].book_type=="recharge"){
                    rechargeSales += report[i].amount;
                }else{
                    if(report[i].site_id==1||report[i].site_id==2){
                        eBookSalesWS += report[i].amount;
                    }else{
                        eBookSalesWhiteLabel += report[i].amount;
                    }
                }
                htmlStr +="<tr><td>"+report[i].id+"</td>"+
                    "<td>"+report[i].date_created+"</td>" +
                    "<td>"+report[i].payment_id+"</td>" +
                    "<td>"+report[i].book_type+"</td>" +
                    "<td>"+report[i].amount+"</td>" +
                    "<td>"+deliveryCost+"</td>" +
                    "<td>"+report[i].title+"</td>" +
                    "<td>"+report[i].bookId+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').show();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
        }
        $("#batchUsers").show();


        //publisher calculation
        var publisherHtml ="<br><h5>Publisher Share</h5>" +
            "Printbook: " +publisherPrintbook+"%<br>" +
            "EBook on WS: " +publisherOnWS+"%<br>" +
            "EBook on WhiteLabel: " +publisherOnWhiteLabel+"%<br>" +
            "<br><h5>Calculation</h5>";
        publisherHtml += "<table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>Book Type</th>\n" +
            "                            <th>Amount</th>\n" +
            "                            <th>Razorpay</th>\n" +
            "                            <th>GST</th>\n" +
            "                            <th>Balance</th>\n" +
            "                            <th>Publisher Share</th>\n" +
            "                            <th>GST</th>\n" +
            "                            <th>Publisher Total</th>\n" +
            "                        </tr>\n" ;

        var razorpay=printbookSales*0.02;
        //round off to 2 decimal places
        razorpay = Math.round(razorpay * 100) / 100;
        var gst = 0;
        var balance = printbookSales-razorpay-gst;
        var publisherShare = balance*publisherPrintbook/100;
        //round off to 2 decimal places
        publisherShare = Math.round(publisherShare * 100) / 100;
        var publisherGST = 0;
        publisherHtml += "<tr><td>Printbook Sales</td><td>"+printbookSales+"</td>" +
            "<td>"+razorpay+"</td><td>"+gst+"</td><td>"+balance+"</td><td>"+publisherShare+"</td><td>"+publisherGST+"</td>" +
            "<td>"+(publisherShare+publisherGST)+"</td>"+
            "</tr>";

        publisherHtml += "<tr><td>Recharge Sales</td><td>"+rechargeSales+"</td></tr>";
        razorpay=eBookSalesWS*0.02;
        gst =eBookSalesWS*0.05;
        //round off to 2 decimal places
        razorpay = Math.round(razorpay * 100) / 100;
        gst = Math.round(gst * 100) / 100;
        balance = eBookSalesWS-razorpay-gst;
        publisherShare = balance*publisherOnWS/100;
        //round off to 2 decimal places
        publisherShare = Math.round(publisherShare * 100) / 100;
        publisherGST = publisherShare*0.05;
        //round off to 2 decimal places
        publisherGST = Math.round(publisherGST * 100) / 100;
        publisherHtml += "<tr><td>EBook Sales WS</td><td>"+eBookSalesWS+"</td>" +
            "<td>"+razorpay+"</td><td>"+gst+"</td><td>"+balance+"</td><td>"+publisherShare+"</td><td>"+publisherGST+"</td>" +
            "<td>"+(publisherShare+publisherGST)+"</td>"+
            "</tr>";
        razorpay=eBookSalesWhiteLabel*0.02;
        gst =eBookSalesWhiteLabel*0.05;
        //round off to 2 decimal places
        razorpay = Math.round(razorpay * 100) / 100;
        gst = Math.round(gst * 100) / 100;
        balance = eBookSalesWhiteLabel-razorpay-gst;
        publisherShare = balance*publisherOnWhiteLabel/100;
        //round off to 2 decimal places
        publisherShare = Math.round(publisherShare * 100) / 100;
        publisherGST = publisherShare*0.05;
        //round off to 2 decimal places
        publisherGST = Math.round(publisherGST * 100) / 100;
        publisherHtml += "<tr><td>EBook Sales WhiteLabel</td><td>"+eBookSalesWhiteLabel+"</td>" +
            "<td>"+razorpay+"</td><td>"+gst+"</td><td>"+balance+"</td><td>"+publisherShare+"</td><td>"+publisherGST+"</td>" +
            "<td>"+(publisherShare+publisherGST)+"</td>"+
            "</tr>";

        publisherHtml += "<tr><td>Total Delivery Costs</td><td>"+totalDeliveryCosts+"</td></tr>";
        publisherHtml += "</table>";
        document.getElementById("publisherCalculation").innerHTML= publisherHtml;
        $("#publisherCalculation").show();

    }

    $('#download-btn').on('click', function() {
        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value
        var publisherId = document.getElementById("publisherId").value;
        window.location.href = "/finance/salesReportByPublisher?mode=download&startDate="+startDate+"&endDate="+endDate+'&publisherId='+publisherId;
    });
</script>

</body>
</html>
