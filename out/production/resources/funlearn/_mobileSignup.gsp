<%  String siteName = session.getAttribute("siteName")!=null?session.getAttribute("siteName"):grailsApplication.config.grails.appServer.siteName; %>
<style>
.g-signin2{
    background: url('../../images/landingpageImages/ic_google.svg') no-repeat;
    background-position: 14px;
    font-size: 16px;
    color: #444444;
    font-family: 'Rubik', sans-serif;
    border: none;
    background-color: #ffffff;
    width: 296px;
    height: 48px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    position: relative;
    z-index: 999;
}
</style>
<%
    boolean mobileOnlyLogin = "eutkarsh".equals(session['entryController'])||"mobile".equals(""+session.getAttribute("loginType"))
    boolean wonderslateSite = "books".equals(session['entryController'])
%>
<div class="modal ss  fade" id="loginSignup" data-page="login-google" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <div class="login-back-btn">
                    <a id="back-btn"><i class="material-icons">arrow_back</i></a>
                </div>
                <h4 class="login">Login</h4>
                <h4 class="signup">Sign Up</h4>
                <h4 class="reset">Reset Password</h4>
                <h4 class="otp-header">OTP Verification</h4>
                <h4 class="mobileauth-header">Mobile Authentication</h4>
                <h4 class="verify">Verification Code</h4>
                <h4 class="set-paswd">Set Password</h4>
                <sec:ifLoggedIn>
                    <button type="button" onclick="location.href='/logoff'" class="close"  id="closeSignupModal"><i class="material-icons">close</i> </button>
                </sec:ifLoggedIn>
                <sec:ifNotLoggedIn>
                    <button type="button" class="close"  id="closeSignupModal"><i class="material-icons">close</i> </button>
                </sec:ifNotLoggedIn>

            </div>
            <!-- Modal body -->
            <div class="modal-body text-center mx-auto">
                <div class="loading-icon hidden">
                    <img src="${assetPath(src: 'landingpageImages/ajax-loader.gif')}">
                </div>
                <div class="sign-content">
                    <div class="signup-bg"></div>
                    <h1>Welcome to <%= siteName%></h1>
                    <p class="mt-3 bannerText">For Online Tests,Videos and Smart e-Books </p>

                    %{--                    <button type="button" class="sign-mobile mt-4" id="" onclick="javascript:signIn();">Sign in with Mobile Number</button>--}%
                    <div class="sign-in mt-3">
                        <form method="post" action="/login/authenticate" style="padding: 0 1rem;" name="signin">
                            <input type="hidden" name="username" value="">
                            <span class="input-login">
                                <input class="input-field input-field-login" type=<%= mobileOnlyLogin="number" %> id="number" name="username_temp" required>
                                <label class="input-label input-label-login input-label-login-color-1" for="email">
                                    <span class="input-label-content input-label-content-login">Your registered Mobile number<%= mobileOnlyLogin?"":"" %></span>
                                </label>
                            </span>
                            <span class="input-login">
                                <input class="input-field input-field-login" type="password" id="password" name="password" required>
                                <label class="input-label input-label-login input-label-login-color-1" for="password">
                                    <span class="input-label-content input-label-content-login">Enter Password</span>
                                </label>
                            </span>
                            <input type="button" class="btn btn-lg w-100 continue mt-2" value="Login" id="nrmlSignin" onclick="submitSignIn();">
                            <div id="loginFailed" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>


                            <% if(mobileOnlyLogin){%>
                            <p style="max-width:100%; margin-top: 0.5rem;"><a class="forgot-password mt-2" onclick="javascript:forgotPasswordMobile();">Forgot Password ?</a></p><span class="no-account">Dont have an account? </br><a onclick="javascript:continueSignin();">Sign Up Now/Sign In with OTP</a></span></p>
                            <%}else{%>
                            <p style="max-width:100%; margin-top: 0.5rem;"><a class="forgot-password mt-2" onclick="javascript:forgotPasswordMobile();">Forgot Password ?</a><span class="no-account">Dont have an account? <a onclick="javascript:continueSignin();">Sign up</a></span></p>
                            <%}%>
                        </form>
                    </div>
                    %{--                    <p class="no-account">Dont have an account? <a onclick="javascript:continueSignin();">Sign up</a> </p>--}%
                    <span class="error-msg" id="limit-signin"></span>


                    <div class="loginBtns">
                        <button type="button" class="sign-google mt-4" id="googleSignInButton" onclick="googleSignInCalled();">Sign in with Google</button>
                        <button type="button" class="sign-email mt-4" id="login">Sign in with Email</button>
                    </div>


                </div>
                <div class="login-content">
                    <form method="post" action="/login/authenticate">
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="email1" name="username" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="email">
                                <span class="input-label-content input-label-content-login">Email</span>
                            </label>
                        </span>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="password" id="password" name="password" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="password">
                                <span class="input-label-content input-label-content-login">Password</span>
                            </label>
                        </span>
                        <input type="submit" onclick="javascript:migrateSignin();" class="btn btn-lg continue mt-2" value="Continue">
                        <a id="forgot-paswd" class="forgot-password">Forgot Password ?</a>
                    </form>
                    <div class="email-error" id="signInError" style="display: none;">
                        <p class="email-error-text">All fields are mandatory.</p>
                    </div>
                </div>
                <div class="mobile-auth">
                    <p class="migrate-msg">For better experience we are moving
                    to <b>mobile autentication.</b> Get your
                    number verified.</p>

                    <span class="input-login">
                        <input class="input-field input-field-login" type="<%= mobileOnlyLogin?"number":"text" %>" name="mobile" id="migrateMobile" required >
                        <label class="input-label input-label-login input-label-login-color-1" for="migrateMobile">
                            <span class="input-label-content input-label-content-login">Enter your mobile number<%= mobileOnlyLogin?"":"" %></span>
                        </label>
                    </span>
                    <p style="font-size: 14px;">We’ll send an OTP to verify your phone number<%= mobileOnlyLogin?"":"" %></p>
                    <span class="error-msg" style="display: none;">Please enter 10 digit number<%= mobileOnlyLogin?"":" " %>.</span>
                    <span id="user-exist"></span>
                    <input type="button"  onclick="javascript:submitMigrateOTP();" class="btn btn-lg continue w-100 mt-2" value="GET OTP" id="migrateOtp">

                </div>
                <div class="verification-code">
                    <p>SMS verification code has been sent to</p>
                    <span id="sentMobileNumber"></span>
                    <div>
                        <span id="timer" style="font-size: 14px;color:#B72319;">
                            <span id="time">60</span>s
                        </span>
                    </div>
                    <span class="input-login">
                        <input class="input-field input-field-login mobile-input" type="number" name="signupOtp" id="signupOtp" required maxlength="10" minlength="10">
                        <label class="input-label input-label-login input-label-login-color-1" for="signupOtp">
                        </label>
                    </span>
                    <span id="otperror-msg" style="display: none;">OTP failed.OTP is Incorrect.</span>
                    <span id="otp-empty"></span>
                    <p id="resendotp" style="display: none;">Didn’t  recieve the OTP?<a href="javascript:submitMigrateOTP(true);">Resend</a> </p>
                    <input type="button" onclick="javascript:verifyOTP();" class="btn btn-lg continue w-100 mt-2" value="Continue" id="verifybtn">
                    <span class="error-msg" id="limit-signin-verify"></span>
                </div>
                <div class="set-password">
                    <span class="input-login">
                        <input class="input-field input-field-login" type="password" id="setPassword1" name="password" required>
                        <label class="input-label input-label-login input-label-login-color-1" for="setPassword1">
                            <span class="input-label-content input-label-content-login">New Password</span>
                        </label>
                    </span>
                    <span class="input-login">
                        <input class="input-field input-field-login" type="password" id="setPassword2" name="password" required>
                        <label class="input-label input-label-login input-label-login-color-1" for="setPassword2">
                            <span class="input-label-content input-label-content-login">Confirm Password</span>
                        </label>
                    </span>
                    <span id="passwd-error"></span>
                    <input type="button"  onclick="javascript:setPassword();" class="btn btn-lg continue w-100 mt-2" value="Continue">
                </div>
                <div class="new-user">
                    <h2>Your account is ready</h2>
                    <p>Tell us more about yourself</p>
                    <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="name" name="name" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="name">
                                <span class="input-label-content input-label-content-login">Your Name</span>
                            </label>
                        </span>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="password" id="signup-password" name="password" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="signup-password">
                                <span class="input-label-content input-label-content-login">Set Password</span>
                            </label>
                        </span>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="email" name="email">
                            <label class="input-label input-label-login input-label-login-color-1" for="email">
                                <span class="input-label-content input-label-content-login" id="newUserEmailMobile">Your Email</span>
                            </label>
                        </span>


                        <span class="input-login text-left">
                            State
                            <select name="state" id="stateSel" size="1" class="form-control">
                                <option value="" selected="selected">Select State</option>
                            </select>
                        </span>
                        <span class="input-login text-left">
                            District
                            <select name="district" id="districtSel" size="1" class="form-control">
                                <option value="" selected="selected">Select District</option>
                            </select>
                        </span>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="button" onclick="javascript:formSubmit();"   class="btn btn-lg continue w-100 mt-2" value="Done">
                    </g:form>
                </div>
                <div class="reset-password">
                    <p>To recieve a link to reset your password, please enter your <%=siteName%> account email address.</p>
                    <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text"  name="username" id="fPemail" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="email">
                                <span class="input-label-content input-label-content-login">Email</span>
                            </label>
                        </span>
                        <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset mt-2" value="Send reset link">
                    </g:form>
                </div>
                <div id="reset-password-completed">
                    <p>Password reset link sent.</p>
                    <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from  <%=siteName%> within a couple of minutes, please check your spam folder.</p>
                    <a id="back-login"><i class="material-icons">arrow_back</i>Back to Login</a>
                </div>
                <div id="reset-google-paswd">
                    <p>Password reset not possible!</p>
                    <p>We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
                    <a id="back-login"><i class="material-icons">arrow_back</i>Back to Login</a>
                </div>
                <div id="account-exists">
                    <p class="notRegister">This Email is not registered.<br> Please signup!</p>
                    <a id="back-signup"><i class="material-icons">arrow_back</i>Back to Signup</a>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <p class="terms">By continuing you agree to our &nbsp;<a href="/smartebook/termsandconditions">Terms and Conditions.</a></p>
            </div>

        </div>
    </div>
</div>

<asset:javascript src="jquery-1.11.2.min.js"/>

<script>
    <sec:ifLoggedIn>
    <%  if(otpReg && session['userdetails']!=null && (session['userdetails'].otpFinished==null || session['userdetails'].otpFinished=="false")) { %>
    $(window).on('load',function() {
        if(elementExists('loginSignup')) {
            operation="migrate";
            $('#loginSignup').attr('data-page', 'mobile-auth');
            $('#migrateMobile').val('${session['userdetails'].mobile}');
            $('#loginSignup').modal('show');
        }
    });
    <%  } %>
    </sec:ifLoggedIn>
    var otpReg = "${otpReg}";

    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    var operation;
    var mobileNumber;
    function loginOpen(){
        $('#loginSignup').modal('show');
        document.getElementById("migrateMobile").value="";
        document.getElementById('limit-signin-verify').innerText="";
        $('#user-exist').html('');
        $('#otp-empty').html('');
        $('#passwd-error').html('');
        $('#otperror-msg').hide();
        $('.mobile-auth .error-msg').hide();
    }
    function signUpOpen() {
        $('#loginSignup').modal('show');
        document.getElementById("migrateMobile").value="";
        document.getElementById('limit-signin-verify').innerText="";
        $('#user-exist').html('');
        $('#otp-empty').html('');
        $('#passwd-error').html('');
        $('#otperror-msg').hide();
        $('.mobile-auth .error-msg').hide();
        continueSignin();
    }
    function loginClose() {
        $('#loginSignup').modal('hide');
    }
    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function formSubmit() {
        document.adduser.username.value = mobileNumber;
        if(mobileNumber.includes("@")) {
            document.adduser.mobile.value = document.adduser.email.value;
            document.adduser.email.value=mobileNumber;
        }else{
            document.adduser.mobile.value=mobileNumber;
        }
        <%if(wonderslateSite){%>
         var oData = new FormData(document.forms.namedItem("adduser"));
        oData.append("site","Wonderslate");

        var url = "${createLink(controller:'creation',action:'addUser')}";
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            }
        });
        <%}else{%>
        document.adduser.submit();
        <%}%>



    }






    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                return false;
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-completed');
            $('#fp-user-email').html("“"+userEmail+"”");
        } else if("Google"==data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-google-paswd');
            $('#fp-user-email1').html("“"+userEmail+"”");
        }
        else if("Fail"==data.status){
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'account-exist');
        }
    }




    $('#back-signup').on('click',function () {
        $('#loginSignup').modal('show');
        $('#loginSignup').attr('data-page', 'login-google');
        $('.sign-mobile,.alternatelogin,.no-account').show();
        $('.loginBtns,.login-back-btn').hide();
    });
    $('#closeSignupModal').on('click',function () {
        $('#loginSignup').modal('hide');
        $('#loginSignup').attr('data-page', 'login-google');
        $('#loginFailed').hide();
    });
    function oldlogin() {
        $('.sign-mobile,.alternatelogin,.no-account').hide();
        $('.loginBtns,.login-back-btn').show();
        $('#back-btn').on('click',function(){
            $('.sign-mobile,.alternatelogin,.no-account').show();
            $('.loginBtns,.login-back-btn').hide();
            $('.sign-in').show();
        });
        $('.sign-in').hide();
    }

    function continueSignin() {
        operation ="signup";
        $('#loginSignup').attr('data-page', 'mobile-auth');
        document.getElementById("migrateMobile").value="";
        $('.mobileauth-header').text('Sign up');
    }
    function submitOTP() {
        mobileNumber = document.getElementById("mobileSignup").value;
        $('.mobile-auth .error-msg').hide();
        if (/^\d{10}$/.test(mobileNumber)) {
            document.getElementById("sentMobileNumber").innerHTML=mobileNumber;
            <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
         params="'mobile='+mobileNumber" />
            $('#loginSignup').attr('data-page', 'otp-verify');
            testr();
        } else {
            $('.mobile-auth .error-msg').show();
            document.getElementById("mobileSignup").focus();
            return false
        }
    }

    function submitMigrateOTP(resend=false) {
        $('.modal-content .loading-icon').removeClass('hidden');
        $('#migrateOtp').attr("disabled", "disabled");
        mobileNumber = document.getElementById("migrateMobile").value;
        $('.mobile-auth .error-msg').hide();
        if (/^\d{10}$/.test(mobileNumber)) {
            document.getElementById("sentMobileNumber").innerHTML=mobileNumber;
            if(operation=="migrate") {
                <g:remoteFunction controller="creation" action="generateUmOTP"  onSuccess='OTPReceived(data);'
         params="'mobile='+mobileNumber" />
            }else{
         %{--       if(mobileNumber.includes("@"))--}%
         %{--           <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'--}%
         %{--params="'email='+mobileNumber" />--}%
         %{--       else--}%
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
         params="'mobile='+mobileNumber+'&resend='+resend" />
            }

        } else {
            $('.modal-content .loading-icon').addClass('hidden');
            $('#migrateOtp').removeAttr('disabled');
            $('.mobile-auth .error-msg').show();
            document.getElementById("migrateMobile").focus();
            return false
        }

        // $('.loading-icon').addClass('hidden');
    }

    function OTPReceived(data){
        if(data.status=="Failed"){
            $('.modal-content .loading-icon').addClass('hidden');
            $('#migrateOtp').removeAttr('disabled');
            $('#user-exist').html('User already exists. Please use a different mobile number');
        }else{
            $('.modal-content .loading-icon').addClass('hidden');
            $('#migrateOtp').removeAttr('disabled');
            $('#loginSignup').attr('data-page', 'otp-verify');
            testr();
            $('#otperror-msg').hide();
            document.getElementById("signupOtp").value="";
        }
    }
    function verifyOTP(){
        $('.modal-content .loading-icon').removeClass('hidden');
        $('#verifybtn').attr("disabled", "disabled");
        if(document.getElementById("signupOtp").value==""){
            $('.modal-content .loading-icon').addClass('hidden');
            $('#verifybtn').removeAttr('disabled');
            $('#otp-empty').html('Please enter the OTP to proceed');
        }
        else{

            var mobileOTP=document.getElementById("signupOtp").value;
            if(operation=="migrate"){
                var username = "<%= session['userdetails']!=null?session['userdetails'].username:"" %>";

                <g:remoteFunction controller="creation" action="checkUmOTP"  onSuccess='otpVerified(data);'
    params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP+'&username='+username" />
            }
            else{
                if(mobileNumber.includes("@"))
                    <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
    params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
                else
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
    params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />
            }


        }

    }

    function otpVerified(data){
        $('.modal-content .loading-icon').addClass('hidden');
        if("OK"==data.status){
            if(operation=="signup"){
                if(data.userExists){
                    if(data.allowLogin){

                        window.location.href="/security/loginmanager";
                    }else{
                        $('.modal-content .loading-icon').addClass('hidden');
                        $('#verifybtn').removeAttr('disabled');
                        document.getElementById('limit-signin-verify').innerText='The user cannot login as he already logged in from multiple devices';
                    }
                }
                else{
                    $('.modal-content .loading-icon').addClass('hidden');
                    $('#verifybtn').removeAttr('disabled');
                    $('#loginSignup').attr('data-page', 'new-user');
                    document.getElementById("email").value="";
                    document.getElementById("signup-password").value="";
                    if(mobileNumber.includes("@"))
                        document.getElementById("newUserEmailMobile").innerText="Your mobile number (optional)";
                    else
                        document.getElementById("newUserEmailMobile").innerText="Your email address (optional)";
                }
            } else  if(operation=="forgotpassword"){
                if(data.userExists) {
                    //temporary solution
                    if(data.allowLogin){
                        $('#loginSignup').attr('data-page', 'set-password');
                    }else{
                        document.getElementById('limit-signin-verify').innerText='The user cannot login as he already logged in from multiple devices';
                    }
                }else{

                    $('#user-exist').html('No user exists for this email/mobile number');
                    $('#loginSignup').attr('data-page', 'mobile-auth');
                    $('#verifybtn').removeAttr('disabled');
                }
            }else  if(operation=="migrate"){

                $('#loginSignup').attr('data-page', 'set-password');
            }
        }
        else{

            $('#verifybtn').removeAttr('disabled');
            $('#otperror-msg').show();
        }
    }

    function setPassword(){
        if(document.getElementById("setPassword1").value!=document.getElementById("setPassword2").value){
            $('#passwd-error').html('Please make sure password and confirm password are same.');
        }
        else if(document.getElementById("setPassword1").value==""){
            $('#passwd-error').html('Please enter the password.')

        }else{
            var oldPassword  ="<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
            var password = document.getElementById("setPassword1").value;
            <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='passwordSetCompleted(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
        }
    }

    function passwordSetCompleted(data){
        window.location.href="/security/loginmanager";
    }
    function continueOTP() {
        if(operation=="signup")
            $('#loginSignup').attr('data-page', 'set-password');
        else
            $('#loginSignup').attr('data-page', 'set-password');
    }
    function setPaswd() {
        $('#loginSignup').attr('data-page', 'new-user');
    }
    function signIn() {
        $('#loginSignup').attr('data-page', 'sign-in');
        $('#nrmlSignin').val('Login');

    }

    function forgotPasswordMobile(){
        operation ="forgotpassword";
        $('#loginSignup').attr('data-page', 'mobile-auth');
        $('.mobileauth-header').text('Forgot Password');
        $('#user-exist').html('');
    }
    function migrateSignin() {
        $('#loginSignup').attr('data-page', 'migrate-signin');

    }
    var interval;
    function testr() {
        var counter = 60;
        clearInterval(interval);
        interval = setInterval(function () {
            counter--;
            // alert(counter);
            // Display 'counter' wherever you want to display it.
            if (counter <= 0) {
                clearInterval(interval);
                $('#timer').html("");
                $('#resendotp').show();
                return;
            } else {
                $('#timer').text(counter+"s");
            }
        }, 1000);
    }

</script>

<script>
    var stateObject = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }
    window.onload = function () {
        var stateSel = document.getElementById("stateSel"),
            districtSel = document.getElementById("districtSel");
        for (var states in stateObject) {
            stateSel.options[stateSel.options.length] = new Option(states, states);
        }
        stateSel.onchange = function () {
            districtSel.length = 1; // remove all options bar first
            if (this.selectedIndex < 1) return; // done
            var district = stateObject[stateSel.value];
            for (var i = 0; i < district.length; i++) {
                districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
            }
        }
        stateSel.onchange(); // reset in case page is reloaded
    }

    function submitSignIn(){
        $('.loading-icon').removeClass('hidden');
        <%if("eutkarsh".equals(session['entryController'])){%>
        document.signin.username.value= document.signin.username_temp.value;
        <%}else{%>
        document.signin.username.value= "${session["siteId"]}_"+document.signin.username_temp.value;
        <%}%>
%{--        <%if(wonderslateSite){%>--}%
        var username = document.signin.username.value;
        var password = document.signin.password.value;


        <g:remoteFunction controller="log" action="login"  onSuccess="loginDone(data);" params="'source=web&username='+username+'&password='+password" />
%{--        <%}else{%>--}%
//        if("ok"==data.status||"OK"==data.status){
//            console.log('if loop');
//             document.signin.submit();
//         }else {
//            console.log('else loop');
//            $('.loading-icon').addClass('hidden');
//            $("#loginFailed").show();
//        }
         // document.signin.submit();

%{--        <%}%>--}%
    }

    function loginDone(data){
       // var functionName = "variableNamedFunction";
       // window[functionName]();
        if("ok"==data.status||"OK"==data.status){
            <%if(wonderslateSite){%>
            window.location.href="/library";
            <%}else{%>
        document.signin.submit();

        <%}%>
        }else{
            $('.loading-icon').addClass('hidden');
            $("#loginFailed").show();
        }

    }

    function variableNamedFunction()
    {
        alert("variable name called ");
    }



</script>
