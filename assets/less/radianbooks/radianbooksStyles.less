@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

.radian_books {
  a {
    font-weight: normal;
  }
  p {
    line-height: normal;
  }
  .global-search {
    input[type="text"] {
      padding-left: 10px;
      padding-right: 40px;
      z-index: 3;
      @media (max-width: 320px) {
        padding-right: 20px;
      }
    }
    button {
      width: auto;
      height: 33px;
      margin-left: -38px;
      padding: 4px;
      position: relative;
      z-index: 10;
      color: @theme-primary-color !important;
      .material-icons {
        line-height: normal;
      }
    }
  }
  .add-tabs {
    top: 75px;
  }
  #allAddButton {
    display: none !important;
  }
  &.hasScrolled {
    .main-menu-wrp {
      position: relative !important;
    }
    .bookTemplate {
      .content-wrapper #book-sidebar .side-content > h2 {
        margin-top: 0;
      }
      .export-notes {
        top: 50px;
      }
    }
    .user-menu-wrp .menu-actives a.menu-dots-img-wrp {
        position: fixed;
    }
  }
  .bookTemplate {
    .content-wrapper {
      height: calc(100vh - 50px);
    }
    &.book_preview {
    .content-wrapper {
      @media (max-width: 767px) {
        .read-content.col-md-12 {
          .price-wrapper {
            display: none !important;
          }
        }
          #book-read-material {
            padding-bottom: 70px;
          }
          #book-sidebar {
            height: calc(100vh - 70px);
            padding-bottom: 0;
          }
          .price-wrapper {
            padding: 0.5rem 0;
            position: fixed;
            width: 100%;
            z-index: 991;
            .section-btns {
              padding: 0.5rem 0 0;
              background: @white;
            }
            .preview-book-btns {
              margin-left: 0;
            }
          }
        }
      }
    }
    .content-wrapper #book-sidebar .side-content > h2 {
      margin-top: 100px;
    }
    .export-notes {
      top: 128px;
    }
    .mobChapname #chapters-toggle.left i {
      transform: rotate(0deg);
    }
    .preview-book-btns .btn-book-buy {
      background: @theme-primary-color !important;
    }
    .ChapterHeader .bookTitle {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
  .web-mcq .sub-header {
    top: 90px;
    padding-top: 10px;
  }
  .bg-wsTheme {
    background: @theme-primary-color !important;
  }
  #question-block .question-wrapper {
    margin-top: 2rem;
    img {
      max-width: 100%;
    }
  }
  .tab-wrappers {
    top: 190px;
  }
  .web-mcq .result-menu {
    top: 90px;
  }
  #quizQuestionSection {
    padding-bottom: 50px;
  }
  .book_details_info #filelabel1, .book_details_info #filelabel2 {
    right: 0;
    left: 0;
  }
  #bookcover .smallText {
    justify-content: center;
  }
  .orders .payment-details > div:last-child p .rupees {
    display: none;
  }
  .users-orders > p {
    padding-right: 15px;
    padding-left: 15px;
  }
  .user_profile .tab-content .jumbotron form .media .continue,
  .btn-starts,
  #answer-block .button-wrapper a,
  #answer-block .button-wrapper a:hover {
    background: @theme-primary-color !important;
  }
  .test-gen-box-main .test-gen-box .btn-info {
    background: @theme-primary-color !important;
    border-color: @theme-primary-color !important;
    &:active:focus {
      box-shadow: 0 0 0 0.2rem rgba(239, 114, 21, 0.5) !important;
    }
  }
  .dropdown-menu a:active, .dropdown-menu span:active, .dropdown-menu li:active {
    background-color: #fce5d4;
  }
  .all-container .container-wrapper .media .quiz-practice-btn,
  .all-container .container-wrapper .media .showRank {
    text-transform: uppercase;
    color: @theme-primary-color;
    padding: 0;
    display: inherit;
    border-radius: 0;
  }
  .all-container .container-wrapper .media .showRank {
    padding-left: 10px;
    margin-left: 10px;
    border-left: 1px solid @black;
  }
  .all-container .container-wrapper .d-flex p.testStarts {
    margin: 0;
    top: 0;
    font-size: 12px;
    flex: none;
  }
  .play::before {
    left: -20px;
  }
  .backfromgenerator {
    margin-top: 20px;
    i {
      color: @theme-primary-color;
    }
  }
  #htmlContent {
    margin-top: 2rem;
  }
  .purchase-details-container {
    .purchase-heading {
      background: none !important;
      -webkit-text-fill-color: unset !important;
    }
    .browse-purchase-book {
      a.learn-btn {
        background: @theme-primary-color !important;
        color: @white;
      }
    }
  }
  .web-mcq .mt-fixed {
    margin-top: 3.5rem !important;
    padding-top: 0;
  }

  &.custom-fix .bookTemplate .shadowHeader {
    position: fixed;
    top: 0;
  }

  .start-test .header p {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    padding: 0 15px;
  }

  #quizQuestionSection .result-menu > div h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    padding: 0 10px;
  }

  .mt-fixed #resourceTitle {
    margin-top: 50px;
    text-align: center;
    font-size: 18px;
    font-weight: @medium;
    padding: 0 15px;
  }
  .notes-creation-header {
    z-index: 1;
  }

  .index-page {
    .main-menu-wrp {
      position: relative;
    }
    .header-menu-wrapper {
      position: absolute;
      left: 0;
      top: 100px;
      text-align: center;
      width: 100%;
    }
    .this-is-a-web-view-slider {
      display: block;
      overflow: hidden;
      @media (max-width: 991px) {
        display: none;
      }
      .wapper-all-slider-staticss {
        min-height: 80vh;
        background-size: 100% auto !important;
        background-attachment: fixed !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        @media (min-width: 1600px) {
          background-position: left top !important;
        }
      }
      .carousel-inner {
        .carousel-item {
          img {
            width: 100%;
            max-width: 100%;
            min-height:auto;
            max-height: 70vh;
            @media (min-width: 2000px) {
              min-height: auto;
              max-height: 70vh;
            }
            @media (max-width: 1199px) {
              min-height: auto;
              max-height: 100%;
            }
          }
        }
      }
    }
    .this-is-a-responsive-view-slider {
      display: none;
      @media (max-width: 991px) {
        display: block;
      }
      img {
        width: 100%;
      }
    }
    .carousel-indicators {
      li {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: lighten(@theme-primary-color,50%);
        &.active {
          background: @theme-primary-color;
        }
      }
    }
  }
  .ebooks {
    .ebooks_filter {
      width: 100%;
    }
  }
}

.mozilla .arihant {
  .index-page {
    .this-is-a-web-view-slider {
      .wapper-all-slider-staticss {
        background-attachment: unset !important;
      }
    }
  }
}

#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal,
#test-gen-modal,
#PlayAudiOnlyModal,
#quizModal {
  z-index: 9992;
}

.mobile-footer-nav {
  background: @theme-primary-color;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px 20px 0 0;
  bottom:0;
  left:0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  &.hide-menus {
    bottom: -75px;
    transition: all 0.5s linear;
    -webkit-transition: all 0.5s linear;
    -moz-transition: all 0.5s linear;
    -ms-transition: all 0.5s linear;
    -o-transition: all 0.5s linear;
  }
  a{
    text-decoration: none;
    flex-wrap: wrap;
    text-align: center;
    color: @white;
    &:focus {
      text-decoration: none;
    }
    &:visited {
      text-decoration: none;
    }
    img {
      margin: 0 auto;
      width: 22px;
    }
    p {
      width: 100%;
      font-size: 13px;
    }
  }

  i {
    color: white;
  }
  .active-menu {
    opacity: 1;
  }
  .common-footer-nav {
    opacity: 0.8;
  }
}

::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}

#test-gen-modal .overlay-testgen-book .book-selected {
  width: 40px;
  height: 40px;
}
#quizQuestionSection #submit-test .modal-footer button {
  font-family: 'Poppins', sans-serif !important;
}
#quizQuestionSection #submit-test .modal-footer .submit {
  background: @theme-primary-color;
  color: @white;
}

@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation : portrait),
only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation : landscape) {
  .arihant {
    #book-read-material #content-data-all {
      padding: 0;
      > .container {
        padding: 0;
      }
    }
    .all-container .container-wrapper {
      margin-top: 0;
      border: none;
      border-bottom: 1px solid #ededed;
      box-shadow: none;
      border-radius: 0;
    }
    .all-container .container-wrapper .media i {
      margin: 0 1rem;
    }

  }
}

@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation : landscape) {
  .arihant {
    .bookTemplate .export-notes {
      top: 51px !important;
    }
  }
}

.arihant.hasScrolled.custom-fix{
  .bookTemplate .export-notes{
    top:50px !important;
  }
}

@media only screen and (max-width: 767px) {
  .arihant h2#expiry-date {
    margin-top: 0 !important;
  }
}

.footer {
  .download-app-links {
    .android-app-link {
      border: 1px solid #ddd;
      padding: 2px 0 5px;
      border-radius: 7px;
      min-width: 140px;
      background-color: #e5e5e5;
      &:hover {
        background-color: #e7e7e7;
      }
      img {
        width: 20px;
        margin-right: 10px;
        height: auto;
      }
      span {
        line-height: normal;
        font-size: 15px;
        small {
          position: relative;
          top: 3px;
        }
      }
    }
  }
}


// Overwriting Styles
.radian_books {
  #total-books-of-user {
    display:none;
  }
  #loginOpen,#signup,#forgotPasswordmodal {
    .modal-header .close {
      font-size: 20px;
    }
  }
  .menu-main-min-height {
    min-height: 95px;
    @media (max-width: 767px) {
      min-height: 70px;
    }
  }
  .main-menu-wrp {
    height: 95px;
    background: @theme-primary-color none repeat scroll 0 0;
    @media (max-width: 767px) {
      height: 70px;
    }
    .posi-static-respons {
      display: flex;
      align-items: center;
    }
  }
  p, a, button {
    font-weight: initial;
  }
  .bookTemplate {
    .side-content ol {
      li.chapter-name i {
        position: relative;
        top: 4px;
      }
    }
    .chapterSection a.slide-toggle {
      top: 190px;
    }
    .export-notes {
      top: 140px;
    }
  }
  .manage-count-wrp-box:after {
    background: linear-gradient(50deg,@theme-primary-color 0%,#89c1ff 100%);
  }
  .connect-section {
    background: #E3E3E3 !important;
  }
  .red-color-fill-bg {
    background: @theme-primary-color;
  }
  ul.this-is-side-wrp-ul-big-menu-arihant {
    li.active-menuss, li:hover {
      background: #E3E3E3 !important;
    }
  }
  .categories-section {
    background: url("../../images/radianbooks/radian-books-bg.jpg");
    background-size: cover;
    background-repeat: no-repeat;
  }
  .ebook_detail .book_info .book_buttons .col #buyNow {
    color: @white;
  }
  #okBuy {
    color: @white;
  }

  // For all whitelabel sites
  .bookTemplate {
    .shadowHeader {
      height: 50px !important;
    }
  }
  @media @extraSmallDevices, @smallDevices {
    .bookTemplate {
      height: auto;
      .mobChapname {
        transition: all 0.3s;
        z-index: 991;
      }
      .shadowHeader {
        z-index: 991;
        height: 45px !important;
        position: fixed;
        transition: all 0.3s;
        .tab-header {
          .navbar {
            height: auto;
            padding-top: 0.15rem;
          }
          .contentEdit {
            position: fixed;
            top: 50px;
            right: 0;
            transition: all 0.3s;
            overflow: hidden;
          }
        }
        .prevnextbtn {
          position: fixed;
          top: 50px;
          width: 100% !important;
          justify-content: center !important;
          transition: all 0.3s;
          button {
            margin: 0 5px;
            width: 80px;
            font-size: 13px;
          }
        }
      }
      .chapterSection {
        z-index: 991;
      }
      #book-sidebar {
        //padding-bottom: 0;
        //height: calc(100vh - 80px);
        .backtolibrary {
          font-weight: normal !important;
        }
        .mobile-title {
          z-index: 98;
          p {
            line-height: normal;
            padding-left: 10px;
          }
        }
        .side-content ol {
          padding-left: 10px !important;
          li.chapter-name {
            font-size: 15px;
            position: relative;
            margin-right: 1.5rem;
            padding-right: 20px;
            &.orangeText a {
              font-size: 15px;
            }
          }
        }
        ul.chapter-sections {
          display: none;
        }
      }

      #book-read-material {
        //padding: 0;
        //margin-bottom: 70px !important;
        #content-data-all {
          position: relative;
          z-index: 99;
        }
        .all-container {
          margin-top: 2rem;
          .container-wrapper {
            width: 100%;
            margin-top: 1rem;
            min-height: auto;
            .media {
              padding: 0;
              i {
                margin-left: 0;
              }
              .title {
                margin-bottom: 5px;
              }
              .readnow {
                padding-right: 15px;
              }
            }
          }
        }
      }

      #htmlreadingcontent {
        iframe {
          height: 100vh !important;
          border: 1px solid @gray;
          margin-top: 0 !important;
        }
      }

      .export-notes {
        top: 90px !important;
        .notes-creation-header {
          padding: 0.5rem 0;
        }
        .notes-creation-header-title {
          font-size: 16px;
          color: @black;
        }
      }
    }
    &.hasScrolled {
      .bookTemplate {
        .tab-header .contentEdit {
          top: 5px;
        }
        .export-notes {
          top: 45px !important;
        }
        .shadowHeader .prevnextbtn {
          top: 7px;
        }
      }
    }

    .logo-wrapper {
      display: none;
    }

    .my_books {
      #subjectFilter {
        .dropdown #sortBy {
          width: auto;
        }
      }
    }
  }

  .purchase-details-container .purchase-details-wrapper .learn-btn {
    background: @theme-primary-color;
  }
}

.category_list{
  display: flex;
  flex-direction: column;

  .category_level{
    margin-bottom: 2rem;
    h4{
      position: relative;
      margin-bottom: 1.4rem;
      &:after{
        width: 50px;
        height: 2px;
        content: '';
        position: absolute;
        background:@theme-primary-color;
        left: 0;
        bottom: -4px;
      }
    }
    .category_cards{
      display: grid;
      grid-template-columns: repeat(5,1fr);
      grid-gap: 1.2rem;
      @media (max-width: 768px){
        grid-template-columns: repeat(2,1fr);
        grid-gap: 1rem;
      }

      .category_card{
        padding: 0.5rem;
        border-radius: 5px;
        border: 1px solid rgba(0,0,0,0.1);
        height: 80px;
        background: #eee;
        transition: all 0.3s ease-in;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #000;
        &:active{
          transform: scale(0.7);
        }
        &:hover{
          //background: #eee;
          background: transparent;
        }
        a{
          color: black;
          height: 100%;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &-title{
          width: 100%;
          text-align: center;
        }
        @media (max-width: 768px){
          width: 100%;
        }
      }

    }
  }
}

.title-connect-section,
.connect-sec-wrp-disc-pera,
ul.social-icon-wrp-connect li a,
h3.call-here-number a,
ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss a,
ul.this-is-side-wrp-ul-big-menu-arihant li:hover a{
  color: #000 !important;
}

.headerCategoriesMenu{
  border-bottom: 1px solid rgba(0,0,0,0.175);
}
.headerCategoriesMenu .header__categories{
  background: #F5F5F5 !important;
}
.headerCategoriesMenu .header__categories-list__item:not(:last-child){
  border-right-color: @theme-primary-color;
}
.headerCategoriesMenu .header__categories-list__item a{
  color: @black;
}
#accordion .card-body{
  background: #e5e5e59c;
}
#accordion .card-header{
  background: #dadada;
}
#accordion .card a{
  color: #000;
}
#accordion .card-header .btn-link:after{
  color:#000;
}
#accordion .card-header .btn-link.collapsed:after{
  color:#000;
}