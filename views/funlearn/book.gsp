<g:render template="navheader"></g:render>
<g:render template="topicinclude"></g:render>
<link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet' type='text/css'>

<style>

div.hopscotch-bubble {
    border: 2px solid #f15a29;
    background: transparent;
    box-shadow: 0 2px 5px 0 white,
    0 2px 10px 0 white;
}
.hopscotch-bubble-container {
    background: transparent;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
}

div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {
    border: 1px solid #ffffff;
}

</style>

<asset:stylesheet href="flipbook.style.css"/>
<asset:stylesheet href="font-awesome.css"/>

<div class="wrapper">
    <div class="container-fluid">
        <div  class='row'>

            <div class='col-md-10 col-md-offset-1   chapterbackground'>
                <div id="content-notes" style="display:none" >
                    <div class="row " >
                        <div class="col-md-5 col-md-offset-1">
                            <div class="row"  id="content-header-notes">

                            </div>
                            <div id="content-data-notes" class="row">
                            </div>
                        </div>
                        <div class="col-md-5 col-md-offset-1"><div id="content-data-quiz" class="row">
                        </div></div>

                    </div>
                    <br>
                </div>


                <div  id="content-quiz" style="display:none">
                </div>
                <div  id="content-header-quiz" style="display:none">
                </div><br>
            </div>
            <div class='col-md-1'>
                <div ><a href='/funlearn/book?bookId=1'><img src='/funlearn/showProfileImage?id=1&fileName=book1.jpg&type=books&imgType=passport' height='150' class='boxifybook'></a>
                </div>
            </div>

            <br>
            <br>
            <br>
        </div>


    </div>
    <br>
    <div class="push"></div>
</div>
<div id="lightbook">

</div>
<g:render template="footer"></g:render>
<!--</div>-->
<script>
    var pageType='book';
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>


<asset:javascript src="pageflip/flipbook.min.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>


<g:render template="topicscripts"></g:render>

<script>
    displayChapterName();
    getTopicDetails('${topicId}','chapter');

    function displayChapterName()
    {
        var chapterSelect = document.getElementById("addlevel");
        var chapterName = chapterSelect.options[chapterSelect.selectedIndex].text;
       // document.getElementById("content-header-notes").innerHTML="<h4>"+chapterName+"</h4>";
    }

function getChapterDetails(chapterId){
    displayChapterName();
   document.getElementById("content-data-quiz").innerHTML="";
   document.getElementById("content-data-notes").innerHTML="";
    document.getElementById("content-header-quiz").innerHTML="";
    getTopicDetails(chapterId,'chapter');
}

    function initializeFirstTwoQuizQuestions(data) {
        var questions = data.results;
        var htmlStr="";
        var quest;
        for (var i = 0; i < questions.length; ++i) {

            htmlStr +=" <div class='row ' ><div class='col-md-9 col-md-offset-1 chaptertext'>"+(i+1)+". "+questions[i].ps+"</div></div>";

            console.log("htmlStr=" + questions[i].resType);

            if ("Fill in the blanks" == questions[i].resType) {

            }
            else if ("Opposites" == questions[i].resType) {

            }
            else if ("True or False" == questions[i].resType) {
                htmlStr += "<div style='position:relative;' class='row'>" +
                        "                            <div class='col-md-9 col-md-offset-1 '>" +
                        "                                <input id='radio-btn-true' class='radio-true' type='radio' value='true' name='optradio'>" +
                        "                                <label style='position:absolute;' for='radio-btn-true'></label>" +
                        "                                <span style='font-weight:700; margin-left: 40px;'>True</span>" +
                    "                         <input id='radio-btn-false' class='radio-false' type='radio' value='false' name='optradio'>" +
                        "                                <label style='position:absolute;' for='radio-btn-false'></label>" +
                        "                                <span style='font-weight:700; margin-left: 40px;'>False</span>" +
                        "                            </div>" +
                        "                        </div><br>"
            } else {
                htmlStr +=" <div class='row ' ><div class='col-md-9 col-md-offset-1 greytext'>&nbsp;&nbsp;<input type='radio' name='answer'> "+questions[i].op1+" &nbsp;&nbsp;<input type='radio' name='answer'> "+questions[i].op2+"&nbsp;&nbsp;<input type='radio' name='answer'> "+questions[i].op3+"&nbsp;&nbsp;<input type='radio' name='answer'> "+questions[i].op4+"</div></div><br>";
            }
        }
        document.getElementById("quiz"+data.quizId).innerHTML=htmlStr;

    }
</script>


</body>
</html>