package com.wonderslate

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.data.BookIdGenerator
import com.wonderslate.data.CurrentAffairsMst
import com.wonderslate.data.RelatedVideos
import com.wonderslate.data.RelatedVideosNew
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SitemapService
import com.wonderslate.institute.OnlineTestService
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookProductTypes
import com.wonderslate.shop.DirectSaleOrders
import com.wonderslate.shop.ShoppingCartOrdersDtl
import com.wonderslate.shop.SubscriptionMst
import com.wonderslate.shop.WsshopService
import com.wonderslate.sqlutil.SafeSql
import org.apache.commons.lang.StringEscapeUtils;
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.comparison.SorensenDiceService
import com.wonderslate.data.BannersMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.DisciplinesMst
import com.wonderslate.data.FeaturedGradeDtl
import com.wonderslate.data.KeyValues
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.MetainfoService
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.QuizExtractorService
import com.wonderslate.data.QuizIdGenerator
import com.wonderslate.data.ResourceCreatorService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.ResourceDtlSub
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.data.AnnotatorMst
import com.wonderslate.data.VideoExplanation
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.institute.InstituteMst
import com.wonderslate.log.BooksViewDtl
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.publish.BooksPermissionCopy
import com.wonderslate.publish.BooksQueueDtl
import com.wonderslate.publish.ExamDtl
import com.wonderslate.publish.ExamMst
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SageTags
import com.wonderslate.shop.DiscountMst
import com.wonderslate.shop.PurchaseService
import com.wonderslate.usermanagement.AuthenticationToken


import com.wonderslate.usermanagement.AnnotatorQuoteDetails

import grails.transaction.Transactional

import org.grails.web.util.WebUtils


import org.imgscalr.*

import pl.touk.excel.export.WebXlsxExporter


import javax.imageio.ImageIO
import javax.servlet.http.Cookie
import java.awt.image.BufferedImage

import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat

import com.wonderslate.discussions.Answers
import com.wonderslate.discussions.Questions
import com.wonderslate.publish.Authors
import com.wonderslate.publish.BookRatingReviews
import com.wonderslate.publish.BooksAuthorDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ChapterAccess
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import com.wonderslate.publish.BooksPermission

import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import groovy.sql.Sql
import jxl.Sheet
import jxl.Workbook
import jxl.WorkbookSettings
import org.apache.commons.io.FileUtils


import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonBuilder


import java.util.concurrent.TimeUnit


import javax.xml.parsers.DocumentBuilder
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException

import org.w3c.dom.Document

import org.w3c.dom.NodeList
import org.w3c.dom.Node
import org.w3c.dom.NamedNodeMap
import org.xml.sax.SAXException

import pl.allegro.finance.tradukisto.ValueConverters

import com.razorpay.*
import com.wonderslate.institute.IsbnKeyword
import com.ccavenue.security.*

import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.apache.commons.codec.binary.Base64;

@Transactional
class WonderpublishController {
    UserManagementService userManagementService
    SpringSecurityService springSecurityService
    Document dom
    def wantedNode = false
    Vector<String> v
    List<String> filesListInDir
    String opfFileName
    QuizExtractorService quizExtractorService
    def redisService
    DataProviderService dataProviderService
    WsLibraryService wsLibraryService
    UtilService utilService
    SorensenDiceService sorensenDiceService
    DataNotificationService dataNotificationService
    PurchaseService purchaseService
    AsyncLogsService asyncLogsService
    ResourceCreatorService resourceCreatorService
    MetainfoService metainfoService
    WsshopService wsshopService
    WsLibraryCacheService wsLibraryCacheService
    SitemapService sitemapService
    LibraryBooksService libraryBooksService
    OnlineTestService onlineTestService
    
    def index() {
        session['siteId'] = new Integer(1)
        render (view:"index")
        [backgroundImage  : true]
    }
    def errorPage(){
        [newCss : true]
    }
    def instructorLibrary(){}

    def answerMatch() {}

    def welcome() {}

    def footer() {}

    def aboutus() {}

    def faq() {}

    def booksHolderTemplate() {}
    def booksHolder() {}

    def slider() {}

    def bookUploadHeader() {}

    def bookReviews() {}

    def bookReviewModal() {}
     def book1(){}
    def studySet() {
        String mode="create"
        List keyValues
        String revisionName=""
        def resId="-1"
        if("edit".equals(params.mode)){
            mode="edit"
            keyValues = KeyValues.findAllByResIdAndStatus(new Long(params.resId),"active")
            resId = params.resId
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            revisionName = resourceDtl.resourceName
        }

        [mode:mode,keyValues:keyValues,resId: resId, hideBanner: true,revisionName:revisionName]
    }

    def wonderGoaStudySet() {}

    def answerMatchModal() {}
    def approvalChapterDetails(){
        List chapterDetails  = ResourceDtl.findAllByTopicIdAndSharing(params.topicId,null,[sort:"resType"])
        List jsonChapter = chapterDetails.collect{comp ->
            return [id: comp.id, resType: comp.resType,  resLink: comp.resLink, resName: comp.resourceName,
                    canDelete:(springSecurityService.currentUser!=null)? userManagementService.canDelete(springSecurityService.currentUser.username, comp.createdBy):false ,
                    canEdit:(springSecurityService.currentUser!=null)? userManagementService.canEdit(springSecurityService.currentUser.username, comp.createdBy):false,
                    canApprove:true]
        }

        def json =[
                'results': jsonChapter,
                'status': jsonChapter ? "OK" : "Nothing present"
        ]

        render json as JSON
    }




    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def bookCreate() {


    }

    @Transactional
    def bookCreateNew() {
        def title
        def bookId = -1
        BooksMst booksMst = null
        List selectedAuthors = null
        List chaptersMst = null
        ResourceDtl resourceDtl = null
        List booksTagDtl = null
        List publishers
        List authors
        List disciplines = null
        List sageTags=[]
        List publisherAccessUsers;
        boolean allowAudioVideo = false
        boolean showDownloadChapters = false
        boolean wsSite = false
        def siteId = getSiteId(request)
        boolean gptManager=false
        if(session["userdetails"].authorities.any { it.authority == "ROLE_GPT_MANAGER" }) gptManager=true
        if(siteId.intValue()==1){
           if(session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_ADMIN"} && session["userdetails"].publisherId==null) {
               allowAudioVideo = true
           }
            wsSite = true
        }
        if(siteId.intValue()==25 && session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_ADMIN"} && session["userdetails"].publisherId==null){
            //libWonder
            allowAudioVideo = true
        }


        session.removeAttribute("htmlId")

        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        String sql = "select u.username from wsuser.user u,wsuser.user_role ur,wsuser.role r" +
                " where r.authority='ROLE_BOOK_CREATOR' and ur.role_id=r.id and ur.user_id=u.id and u.site_id="+getSiteId(request)
        if(session["userdetails"].publisherId!=null) sql +=" and u.publisher_id="+session["userdetails"].publisherId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql);

        publisherAccessUsers = results.collect { user ->
            String username=user[0]+"";
            if(username.contains("_")) {
                username = user[0].toString().split("_")[1];
            }
            return [username:username]
        }

        if (params.bookId != null && !"".equals(params.bookId)) {
            bookId = params.bookId
            booksMst = dataProviderService.getBooksMst(new Long(bookId))
            // check does the user has rights to enter
            if (session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR" } ||
                    (""+booksMst.publisherId).equals(""+session["userdetails"].publisherId) ||(session["userdetails"].authorities.any { it.authority == "ROLE_BOOK_CREATOR" }&&booksMst.createdBy.equals(springSecurityService.currentUser.username)) ) {
                if (redisService.("authorsforbook_" + params.bookId) == null) {
                    dataProviderService.getAuthorsForBook(new Long(bookId))
                }

                selectedAuthors = new JsonSlurper().parseText(redisService.("authorsforbook_" + params.bookId))
                if (redisService.("chapters_" + booksMst.id) == null) {
                    dataProviderService.getChaptersList(booksMst.id)
                }

                chaptersMst = new JsonSlurper().parseText(redisService.("chapters_" + booksMst.id))

                if (redisService.("tagsforbooks_" + params.bookId) == null) {
                    dataProviderService.getBookTagsForBook(new Long(bookId))
                }

                booksTagDtl = new JsonSlurper().parseText(redisService.("tagsforbooks_" + params.bookId))

                if (redisService.("authorsforpublishers_" + booksMst.publisherId) == null) {
                    dataProviderService.getAuthorsForPublishers(booksMst.publisherId)
                }

                authors = new JsonSlurper().parseText(redisService.("authorsforpublishers_" + booksMst.publisherId))


            } else {
                redirect(action:'index')
            }
        }
        if(session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR"}) {
            if (redisService.("publishers_" + getSiteId(request)) == null) {
                dataProviderService.getPublishers(getSiteId(request))
            }
        }else{
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        }
        if(session["userdetails"].authorities.any {
            it.authority == "ROLE_WS_CONTENT_CREATOR"})
            publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
        boolean smartEbook = "true".equals(params.printBooks)?false:true
        //remove the below comment tag once the sage cms is set

        if(session["siteId"]!=null&&session["siteId"].intValue()==9) {
            disciplines = DisciplinesMst.findAll()
            sageTags = SageTags.findAll()

        }

        SiteMst siteMst
        if(siteId!=null) {
            siteMst = dataProviderService.getSiteMst(siteId)

        }
        String keywordStr = ""
        if((booksMst==null || (booksMst!=null && booksMst.singleEpub!="true")) && siteMst.downloadBookChapters=="true")showDownloadChapters=true
        if(booksMst!=null && booksMst.isbn!=null) {
            List<IsbnKeyword> isbn = IsbnKeyword.findAllByIsbn(booksMst.isbn)
            if (isbn.size() > 0) {
                for (int i = 0; i < isbn.size(); i++) {
                    keywordStr = keywordStr + isbn.get(i).keyword + ","
                }
                keywordStr = keywordStr.substring(0, keywordStr.length() - 1);
            }
        }

        List subscriptions = SubscriptionMst.findAll()
        List bookTypes = BookProductTypes.findAll()

        boolean showDeleteButton = true;
        if("71".equals(""+session["siteId"]) && booksMst != null && "true".equals(booksMst.lockEdit)) {
            // For site ID 71, hide delete button if lockEdit is true, unless user has ROLE_PDF_EXTRACTOR
            if(session["userdetails"].authorities.any { it.authority == "ROLE_PDF_EXTRACTOR" }){
                showDeleteButton = true;
            }
        }
         [booksMst     : booksMst,keywordStr:keywordStr, bookId: bookId, authors: authors, selectedAuthors: <AUTHORS>
         newCss       : true, publishers:publishers, smartEbook:smartEbook,
         disciplines  :disciplines, hideBanner:true, tags:sageTags, userPublisherId:session["userdetails"]!=null?session["userdetails"].publisherId:null, siteMst: siteMst,
         levelsMstList: LevelsMst.findAllBySiteIdInList([siteId,new Integer(1)], [sort:"siteId",order:"desc"]), langMstList:com.wonderslate.data.LangMst.listOrderBySortBy(),
         showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),apiKey: siteMst.googleApiKey,institutePublisher:userManagementService.isInstitutePublisher(),
         instituteId:userManagementService.getInstituteId(),allowAudioVideo:allowAudioVideo,wsSite:wsSite,
          showDownloadChapters:showDownloadChapters,subscriptions:subscriptions,bookTypes: bookTypes,gptManager:gptManager, showDeleteButton: showDeleteButton]
    }
    @Transactional
    def updateDescription(){
        BooksMst booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
        BooksMst.executeUpdate("update BooksMst set description ='" + params.description + "' where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set description ='" + params.description + "' where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set description ='" + params.description + "' where id=" + params.bookId)
        dataProviderService.refreshCacheForPublishUnpublish(params.bookId,getSiteId(request))
        if("sage".equals(session["entryController"]))  {
            redirect(controller: "wonderpublish", action: "bookCreate", params: [bookId: booksMst.id, booksMst: booksMst, smartEbook: ("true".equals(params.smartEBook)) ? true : false])
        }
        else{
            redirect(controller: "wonderpublish", action: "bookCreateNew", params: [bookId: booksMst.id, booksMst: booksMst, smartEbook: ("true".equals(params.smartEBook)) ? true : false])
        }
        }

    @Transactional @Secured(['ROLE_BOOK_CREATOR'])

    def pdfBookCreate(){
        BooksMst booksMst,booksMst1,booksMst2
        Long bookId
        Long chapterId
        Integer siteId = getSiteId(request)
        def colValue=(params.columnValue);
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        User user  = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        booksMst = new BooksMst(title:colValue, createdBy: springSecurityService.currentUser.username, siteId: siteId
                ,bookType: "true".equals(params.printBook)?"print":"smartebook",publisherId: params.publisherId,showInLibrary: "Yes",showDiscount: "Yes", authors: user.name)
        booksMst.save(failOnError: true,flush: true)
        booksMst1 = new BooksMst(id:booksMst.id,title: colValue, createdBy: springSecurityService.currentUser.username, siteId: siteId
                ,bookType: "true".equals(params.printBook)?"print":"smartebook",publisherId: params.publisherId,showInLibrary: "Yes",showDiscount: "Yes", authors: user.name)
        booksMst1.wsuser.save(failOnError: true,flush: true)
        booksMst2 = new BooksMst(id:booksMst.id,title: colValue, createdBy: springSecurityService.currentUser.username, siteId: siteId
                ,bookType: "true".equals(params.printBook)?"print":"smartebook",publisherId: params.publisherId,showInLibrary: "Yes",showDiscount: "Yes", authors: user.name)
        booksMst2.wsshop.save(failOnError: true,flush: true)
        bookId = booksMst.id

        BooksPermission booksPermission = new BooksPermission(bookId: bookId, username: springSecurityService.currentUser.username)
        booksPermission.save(failOnError: true, flush: true)
        if(siteMst.allBooksLibrary=="true" || siteId.intValue()==25) dataProviderService.getBooksListForUser()
        if(siteMst.allBooksLibrary!="true")redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
        dataProviderService.getWSUnpublishedMyBooks()
        dataProviderService.getWSPublishedMyBooks()

        ChaptersMst chaptersMst = new ChaptersMst(name: "Full book", bookId: bookId,sortOrder:null )
        chaptersMst.save(failOnError: true)
        chapterId = chaptersMst.id

        def json = ['bookId':bookId,chapterId:chapterId]

        render json as JSON


    }

    @Transactional
    def bookUpdate() {
        BooksMst booksMst,booksMst1,booksMst2
        def bookId
        def newBook = "false"
        String oldPackageBookIds= null
        def colValue=(params.columnValue);
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        if (params.bookId == "-1") { //new book

            BookIdGenerator bookIdGenerator = new BookIdGenerator()
            bookIdGenerator.save(failOnError: true, flush: true)
             booksMst = new BooksMst()

            // Assign property values
            booksMst.id = bookIdGenerator.id
            booksMst.title = colValue
            booksMst.createdBy = springSecurityService.currentUser.username
            booksMst.siteId = new Integer(""+session["siteId"])
            booksMst.bookType = "true".equals(params.printBook) ? "print" : "smartebook"
            booksMst.publisherId = params.publisherId
            booksMst.showInLibrary = "Yes"
            booksMst.showDiscount = "Yes"
            booksMst.newStorage="Yes"
            booksMst.save(failOnError: true,flush: true)


            // Assign property values
            booksMst1 = new BooksMst()
            booksMst1.id = bookIdGenerator.id
            booksMst1.title = colValue
            booksMst1.createdBy = springSecurityService.currentUser.username
            booksMst1.siteId = new Integer(""+session["siteId"])
            booksMst1.bookType = "true".equals(params.printBook) ? "print" : "smartebook"
            booksMst1.publisherId = params.publisherId
            booksMst1.showInLibrary = "Yes"
            booksMst1.showDiscount = "Yes"
            booksMst1.newStorage="Yes"
             booksMst1.wsuser.save(failOnError: true,flush: true)

            // Assign property values
            booksMst2 = new BooksMst()
            booksMst2.id = bookIdGenerator.id
            booksMst2.title = colValue
            booksMst2.createdBy = springSecurityService.currentUser.username
            booksMst2.siteId = new Integer(""+session["siteId"])
            booksMst2.bookType = "true".equals(params.printBook) ? "print" : "smartebook"
            booksMst2.publisherId = params.publisherId
            booksMst2.showInLibrary = "Yes"
            booksMst2.showDiscount = "Yes"
            booksMst2.newStorage="Yes"
            booksMst2.wsshop.save(failOnError: true,flush: true)
            bookId = booksMst.id
            newBook="true"

            if(!"true".equals(params.printBook)) {
                BooksPermission booksPermission = new BooksPermission(bookId: bookId, username: springSecurityService.currentUser.username)
                booksPermission.save(failOnError: true, flush: true)
                if(siteMst.allBooksLibrary=="true" || getSiteId(request)==25)  dataProviderService.getBooksListForUser()
                if(siteMst.allBooksLibrary!="true")redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
            }
        } else { //update
            booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
            oldPackageBookIds = booksMst.packageBookIds
            if("testStartDate".equals(params.columnName) || "testEndDate".equals(params.columnName)){
                if(params.columnValue!=null&&!"".equals(params.columnValue)) {
                    DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm")
                    Date columnValue = df.parse(params.columnValue)
                    columnValue = convertDate(columnValue, "IST", "UTC")
                    booksMst.("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") = columnValue
                    booksMst.save(flush: true, failOnError: true)

                    String stringcolumnValue = df.format(columnValue)
                    BooksMst.wsuser.executeUpdate("update BooksMst set " + ("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") + "='" + stringcolumnValue + "' where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set " + ("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") + "='" + stringcolumnValue + "' where id=" + params.bookId)
                }else{
                    booksMst.("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") = null;
                    booksMst.save(flush: true, failOnError: true)
                    BooksMst.wsuser.executeUpdate("update BooksMst set " + ("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") + "=" + null + " where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set " + ("testStartDate".equals(params.columnName) ? "testStartDate" : "testEndDate") + "=" + null + " where id=" + params.bookId)
                }
//                }
            } else if("bookExpiry".equals(params.columnName)){
                if(params.columnValue!=null&&!"".equals(params.columnValue)) {
                    DateFormat df = new SimpleDateFormat("yyyy-MM-dd")
                    Date columnValue = df.parse(params.columnValue)
                    booksMst.("bookExpiry") = columnValue
                    booksMst.save(flush: true, failOnError: true)

                    String stringcolumnValue = df.format(columnValue)
                    BooksMst.wsuser.executeUpdate("update BooksMst set bookExpiry='" + stringcolumnValue + "' where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set bookExpiry='" + stringcolumnValue + "' where id=" + params.bookId)

                }else{
                    booksMst.("bookExpiry") = null;
                    booksMst.save(flush: true, failOnError: true)
                    BooksMst.wsuser.executeUpdate("update BooksMst set bookExpiry=" + null + " where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set bookExpiry=" + null + " where id=" + params.bookId)

                }
                }
             else{
                if("ebookplus".equals(params.columnValue)) colValue="eBook+"
                colValue=StringEscapeUtils.escapeSql(colValue)
                BooksMst.executeUpdate("update BooksMst set " + params.columnName + "='" + (""+colValue).trim() + "' where id=" + params.bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set " + params.columnName + "='" + (""+colValue).trim() + "' where id=" + params.bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set " + params.columnName + "='" + (""+colValue).trim() + "' where id=" + params.bookId)
                if("packageBookIds".equals(params.columnValue)){
                    dataProviderService.packageBooksByMainBookId(new Long(params.bookId))
                }
            }

            bookId = params.bookId
            booksMst = BooksMst.findById(new Long(params.bookId))
            booksMst.refresh()
            if(siteMst.allBooksLibrary=="true" || getSiteId(request)==25)  dataProviderService.getBooksListForUser()
            if(siteMst.allBooksLibrary!="true")redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
        }

        def json
        if("publisherId".equals(params.columnName)){
            //first remove existing records
            BooksAuthorDtl.executeUpdate("delete BooksAuthorDtl where bookId=" + params.bookId)
            if(redisService.("authorsforpublishers_"+params.columnValue)==null){
                dataProviderService.getAuthorsForPublishers(new Long(params.columnValue))
            }

            List authors =  new JsonSlurper().parseText(redisService.("authorsforpublishers_"+params.columnValue))
            json = [
                    'bookId': bookId,'fieldName':params.columnName,'newBook': newBook, 'authors':authors
            ]

        } else {
            json = [
                    'bookId': bookId,'fieldName':params.columnName,'newBook': newBook
            ]
        }

        //update the cache
        //dataProviderService.getBooksListCreatedBy(getSiteId(request),session["userdetails"].publisherId,springSecurityService.currentUser.username)
        if("packageBookIds".equals(params.columnName)) dataProviderService.getChaptersList(booksMst.id)
        if("published".equals(booksMst.status)){
            if("packageBookIds".equals(params.columnName)) updatePackageBooksInfo(booksMst,oldPackageBookIds)
            dataProviderService.refreshCacheForPublishUnpublish(params.bookId,getSiteId(request))

        }else {
            dataProviderService.getTestSeriesDtl(params.bookId)
//            dataProviderService.getUnpublishedBooksForAdmin(getSiteId(request))
//            dataProviderService.getPublishedBooksListForAdmin(getSiteId(request))
            dataProviderService.getWSUnpublishedMyBooks()
            dataProviderService.getWSPublishedMyBooks()
        }
        if(booksMst.publisherId!=null){
            dataProviderService.getBooksListForPubDeskForPublisher(booksMst.publisherId)
            dataProviderService.getPublisherLatestBooks(getSiteId(request), booksMst.publisherId)
            dataProviderService.getPublisherBookCategories(utilService.getSiteId(request, session), booksMst.publisherId)
            if(redisService.("tagsforbooks_" + booksMst.id)!=null) {
                List<BooksTagDtl> dtlList = new JsonSlurper().parseText(redisService.("tagsforbooks_" + booksMst.id))
                for (int i = 0; i < dtlList.size(); i++) {
                    dataProviderService.getNewBooksList(
                            redisService.("siteIdList_" + getSiteId(request)),
                            getSiteId(request),
                            dtlList[i].level,
                            dtlList[i].syllabus,
                            dtlList[i].grade,
                            ("" + dtlList[i].level + '' + dtlList[i].syllabus + '' + dtlList[i].grade + '_' + booksMst.publisherId).replaceAll("\\s+", ""),
                            false,
                            booksMst.publisherId)
                }
            }
        }

        if("published".equals(booksMst.status)) dataNotificationService.latestBooksChanged(params.bookId)

        render json as JSON
    }

    def bookPubDtUpdate() {
        if (params.bookId==null) return

        BooksMst.executeUpdate("update BooksMst set datePublished=STR_TO_DATE('"+params.pubYear+"-"+params.pubMonth+"-01', '%Y-%m-%d') where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set datePublished=STR_TO_DATE('"+params.pubYear+"-"+params.pubMonth+"-01', '%Y-%m-%d') where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set datePublished=STR_TO_DATE('"+params.pubYear+"-"+params.pubMonth+"-01', '%Y-%m-%d') where id=" + params.bookId)

        def json =  ['status':  "OK"]
        render json as JSON
    }

    def uploadBookCover() {
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')
        BooksMst booksMst
        if (file.empty) {
            flash.message = "File cannot be empty"
        } else {
            String filename=file.originalFilename
            filename=filename.replaceAll("\\s+","")
            booksMst = BooksMst.findById(new Long(params.bookId))
            String uploadParentDir="supload"
            String cdnPath = ""+grailsApplication.config.grails.cdn.path
             File uploadDir = new File(uploadParentDir+"/books/" + params.bookId)
            if (!uploadDir.exists()) uploadDir.mkdirs()

            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()

            //step 1. check if the image is webp



            BufferedImage image = ImageIO.read(file.getInputStream())

            //saving original image finally
         //   file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
            String webPImage=filename.substring(0, filename.indexOf("."))
            ImageIO.write(image, "webp", new File(uploadParentDir+"/books/"+booksMst.id+"/processed/"+webPImage+".webp"));

            File webpFile = new File(uploadParentDir+"/books/"+booksMst.id+"/processed/"+webPImage+".webp")
            filename = webPImage+".webp"
            image = ImageIO.read(webpFile)
            ByteArrayOutputStream baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 150, 150, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte = baos.toByteArray()
            baos.close()

            baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 300, 300, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte1 = baos.toByteArray()
            baos.close()

            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)

//check to see if the size of the generated file is greater than the source file

            long sourceFileLength = webpFile.length()
            File passportFile = new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf(".")))
            long passportFileLength = passportFile.length()

            if(passportFileLength>sourceFileLength){
                FileInputStream inputStream = new FileInputStream(webpFile);
                FileOutputStream outputStream = new FileOutputStream(passportFile);

                byte[] buffer = new byte[1024];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                // Close the input and output streams
                inputStream.close();
                outputStream.close();
            }
            if(!"localhost".equals(cdnPath)) filename = cdnPath + "/supload/books/" + params.bookId + "/processed/" + filename

            if(params.imgType=="cover") {
                BooksMst.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + params.bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + params.bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + params.bookId)

            } else {
                BooksMst.executeUpdate("update BooksMst set headerImage='" + filename + "' where id=" + params.bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set headerImage='" + filename + "' where id=" + params.bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set headerImage='" + filename + "' where id=" + params.bookId)
            }

            dataProviderService.getBooksMst(booksMst.id)
        }
        if("sage".equals(session["entryController"]))  {
            redirect(controller: "wonderpublish", action: "bookCreate", params: [bookId: booksMst.id, booksMst: booksMst, smartEbook: ("true".equals(params.smartEBook)) ? true : false])
        }
        else {
            redirect(controller: "wonderpublish", action: "bookCreateNew", params: [bookId: booksMst.id, booksMst: booksMst, smartEbook: ("true".equals(params.smartEBook)) ? true : false])
        }
    }

    def uploadDisciplineCover() {
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')
        SiteMst siteMst
        if (file.empty) {
            flash.message = "File cannot be empty"
        } else {
            String filename=file.originalFilename
            filename=filename.replaceAll("\\s+","")
            siteMst = SiteMst.findById(session["siteId"])
            File uploadDir = new File("upload/site/" + session["siteId"])
            if (!uploadDir.exists()) uploadDir.mkdirs()

            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()

            BufferedImage image = ImageIO.read(file.getInputStream())

            ByteArrayOutputStream baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte = baos.toByteArray()
            baos.close()

            baos = new ByteArrayOutputStream()
            ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
            baos.flush()
            byte[] scaledImageInByte1 = baos.toByteArray()
            baos.close()

            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
            FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)

            siteMst.displineCoverImage = filename
            //saving original image finally
            file.transferTo(new File(uploadDir.absolutePath + "/" + filename))

            siteMst.save(failOnError: true, flush: true)
            dataProviderService.getBooksMst(siteMst.id)
        }

        redirect(controller: "wonderpublish", action: "pubDesk")
    }

    def updateInstructorDescription(){
        SiteMst siteMst = dataProviderService.getSiteMst(session["siteId"])
        siteMst.teachingNotes = params.teachingNotes
        siteMst.teachingSlides = params.teachingSlides
        siteMst.sampleChapters = params.sampleChapters
        siteMst.mediaLinks = params.mediaLinks
        siteMst.exercises = params.exercises
        siteMst.save(failOnError: true, flush: true)
        dataProviderService.getBooksMst(siteMst.id)
        redirect(controller: "wonderpublish", action: "pubDesk")

    }
    @Transactional
    def updateAuthorNew(){
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        String authorNames=""
        if ((""+session["userdetails"].publisherId).equals(""+booksMst.publisherId)||session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_ADMIN"}||session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR"}) {
            //check if add or remove
            if("remove".equals(params.authorMode)){
                BooksAuthorDtl booksAuthorDtl = BooksAuthorDtl.findByBookIdAndAuthorId(booksMst.id, new Long(params.authorId))
                if(booksAuthorDtl!=null) booksAuthorDtl.delete()

            }
            else{
                BooksAuthorDtl booksAuthorDtl = new BooksAuthorDtl(bookId: new Long(params.bookId), authorId: new Long(params.authorId))
                booksAuthorDtl.save(failOnError: true, flush: true)
            }
            dataProviderService.getAuthorsForBook(new Long(params.bookId))
            List authors = BooksAuthorDtl.findAllByBookId(new Long(params.bookId))



            authors.each {author1->
                Authors author = Authors.findById(author1.authorId)
                authorNames +=author.name+","
            }
            if(authors.size()>0) authorNames=authorNames.substring(0,(authorNames.length()-1))
            BooksMst.executeUpdate("update BooksMst set authors ='" + authorNames + "' where id=" + params.bookId)
            BooksMst.wsshop.executeUpdate("update BooksMst set authors ='" + authorNames + "' where id=" + params.bookId)
            BooksMst.wsuser.executeUpdate("update BooksMst set authors ='" + authorNames + "' where id=" + params.bookId)

        }
        def json =  [
                'bookId': params.bookId
        ]

        render json as JSON
    }
    def updateAuthors() {
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        if ((""+session["userdetails"].publisherId).equals(""+booksMst.publisherId)||session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_ADMIN"}||session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR"}) {
            //first remove existing records
            BooksAuthorDtl.executeUpdate("delete BooksAuthorDtl where bookId=" + params.bookId)

            String authorIds = params.authors

            BooksMst.executeUpdate("update BooksMst set authors ='" + params.optionAuthors + "' where id=" + params.bookId)
            BooksMst.wsshop.executeUpdate("update BooksMst set authors ='" + params.optionAuthors + "' where id=" + params.bookId)
            BooksMst.wsuser.executeUpdate("update BooksMst set authors ='" + params.optionAuthors + "' where id=" + params.bookId)
             if (authorIds.length() > 0) {
                if (authorIds.indexOf(',') == -1) {
                    //single author
                    BooksAuthorDtl booksAuthorDtl = new BooksAuthorDtl(bookId: new Long(params.bookId), authorId: new Long(authorIds))
                    booksAuthorDtl.save(failOnError: true, flush: true)
                } else {
                    //multiple author
                    def authorId = authorIds.split(",")
                    for (int i = 0 ;i < authorId.length; i++) {
                        BooksAuthorDtl booksAuthorDtl = new BooksAuthorDtl(bookId: new Long(params.bookId), authorId: new Long(authorId[i]))
                        booksAuthorDtl.save(failOnError: true, flush: true)
                    }
                }

                dataProviderService.getAuthorsForBook(new Long(params.bookId))
            }
        }

        def json =  [
            'bookId': params.bookId
        ]

        render json as JSON
    }

    @Transactional
    def addAuthor() {
        BooksMst booksMst
        Authors authors = new Authors(name: params.author, publisherId: new Long(params.publisherId))
        authors.save(failOnError: true, flush: true)
        dataProviderService.getAuthorsForPublishers(new Long(params.publisherId))
        def json =  [
                'authorId': authors.id, 'author': authors.name
        ]

        render json as JSON
    }

    def navheader() {}

    def chapterUpdate() {
        ChaptersMst chaptersMst
        def newChapter="false"
        def sortOrderCount
        def chapterId
        def columnValue= (params.columnValue);
        def exceptionHappened=true;
        def json
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        ChaptersMst chaptersMst1 = ChaptersMst.findByBookIdAndSortOrderIsNotNull(new Long(params.bookId),[sort: "sortOrder", order: "desc"])
        if(chaptersMst1 != null)  sortOrderCount=chaptersMst1.sortOrder+1;

        if (params.chapterId == "-1") { //new chapter
            chaptersMst = new ChaptersMst(name: columnValue, bookId: new Long(params.bookId),sortOrder:sortOrderCount!=null && sortOrderCount!=""?sortOrderCount:null )
            chaptersMst.save(failOnError: true)
            chapterId = chaptersMst.id
            newChapter = "true"
        } else { //update
            columnValue= StringEscapeUtils.escapeSql(columnValue);
            ChaptersMst.executeUpdate("update ChaptersMst set " + params.columnName + "='" + columnValue + "' where id=" + params.chapterId)
            chapterId = params.chapterId
            if(siteMst.id==1 && params.columnName=="name") exceptionHappened=utilService.addRelatedVideos(Long.parseLong(chapterId),columnValue,newChapter,siteMst)
            metainfoService.getAllChaptersMetaInfo(params.bookId)
        }
        dataProviderService.getChaptersList(new Long(params.bookId))
        if(siteMst.id==1) {
            json = [
                    'chapterId': chapterId, 'newChapter': newChapter, 'exceptionHappened': exceptionHappened
            ]
        }
        else{
            json = [
                    'chapterId': chapterId, 'newChapter': newChapter
            ]

        }
        render json as JSON
    }

    def deleteChapter(){
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(params.chapterId))
        if(chaptersMst!=null){
            Long bookId = chaptersMst.bookId
            chaptersMst.bookId = new Long(bookId.intValue()*-1)
            chaptersMst.save(flush:true,failOnError: true)
            dataProviderService.getChaptersList(bookId)
            metainfoService.getAllChaptersMetaInfo(bookId)
        }
    }

    def previewChapterUpdate(){
        ChaptersMst.executeUpdate("update ChaptersMst set previewChapter=null where bookId=" + params.bookId)
        if(params.chapterId!=null&&!"".equals(params.chapterId)) ChaptersMst.executeUpdate("update ChaptersMst set previewChapter='true' where id=" + params.chapterId)
        dataProviderService.getChaptersList(new Long(params.bookId))
        dataProviderService.refreshCacheForPublishUnpublish(params.bookId,getSiteId(request))
        def json =  [
                'chapterId': params.chapterId
        ]

        render json as JSON
    }

    def chapterMstDetails() {
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(params.chapterId))

        def json =  [
                'chapterDesc': chaptersMst.chapterDesc, 'chapterName':chaptersMst.name
        ]

        render json as JSON
    }








    def boolean validateXmlFile(String filePath){
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance()

        try {
            DocumentBuilder db = dbf.newDocumentBuilder()
            db.parse(filePath)
        } catch (ParserConfigurationException pce) {
            return false
        } catch (SAXException se) {
            return false
        } catch (IOException ioe) {
            return false
        }

        return true
    }

    def parseXmlFile1(String filePath){
        //get the factory
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance()

        try {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            def resourceDtlInstance = new ResourceDtl()

            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType.replace(" XML","")
            resourceDtlInstance.chapterId = new Integer(params.chapterId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true, flush: true)
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
            def quizId = quizIdGenerator.id
            def resourceDtlId = resourceDtlInstance.id

            //Using factory get an instance of document builder
            DocumentBuilder db = dbf.newDocumentBuilder()

            //parse using builder to get DOM representation of the XML file
            dom = db.parse(filePath)
            v = new Vector<String>()

            NodeList nodes = dom.getElementsByTagName("question")

            for (int i = 0 ;i < nodes.getLength(); i++) {
                Node tempNode = nodes.item(i)
                boolean skiprow = false

                def question=""
                def option1=""
                def option2=""
                def option3=""
                def option4=""
                def answer=0
                def cnt=0
                def filename=""
                def resLink=""
                boolean isImage1=false, isImage2=false
                byte[] decodedBytes=null

                // processing question node
                if (tempNode.getNodeType() == Node.ELEMENT_NODE && tempNode.hasAttributes()) {
                    NamedNodeMap nodeMap = tempNode.getAttributes()
                    Node node

                    for (int j = 0 ;j < nodeMap.getLength(); j++) {
                        node = nodeMap.item(j)

                        if(node.getNodeName().equals("type") && node.getNodeValue().equals("category")) {
                            skiprow = true
                            break
                        }
                    }

                    if(skiprow) continue

                    // processing question child notes
                    if(tempNode.hasChildNodes()) {
                        NodeList nodes1 = tempNode.getChildNodes()
                        for (int j = 0; j < nodes1.getLength(); j++) {
                            Node tempNode1 = nodes1.item(j)

                            if (tempNode1.getNodeType() == Node.ELEMENT_NODE) {
                                if(tempNode1.getNodeName().equals("questiontext")) {
                                    cnt=0
                                    question=tempNode1.getTextContent().trim()
                                    if(question.indexOf("<img")!=-1) question = question.substring(0,question.indexOf("<img"))

                                    //handling images
                                    if(tempNode1.hasChildNodes()) {
                                        NodeList nodes2 = tempNode1.getChildNodes()
                                        for (int k = 0; k < nodes2.getLength(); k++) {
                                            Node tempNode2 = nodes2.item(k)

                                            if (tempNode2.getNodeType() == Node.ELEMENT_NODE && tempNode2.getNodeName().equals("file")) {
                                                NamedNodeMap nodeMap3 = tempNode2.getAttributes()
                                                Node node2

                                                isImage1=false
                                                isImage2=false

                                                for (int m = 0 ;m < nodeMap3.getLength(); m++) {
                                                    node2 = nodeMap3.item(m)
                                                    if(node2.getNodeName().equals("path") && node2.getNodeValue().equals("/")) isImage1=true
                                                    if(node2.getNodeName().equals("encoding") && node2.getNodeValue().equals("base64")) isImage2=true
                                                    if(node2.getNodeName().equals("name")) filename=node2.getNodeValue()
                                                }

                                                decodedBytes = Base64.getDecoder().decode(tempNode2.getTextContent().getBytes())
                                            }
                                        }
                                    }
                                }

                                if(tempNode1.getNodeName().equals("answer")) {
                                    cnt++
                                    NamedNodeMap nodeMap1 = tempNode1.getAttributes()
                                    Node node1
                                    if(cnt==1) option1=tempNode1.getTextContent().trim()
                                    if(cnt==2) option2=tempNode1.getTextContent().trim()
                                    if(cnt==3) option3=tempNode1.getTextContent().trim()
                                    if(cnt==4) option4=tempNode1.getTextContent().trim()

                                    for (int k = 0 ;k < nodeMap1.getLength(); k++) {
                                        node1 = nodeMap1.item(k)

                                        if(node1.getNodeName().equals("fraction") && node1.getNodeValue().equals("100")) {
                                            answer=cnt
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if(question!="" && option1!="" && option2!="" && option2!="" && option4!="" && answer!=0) {
                    ObjectiveMst om = new ObjectiveMst(quizId: quizId, quizType: params.resourceType.replace(" XML",""), question: question,
                            option1: option1,
                            option2: option2,
                            option3: option3,
                            option4: option4,
                            answer1: answer==1?"Yes":null,
                            answer2: answer==2?"Yes":null,
                            answer3: answer==3?"Yes":null,
                            answer4: answer==4?"Yes":null)
                    om.save(failOnError: true, flush: true)
                    def objectiveMstId = om.id

                    //image present writing to disc and updating database
                    if(!filename.equals("") && isImage1 && isImage2) {
                        resLink = "upload/quiz/"+objectiveMstId+"/"+filename

                        try {
                            File uploadDir = new File("upload/quiz/" + objectiveMstId)
                            if (!uploadDir.exists()) uploadDir.mkdirs()
                            FileUtils.writeByteArrayToFile(new File(grailsApplication.config.grails.basedir.path+resLink), decodedBytes)
                            om.questionFilepath = filename!=""?resLink:null
                            om.questionFilename = filename!=""?filename:null
                            om.save(failOnError: true, flush: true)
                        } catch (Exception e) {
                            //nothing to do
                        }
                    }
                }
            }
        } catch(ParserConfigurationException pce) {
            pce.printStackTrace()
        } catch(SAXException se) {
            se.printStackTrace()
        } catch(IOException ioe) {
            ioe.printStackTrace()
        }
    }







    def getHtmlCount(){
        def sql="select count(*) from resource_dtl_sub where resource_id="+params.resId
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        def count=0
        results.each { result ->
            count=count+result[0]
        }

        def json =  [
                'count':  count,
                'status':  count>0 ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    def getHtml(){
        def sql="select filedata,filename from resource_dtl_sub where resource_id="+
                params.resId+" and id=((select min(id) from resource_dtl_sub where resource_id="+
                params.resId+")-1+"+params.fileNo+")"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List htmls = results.collect { result ->
            //blob in db is a byte array
            return [filedata: new String(result[0], "UTF-8"), filename: result[1]]
        }

        def json =  [
                'htmls':htmls,
                'status' : htmls ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    @Transactional
    def bookdtl(){
        if("books".equals(params.siteName)||"books".equals(""+session['entryController']) || "21".equals(""+session['siteId'])){
            forward(controller: 'resources', action:'eBookDtl')
        }else {
            def bookId = null
            def chapterId = null
            def resId = null

            if (params.bookId != null)
                bookId = new Long(params.bookId)
            else if (request.JSON.bookId != null)
                bookId = new Long(request.JSON.bookId)

            if (params.resId != null) {
                resId = new Long(params.resId)
                chapterId = new Long(params.chapterId)
            } else {
                if (request.JSON.resId != null) {
                    resId = new Long(request.JSON.resId)
                    chapterId = new Long(request.JSON.chapterId)
                }
            }

            if (resId != null) {
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': bookId, 'chapterId': chapterId, 'resId': resId])
            } else {
                BooksMst book = dataProviderService.getBooksMst(bookId)
                if (redisService.("authors_" + bookId) == null) {
                    dataProviderService.getAuthors(bookId)
                }

                def authors = redisService.("authors_" + bookId)

                Publishers publisher = dataProviderService.getPublisher(book.publisherId)
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(bookId)

                if (redisService.("bookHeader_" + book.id) == null) {
                    dataProviderService.getBookHeader(book.id)
                }

                String pattern = "yyyy/MM/dd HH:mm:ss"
                SimpleDateFormat sdfFrom = new SimpleDateFormat(pattern)
                String serverTime = sdfFrom.format(convertDate(Calendar.getInstance().getTime(), "UTC", "IST"))
                //logic for addind the seo friendly title
                String seoFriendlyTitle = book.title
                //check if wonderslate is there in the title
                if ("books".equals(session["entryController"])) {
                    if (seoFriendlyTitle.indexOf("Wonderslate") == -1 && (seoFriendlyTitle.indexOf("wonderslate") == -1)) {
                        seoFriendlyTitle += " - Wonderslate"
                    }

                    //this is for non school books
                    if (booksTagDtl != null) {
                        if ("Competitive Exams".equals(booksTagDtl.level)) {
                            if (seoFriendlyTitle.indexOf(booksTagDtl.subject) == -1) seoFriendlyTitle = booksTagDtl.subject + " " + seoFriendlyTitle
                            if (seoFriendlyTitle.indexOf(booksTagDtl.grade) == -1) seoFriendlyTitle = booksTagDtl.grade + " " + seoFriendlyTitle
                        }

                        if ("College".equals(booksTagDtl.level)) {
                            if (seoFriendlyTitle.indexOf(booksTagDtl.subject) == -1) seoFriendlyTitle = booksTagDtl.subject + " " + seoFriendlyTitle
                            if (seoFriendlyTitle.indexOf(booksTagDtl.syllabus) == -1) seoFriendlyTitle = booksTagDtl.syllabus + " " + seoFriendlyTitle
                        }
                    }

                }
                asyncLogsService.updateBookView(params.bookId, "web", "store", getSiteId(request),
                        (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),null)
                boolean inLibrary = false
                if (springSecurityService.currentUser != null) inLibrary = utilService.userHasBookAccess(book.id, springSecurityService.currentUser.username)

                [
                        book             : book,
                        authors          : authors,
                        customBreadCrumbs: "true",
                        addlMenu         : "yes",
                        booksTag         : booksTagDtl,
                        bookId           : params.bookId,
                        newCss           : true,
                        publisherName    : publisher != null ? publisher.name : "",
                        header           : redisService.("bookHeader_" + book.id),
                        title            : seoFriendlyTitle,
                        hideBottomIcons  : true,
                        hideSearch       : true,
                        serverTime       : serverTime,
                        'showTestsHeader': "eutkarsh".equals(session["entryController"]),
                        inLibrary        : inLibrary
                ]
            }
        }
    }
    @Transactional
    def getBookDetails(){
        def bookId
        def siteId = null

        if(params.bookId!=null){
            bookId = params.bookId
            if(session["siteId"]!=null)
                siteId = ""+session["siteId"]
            else siteId = params.siteId

        } else {
            def jsonObj = request.JSON
            bookId = jsonObj.bookId
            siteId = jsonObj.siteId
            asyncLogsService.updateBookView(bookId,"mobile","store",getSiteId(request),
                    (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),null)

        }
        if("optimized".equals(params.mode)){
            if (redisService.("deepLinkChapterDetails_" + bookId) == null) {
                dataProviderService.getBookDetails(bookId)
            }
            def json = [bookDetails:redisService.("deepLinkChapterDetails_" + bookId)]
            render json as JSON
        }
        else {
            if (siteId == null) siteId = "1"

            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

            if (redisService.("packageBooksByMainBookId_" + bookId) == null) {
                dataProviderService.packageBooksByMainBookId(new Long(bookId))
            }
            List packageBooks= new JsonSlurper().parseText(redisService.("packageBooksByMainBookId_" + bookId))

            if (redisService.("authors_" + bookId) == null) {
                dataProviderService.getAuthors(new Long(bookId))
            }
            def authors = redisService.("authors_" + bookId)
            Publishers publisher = null

            List reviewRatings = []

            Double eBookPrice =  null
            Double eBookListPrice = null

            List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
            bookPriceDtls.each{ bookPrice->
                if("eBook".equals(bookPrice.bookType)){
                    eBookPrice = bookPrice.sellPrice
                    eBookListPrice = bookPrice.listPrice
                }

            }

            def json = [
                    'bookDesc': booksMst != null ? booksMst.description : null, 'price': eBookPrice, 'authors': authors, 'rating': null,
                    'isbn'    : booksMst != null ? booksMst.isbn : null, 'coverImage': booksMst != null ? booksMst.coverImage : null, 'publisher': publisher,
                    'bookId'  : booksMst.id, 'title': booksMst.title, 'listPrice': eBookListPrice, 'testTypeBook': booksMst.testTypeBook, reviewratings: reviewRatings, showInLibrary: booksMst.showInLibrary != null ? booksMst.showInLibrary : "",
                    buylink1  : booksMst.buylink1, buylink2: booksMst.buylink2, rating1: booksMst.reviewLink1, rating2: booksMst.reviewLink2, status: booksMst.status,showDiscount: booksMst.showDiscount,packageBooks:packageBooks
            ]

            render json as JSON
        }
    }
    @Transactional
    def getNewBookDetails(){
        def bookId
        def siteId = null

        if(params.bookId!=null){
            bookId = params.bookId
            if(session["siteId"]!=null)
                siteId = ""+session["siteId"]
            else siteId = params.siteId

        } else {
            def jsonObj = request.JSON
            bookId = jsonObj.bookId
            siteId = jsonObj.siteId
            asyncLogsService.updateBookView(bookId,"mobile","store",getSiteId(request),
                    (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),null)

        }
        if("optimized".equals(params.mode)){
            if (redisService.("deepLinkChapterDetails_" + bookId) == null) {
                dataProviderService.getBookDetails(bookId)
            }
            def json = [bookDetails:redisService.("deepLinkChapterDetails_" + bookId)]
            render json as JSON
        }
        else {
            if (siteId == null) siteId = "1"

            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

            if (redisService.("authors_" + bookId) == null) {
                dataProviderService.getAuthors(new Long(bookId))
            }
            def authors = redisService.("authors_" + bookId)

            List reviewRatings = []
            def json = [
                    'bookDesc': booksMst != null ? booksMst.description : null, 'price': booksMst != null ? booksMst.price : null, 'authors': authors, 'rating': null,
                    'isbn'    : booksMst != null ? booksMst.isbn : null, 'coverImage': booksMst != null ? booksMst.coverImage : null,
                    'bookId'  : booksMst.id, 'title': booksMst.title, 'listPrice': booksMst.listprice, 'testTypeBook': booksMst.testTypeBook, reviewratings: reviewRatings,
                    buylink1  : booksMst.buylink1, buylink2: booksMst.buylink2, rating1: booksMst.reviewLink1, rating2: booksMst.reviewLink2, status: booksMst.status
            ]

            render json as JSON
        }
    }





    def getBooksList(){
        def sql
        def json

        Integer siteId = getSiteId(request)
        String siteIdList=siteId.toString()

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                SiteMst siteMst = SiteMst.findById(siteId)
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }


        if("true".equals(params.categories)){
            String keyName = params.level.replaceAll("\\s+","")

            if(!"null".equals(params.syllabus)) {
                keyName = keyName +"_"+ params.syllabus.replaceAll("\\s+","")
            }
            if(!"null".equals(params.grade)) {
                keyName = keyName +"_"+ params.grade.replaceAll("\\s+","")
            }
            if(!"null".equals(params.subject)) {
                keyName = keyName +"_"+ params.subject.replaceAll("\\s+","")
            }
            if(redisService.("booksList_"+getSiteId(request)+"_"+keyName.replaceAll("\\s+",""))==null) {
                dataProviderService.getBooksList(siteIdList,siteId,params.level,params.syllabus,params.grade,params.subject,keyName)
            }


            if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
                dataProviderService.getBooksTagList(params.level)
            }

            def books,booksTagDtl
            if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)||"optimized".equals(params.apiMode)) {
                books = redisService.("booksList_" + getSiteId(request) + "_" + keyName.replaceAll("\\s+", ""))
                booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))
            }else{
                books = new JsonSlurper().parseText(redisService.("booksList_"+getSiteId(request)+"_"+keyName.replaceAll("\\s+","")))
                booksTagDtl = new JsonSlurper().parseText(redisService.("bookstag_"+params.level.replaceAll("\\s+","")))
            }
            json = [
                    'books':books,
                    'status' : books ? "OK" : "Nothing present",
                    'booksTag' : booksTagDtl,
                    'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                    'totalBooks':books.size()
            ]
        } else if("level".equals(params.categories)){
            if(redisService.("booksList_"+params.level.replaceAll("\\s+","")+"_"+getSiteId(request))==null) {
                dataProviderService.getBooksList(siteIdList,siteId,params.level)
            }



            if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
                dataProviderService.getBooksTagList(params.level)
            }
            def books,booksTagDtl
            if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)||"optimized".equals(params.apiMode)) {
                books = redisService.("booksList_" + params.level.replaceAll("\\s+", "") + "_" + getSiteId(request))
                booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))
            }else{
                books = new JsonSlurper().parseText(redisService.("booksList_"+params.level.replaceAll("\\s+","")+"_"+getSiteId(request)))
                booksTagDtl = new JsonSlurper().parseText(redisService.("bookstag_"+params.level.replaceAll("\\s+","")))
            }
            json =
                    [
                            'books':books,
                            'status' : books ? "OK" : "Nothing present",
                            'booksTag' : booksTagDtl,
                            'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                            'totalBooks':books.size()
                    ]
        } else {
            if(redisService.("booksList_"+getSiteId(request))==null) {
                dataProviderService.getBooksList(siteIdList,siteId,null)
            }

            List books = new JsonSlurper().parseText(redisService.("booksList_"+getSiteId(request)))
            int startIndex=0
            int noOfBooksRequired=0
            if(params.startIndex!=null) startIndex = Integer.parseInt(params.startIndex)
            if(params.noOfBooksRequired!=null) noOfBooksRequired = Integer.parseInt(params.noOfBooksRequired) else noOfBooksRequired = books.size()-1
            List tempBooks
            if(books.size()>0) {
                if ((startIndex + noOfBooksRequired) > books.size())
                    tempBooks = books.subList(startIndex, (books.size()))
                else
                    tempBooks = books.subList(startIndex, (startIndex + noOfBooksRequired))
            }
            json = [
                    'books':tempBooks,
                    'status' : books ? "OK" : "Nothing present",
                    'totalBooks':books.size(),
                    'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST")
            ]
        }

        render json as JSON
    }

    def getBooksListEvidya(){
        def sql
        def json


            if(redisService.("booksList_"+getSiteId(request))==null) {
                dataProviderService.getBooksListEvidya(getSiteId(request))
            }

            if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
                dataProviderService.getBooksTagList('College')
            }
            def books,booksTagDtl
                books = redisService.("booksList_"+getSiteId(request))
                booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))

            json =
                    [
                            'books':books,
                            'status' : books ? "OK" : "Nothing present",
                            'booksTag' : booksTagDtl
                    ]

        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getBooksListForUser(){
        if("refresh".equals(params.mode)) dataProviderService.getBooksListForUser()
        if(redisService.(springSecurityService.currentUser.username+"_"+"booksList")==null||"null".equals(redisService.(springSecurityService.currentUser.username+"_"+"booksList"))) {
            dataProviderService.getBooksListForUser()
        }
        def booksList
        if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)||"optimized".equals(params.apiMode)) {
            booksList = redisService.(springSecurityService.currentUser.username + "_" + "booksList")
        }else{
            booksList =  new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username+"_"+"booksList"))
        }


        if(redisService.("lastReadBooks_"+springSecurityService.currentUser.username)==null){
            dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
        }

        def json = [
                'books':booksList,
                'status' : booksList ? "OK" : "Nothing present",
                'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                'lastReadBooks':redisService.("lastReadBooks_"+springSecurityService.currentUser.username)
        ]

        render json as JSON
    }

    @Transactional
    def getUserBooksForTestGenerator(){
        Integer siteId = getSiteId(request)
        List booksList
        String defaultBooks="false"
        if(params.batchId!=null&&params.batchId.length()>0){
            String pageNo="1"
            if (redisService.("instituteLibraryBooklist_" + params.batchId + "_page_"+pageNo) == null) libraryBooksService.getInstituteBooksPagination(params.batchId,Integer.parseInt(pageNo))
            booksList =new JsonSlurper().parseText(redisService.("instituteLibraryBooklist_" + params.batchId + "_page_" + pageNo))

        }
        else if(siteId.intValue()==1){
            if (redisService.("userShelfBooks_" + springSecurityService.currentUser.username) == null || redisService.("userShelfBooks_" + springSecurityService.currentUser.username)=="null") {
                wsLibraryCacheService.userShelfBooks(springSecurityService.currentUser.username)
            }
            booksList =new JsonSlurper().parseText(redisService.("userShelfBooks_" + springSecurityService.currentUser.username))
        }else{
            if(redisService.(springSecurityService.currentUser.username+"_"+"booksList")==null) {
                dataProviderService.getBooksListForUser()
            }
            booksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username+"_"+"booksList"))
        }
        List books =[]

        booksList.each { book ->
            if (redisService.("quizPresent_" + book.id) == null) dataProviderService.quizPresent(new Long(book.id))
            if ("Present".equals(redisService.("quizPresent_" + book.id))) {
                books << book

            }
        }
        if(books.size()==0&&siteId.intValue()==1){
            //get the default test generator books
            if(redisService.("testGeneratorBookIds_"+siteId)==null) dataProviderService.getDefaultTestGeneratorBookIds(siteId)
            if(redisService.("getBookDetailsForBookIds"+"_"+siteId) == null ) dataProviderService.getBookDetailsForBookIds(redisService.("testGeneratorBookIds_"+siteId),siteId)
            booksList = new JsonSlurper().parseText(redisService.("getBookDetailsForBookIds"+"_"+siteId))
            defaultBooks = "true"
            booksList.each { book ->
                if (redisService.("quizPresent_" + book.id) == null) dataProviderService.quizPresent(new Long(book.id))
                if ("Present".equals(redisService.("quizPresent_" + book.id))) {
                    books << book

                }
            }
        }
        def json =[
                'books': books.unique(),
                'status': books ? "OK" : "Nothing present",
                'defaultBooks' : defaultBooks
        ]

        render json as JSON

    }
    @Transactional
    def getBooksListForUserForBatch(){
        Integer siteId = getSiteId(request)
        if(redisService.(springSecurityService.currentUser.username+"_"+"booksList")==null) {
            dataProviderService.getBooksListForUser()
        }
        List booksList
        if(params.batchId!=null&&params.batchId.length()>0){
            booksList = BooksBatchDtl.findAllByBatchId(new Long(params.batchId))
        }else{
            if(siteId==1){
                String username=springSecurityService.currentUser.username;
                booksList = wsLibraryService.getUsersInsBooksByUsername(username);
                if(booksList == null) {
                    wsLibraryService.userShelfBooks(username);
                    booksList = wsLibraryService.getUsersInsBooksByUsername(username)
                    booksList == null ? new ArrayList<>():booksList
                }
            }else{
                booksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username+"_"+"booksList"))
            }


        }

        String sql = "select distinct(book_id) from wslog.quizrecorder where username='"+springSecurityService.currentUser.username+"' and book_id is not null"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List testsTaken = results.collect { test ->
            return [bookId: test[0]]
        }

        List books=[]

        booksList.each{book->
            BooksMst booksMst
            if (params.batchId != null && params.batchId.length() > 0)
                booksMst = dataProviderService.getBooksMst(new Long(book.bookId))
            else booksMst = dataProviderService.getBooksMst(new Long(book.id))
           if(!"test".equals(booksMst.bookType)) {

               if (redisService.("test_series_" + booksMst.id) == null) dataProviderService.getTestSeriesDtl("" + booksMst.id)
               List quizDetails = []
               if (redisService.("test_series_" + booksMst.id) != null) quizDetails = new JsonSlurper().parseText(redisService.("test_series_" + booksMst.id))

               books.add(id: booksMst.id, title: booksMst.title, coverImage: booksMst.coverImage,
                       listPrice: booksMst.listprice, offerPrice: booksMst.price, rating: booksMst.rating, testStartDate: ("" + booksMst.testStartDate),
                       testEndDate: ("" + booksMst.testEndDate), totalMarks: quizDetails.size() > 0 ? quizDetails[0].totalMarks : null,
                       noOfQuestions: quizDetails.size() > 0 ? quizDetails[0].noOfQuestions : null,
                       language1: quizDetails.size() > 0 ? quizDetails[0].language1 : null, language2: quizDetails.size() > 0 ? quizDetails[0].language2 : null, bookType: booksMst.bookType,
                       resourceId: quizDetails.size() > 0 ? quizDetails[0].resourceID : null, quizId: quizDetails.size() > 0 ? quizDetails[0].quizId : null)
           }
        }



        def json = [
                'books':books.unique(),
                'status' : books ? "OK" : "Nothing present",
                'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                'testsTaken':testsTaken
        ]

        render json as JSON
    }

    def getBooksListForUserSage(){
        List books
        BooksMst booksMst = BooksMst.findByIsbn(session["isbn"])
        if(params.tags!=null&&booksMst!=null){
            String [] tags = params.tags.split(",")
            String optStr
            if(tags.length==1) optStr = "tags like ('%"+tags[0]+"')"
            else if(tags.length==2) optStr = "tags like ('%"+tags[0]+"')"+" or "+"tags like ('%"+tags[1]+"')"
            else if(tags.length==3) optStr = "tags like ('%"+tags[0]+"')"+" or "+"tags like ('%"+tags[1]+"')" +" or "+"tags like ('%"+tags[2]+"')"

            String sql = "select id,cover_image,title from books_mst where discipline='"+booksMst.discipline+"' and status='published' and ("+optStr+")"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)

             books = results.collect { book ->
                return [id: book[0],coverImage:book[1],title:book[2]]
            }

        }else {
            books = BooksMst.findAllByIsbnAndStatus(session["isbn"],"published")
        }

        def json = [
                'books':books,
                'status' : books ? "OK" : "Nothing present"
        ]

        render json as JSON
    }


    def authorDetails(){}
    @Secured(['ROLE_USER']) @Transactional
    def testgenerator(){
        boolean isInstructor = false
        String username = springSecurityService.currentUser.username
        String ipAddress
        if("yes".equals(params.app) && !"".equals(params.ipAddress)){
            ipAddress=params.ipAddress
        }else{
            ipAddress = utilService.getIPAddressOfClient(request)
        }
        List institutesList = userManagementService.getInstitutesForUser(getSiteId(request),ipAddress)
        //go through the list and see if the userType is Instructor or Manager. If so set the flag isInstructor to true
        institutesList.each{institute->
            if("Instructor".equals(institute.userType)||"Manager".equals(institute.userType)){
                isInstructor = true
            }
        }

        [
                    newCss : true,
                    testGenCss : true,
                    hideSearch : true,
                    instructor: isInstructor,
                    institutes: institutesList
            ]


    }

    def testgeneratorscripts(){
    }


    def displayChapterDetails(){}
    def testgenModal(){}

    def  Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }
    @Transactional
    def reviewrating(){
        BookRatingReviews bookRatingReviews = BookRatingReviews.findByBookIdAndUsername(new Long(params.bookId),springSecurityService.currentUser.username)
        String rating,review

        if(bookRatingReviews==null){
            rating=-1
            review=-1
        } else {
            rating = bookRatingReviews.rating
            review = bookRatingReviews.review
        }

        def json =
                [       'rating':rating,
                        'review' : review
                ]
        render json as JSON
    }

    @Transactional
    def updateReviewRating() {
        BookRatingReviews bookRatingReviews = BookRatingReviews.findByBookIdAndUsername(new Long(params.bookId),springSecurityService.currentUser.username)
        if (bookRatingReviews != null) {
            //update
            if (params.review != null) bookRatingReviews.review = params.review
            if (params.rating != null) bookRatingReviews.rating = params.rating
        } else {
            bookRatingReviews = new BookRatingReviews(bookId: new Long(params.bookId), review: params.review, rating: params.rating,username:springSecurityService.currentUser.username )
        }

        bookRatingReviews.save(flush: true, failOnError: true)
        dataProviderService.getBookRatings(bookRatingReviews.bookId)
        def json =
                ['rating': params.rating,
                 'review': params.review
                ]
        render json as JSON
    }

    def bookanalytics(){}
    def getBookAnalytics(){
        def sql = "select cm.id,cm.name,count(distinct(rd.id)) 'no of resources',count(distinct(rv.resource_dtl_id)) 'no of resources viewed'" +
                " from chapters_mst cm, (resource_dtl rd LEFT JOIN wslog.resource_view rv on rv.resource_dtl_id=rd.id and rv.username='"+springSecurityService.currentUser.username+"')" +
                " where cm.book_id="+params.bookId+" and rd.chapter_id=cm.id and rd.res_type not in ('Reference Web Links')" +
                "group by cm.id order by cm.sort_order desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List chapters = results.collect { chapter ->
            return [id: chapter[0], name: chapter[1],noofresources:chapter[2],noofviews:chapter[3]]
        }

        def json =  [
                'chapters':chapters,
                'status' : chapters ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    def getChapterAnalytics(){
        def sql = "select   rd.resource_name,  min(rv.date_created) maxdate" +
                " from chapters_mst cm, (resource_dtl rd LEFT JOIN wslog.resource_view rv on rv.resource_dtl_id=rd.id and rv.username='"+springSecurityService.currentUser.username+"')" +
                " where cm.id="+params.chapterId+" and rd.chapter_id=cm.id " +
                " group by cm.id,cm.name, rd.id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List resources = results.collect { resource ->
            return [name: resource[0], vieweddate: resource[1]]
        }

        def json = [
                'resources':resources,
                'status' : resources ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    @Transactional
    def getChaptersList(){
        def bookId
        boolean boughtFullBook=false
        if(params.bookId!=null) bookId = params.bookId
        else {
            def jsonObj = request.JSON
            bookId = jsonObj.bookId
        }
        if(springSecurityService.currentUser != null) {
            BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(new Long(bookId), springSecurityService.currentUser.username)
            if(booksPermission!=null) boughtFullBook=true

        }
        List chaptersMst = ChaptersMst.findAllByBookId(new Long(bookId))
        def chapters = chaptersMst.collect{chapter ->
            return [name:chapter.name,chapterId:chapter.id,preview:chapter.previewChapter]
        }

        def json =
                [       'chapters':chapters,
                        'boughtFullBook':boughtFullBook
                ]
        render json as JSON
    }










    @Secured(['ROLE_USER']) @Transactional
    def addQuestion(){
        def chapterId
        chapterId = params.chapterId
        if(params.resId!=null && !"".equals(params.resId)) {
            ResourceDtl resource = ResourceDtl.findById(new Integer(params.resId))
            chapterId = resource.chapterId
        }
        if(userManagementService.canSeeChapter(new Long(chapterId))){
            Questions question=null
            if(params.resId==null){
                question = new Questions(chapterId: new Long(chapterId),
                        question: params.question,username:springSecurityService.currentUser.username)
            }else{
                question = new Questions(chapterId:new Long(chapterId),
                        question: params.question,resId: new Long(params.resId),username:springSecurityService.currentUser.username )
            }

            question.save(flush: true, failOnError: true)
            def json =
                    [       'question':question
                    ]
            render json as JSON
        }else{
            def json =
                    [       'result':"noaccess"
                    ]
            render json as JSON
        }
    }


    @Secured(['ROLE_USER']) @Transactional
    def addAnswer(){
        def chapterId = params.chapterId
        if(userManagementService.canSeeChapter(new Long(chapterId))){
            Answers answer = new Answers(questionId: new Long(params.questionId),answer: params.answer,username:springSecurityService.currentUser.username )
            answer.save(failOnError: true, flush: true)
            def json =
                    [       'answer':answer
                    ]
            render json as JSON
        }else{
            def json =
                    [       'result':"noaccess"
                    ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER'])
    def getQuestions(){
        def chapterId = params.chapterId
        List questions=null
        if(userManagementService.canSeeChapter(new Long(chapterId))){
            questions = Questions.findAllByChapterId(new Long(chapterId),[sort: "id", order: "desc"])

            def sql = "select  qa.id,qa.question,qa.date_created,u.name, (select count(*) from answers where question_id=qa.id)" +
                    " from questions qa, user u " +
                    " where qa.chapter_id="+params.chapterId+" and qa.username=u.username " +
                    " order by qa.id desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)

            questions = results.collect { question ->
                return [id: question[0], question: question[1], dateCreated: question[2],name:question[3],answercount:question[4]]
            }
        }

        def json =
                [       'questions':questions,
                        'status' : questions ? "OK" : "Nothing present"
                ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getAnswers(){
        def chapterId = params.chapterId
        List answers=null
        if(userManagementService.canSeeChapter(new Long(chapterId))){
            answers = Answers.findAllByQuestionId(new Long(params.questionId),[sort: "id", order: "desc"])
            def sql = "select  an.answer,an.date_created,u.name" +
                    " from answers an, user u " +
                    " where an.question_id="+params.questionId+" and an.username=u.username " +
                    " order by an.id desc"

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)

            answers = results.collect { answer ->
                return [answer: answer[0], dateCreated: answer[1], name:answer[2]]
            }
        }

        def json =
                [       'answers':answers,
                        'status' : answers ? "OK" : "Nothing present"
                ]
        render json as JSON
    }

    def videoPlay(){}
    def additionalReferences() {}

    @Secured(['ROLE_USER'])
    def discforum(){}

    def getBookCategories(){

        if(redisService.("bookCategories_"+getSiteId(request))==null) {
            dataProviderService.getBookCategories(getSiteId(request))

        }


        String categories = redisService.("bookCategories_"+getSiteId(request))

        String status = categories?"status:'OK'":"status:'Nothing present'"

        def json
        if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)||"optimized".equals(params.apiMode)){
             json = ['results':categories,
                    'status':(categories)?"OK":"Nothing present",
                    'bookCategories':'true']
        }else{
            json = JSON.parse("{results:" + categories + ","+status +","+
                    "                'bookCategories':'true'}")
        }

        render json as JSON
    }

    @Transactional
    def getSyllabus(){


        List syllabus = LevelSyllabus.findAllByLevelAndSiteId(params.level,getSiteId(request),[sort: "syllabus", order: "asc"])
        if(syllabus.size()==0){
            //get the syllabus of main site ie Wonderslate
            syllabus = LevelSyllabus.findAllByLevelAndSiteId(params.level,new Integer(1),[sort: "syllabus", order: "asc"])
        }
        def json =  [
                'results': syllabus,
                'status': syllabus ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    def getExamTemplates(){
        List exams = ExamMst.findAllByGradeId(params.grade,[sort: "examDetails", order: "asc"])
        def json =  [
                'results': exams,
                'status': exams ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    @Transactional
    def getGrades(){
        List grades = SyllabusGradeDtl.findAllBySyllabusAndSiteId(params.syllabus,getSiteId(request),[sort: "state",sort:"grade", order: "asc"])
        if(grades.size()==0){
            grades = SyllabusGradeDtl.findAllBySyllabusAndSiteId(params.syllabus,new Integer(1),[sort: "state",sort:"grade", order: "asc"])
        }

        def json =  [
                'results': grades,
                'status': grades ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    @Transactional
    def getSubjects(){
        List subjects = SyllabusSubject.findAllBySyllabus(params.syllabus,[sort: "subject", order: "asc"])
        def json =  [
                'results': subjects,
                'status': subjects ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    @Transactional
    def addBookTags(){
        BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: new Long(params.bookId), level: params.level,syllabus: params.syllabus,grade: params.grade,subject: params.subject)
        booksTagDtl.save(failOnError: true, flush: true)
        BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: new Long(params.bookId), level: params.level,syllabus: params.syllabus,grade: params.grade,subject: params.subject)
        booksTagDtl1.wsshop.save(failOnError: true, flush: true)

        dataProviderService.getBookCategories(getSiteId(request))
        dataProviderService.getBookTagsForBook(new Long(params.bookId))
        dataProviderService.getBooksTagList(params.level)

        resourceCreatorService.updateResourceDtlsTag(new Long(params.bookId),params.level,params.grade,params.subject)
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))


        if("published".equals(booksMst.status)) dataNotificationService.tagUpdated(booksTagDtl.level,booksTagDtl.syllabus)
        //if the book is released and the tags are added after that. reset the books list for these tags

        def json =  [
                'results': booksTagDtl,
                'status': booksTagDtl ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    @Transactional
    def deleteBookTag(){
        BooksTagDtl booksTagDtl = BooksTagDtl.findById(new Long(params.id))
        if(booksTagDtl!=null){
            dataNotificationService.tagUpdated(booksTagDtl.level,booksTagDtl.syllabus)
            Long bookId = booksTagDtl.bookId
            String level = booksTagDtl.level
            BooksTagDtl.executeUpdate("delete from BooksTagDtl where id=" + params.id)
            BooksTagDtl.wsshop.executeUpdate("delete from BooksTagDtl where book_id='"+booksTagDtl.bookId+"' AND grade='"+booksTagDtl.grade+"' AND level='"+booksTagDtl.level+"' AND subject='"+booksTagDtl.subject+"' AND syllabus='"+booksTagDtl.syllabus+"'");
            dataProviderService.getBookCategories(getSiteId(request))
            dataProviderService.getBookTagsForBook(new Long(bookId))
            dataProviderService.getBooksTagList(level)
        }
        def json =  [
                'status':  "Deleted"
        ]

        render json as JSON
    }


    @Transactional
    def getChaptersListWithResourceCount(){
        def sql="select cm.name,cm.id, (select count(*) from resource_dtl rd where rd.chapter_id=cm.id and (rd.sharing is  null or rd.sharing !='deleted' or rd.sharing='public') )" +
                " as resource_count from chapters_mst cm where book_id="+params.bookId+" order by cm.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List chapters = results.collect{ chapter ->
            return [chapterName:chapter[0], chaperId:chapter[1], resourceCount:chapter[2]]
        }


        dataProviderService.getTestSeriesDtl(params.bookId)
        List priceList = BookPriceDtl.findAllByBookId(new Integer(params.bookId))
        List testSeries=new JsonSlurper().parseText(redisService.("test_series_"+params.bookId))
        def json =  [
                'results':  chapters,
                'status':  chapters ? "OK" : "Nothing present",
                'testSeries':testSeries,
                'priceList':priceList
        ]

        render json as JSON
    }



    def publishBook() {
        String published = "published"
        if(userManagementService.isInstitutePublisher()) published="institutePublished"
        BooksMst.executeUpdate("update BooksMst set status='"+published+"', datePublished=sysdate() where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set status='"+published+"', datePublished=sysdate() where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set status='"+published+"', datePublished=sysdate() where id=" + params.bookId)
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        if(booksMst.parentBookId!=null) updatePackageBooksInfo(booksMst,null)

        //subscription
        if(booksMst.subscriptionId!=null) wsshopService.addSubscriptionBooks(booksMst)
        dataProviderService.refreshCacheForPublishUnpublish(params.bookId,getSiteId(request))
        dataNotificationService.latestBooksChanged(params.bookId)


            Integer siteId = getSiteId(request)
            String siteIdList = siteId.toString()

            if (siteId.intValue() == 1) {
                if (redisService.("siteIdList_" + siteId) == null) {
                    dataProviderService.getSiteIdList(siteId)
                }

                siteIdList = redisService.("siteIdList_" + siteId)
                dataProviderService.getPublisherBookCategories(siteId, booksMst.publisherId)
                dataProviderService.getPublisherLatestBooks(siteId, booksMst.publisherId)
            }
           dataProviderService.getLatestQuizzes(siteIdList,siteId)
           dataProviderService.getLatestVideos(siteIdList,siteId)
           dataProviderService.getLiveVideos(siteId)
            dataProviderService.getLiveTests(siteId)
        if(siteId.intValue()!=1){
            dataProviderService.getLiveVideos(new Integer(1))
            dataProviderService.getLiveTests(new Integer(1))
        }

        //check and set preview chapter if not set
        dataProviderService.checkAndSetPreviewChapter(params.bookId)
        def json =  [
                'status': "OK"
        ]

        render json as JSON
    }

    def unpublishBook(){
        BooksMst.executeUpdate("update BooksMst set status=null, datePublished=sysdate() where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set status=null, datePublished=sysdate() where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set status=null, datePublished=sysdate() where id=" + params.bookId)

        dataProviderService.refreshCacheForPublishUnpublish(params.bookId,getSiteId(request))
        dataNotificationService.latestBooksChanged(params.bookId)
        dataProviderService.getLiveVideos(getSiteId(request))
        dataProviderService.getLiveTests(getSiteId(request))
        if(getSiteId(request).intValue()!=1){
            dataProviderService.getLiveVideos(new Integer(1))
            dataProviderService.getLiveTests(new Integer(1))
        }
        def json =  [
                'status': "OK"
        ]

        render json as JSON
    }


    @Transactional
    def purchase() {

        String razorpayPaymentId = params.razorpay_payment_id
        if(session["scd"]!=null) request.setAttribute("scd", session["scd"])
        request.setAttribute("paymentFrom", "web")

        Integer poNo = purchaseService.purchase(params,getSiteId(request),request)
        if(session["directSalesId"]!=null){
            DirectSaleOrders directSaleOrders = DirectSaleOrders.findById(session["directSalesId"])
            if(directSaleOrders!=null){
                directSaleOrders.status = "purchased"
                directSaleOrders.save(failOnError: true, flush: true)
                session["directSalesId"] = null
                PurchaseOrder purchaseOrder = PurchaseOrder.findById(poNo)

                if(purchaseOrder!=null){
                    purchaseOrder.directSales = "true"
                    if(directSaleOrders.instituteId!=null) purchaseOrder.instituteId = directSaleOrders.instituteId
                    purchaseOrder.save(failOnError: true, flush: true)
                }
            }
        }

        if("mobile".equals(params.paymentFrom)){
            def json = [
                    'status':"success",
                    'noOfChapters':0,
                    'poNo':poNo]

            render json as JSON
        } else {
            String baseUrl = request.getRequestURL().substring(0,request.getRequestURL().indexOf(request.getRequestURI()))
            User user = User.findByUsername(springSecurityService.currentUser.username)
            session['userdetails'] = user
            redirect (url: baseUrl+"/wonderpublish/orders?bookId="+params.bookId+"&noOfChapters=0&poNo="+poNo+"&paymentId="+razorpayPaymentId)
        }
    }
    @Transactional
    def selfService(){
        try {
           def json = purchaseService.selfService(params,getSiteId(request),request)
            if ("api".equals(params.mode)) {
                render json as JSON
            }else{
                render json as JSON
            }
        }catch(Exception ex){
            render "Bad razor payment id "+ex.toString()
        }
    }

    def getPaymentReports(){
            List sites = SiteMst.findAllByFixPaymentsAndRazorPayKeyIdIsNotNull("true")
             sites.each { site ->
                 purchaseService.getPaymentReports(site.id, request);
             }
            render "Done"
    }


    def getHourlyPaymentReports(){
            List sites = SiteMst.findAllByFixPaymentsAndRazorPayKeyIdIsNotNull("true")
            sites.each { site ->
                purchaseService.getHourlyPaymentReports(site.id, request);
            }
            render "Done"
    }

    @Transactional
    def completeThePOTransaction(Payment payment,payId,user,bookId){
        return purchaseService.completeThePOTransaction(payment,payId,user,bookId,getSiteId(request),request)

    }

    @Transactional @Secured(['ROLE_FINANCE'])
    def callRefund(){
        Integer siteId=utilService.getSiteId(request, session)
        refund(params.razorPaymentId,siteId)
    }

    @Transactional  @Secured(['ROLE_FINANCE'])
    def refund(String razorPaymentId,siteId){
        def json = purchaseService.refund(razorPaymentId,siteId)
        render json as JSON
    }


    def updatePackageBooksInfo(BooksMst booksMst, String oldBooksId){

        asyncLogsService.addPackageBooksToUser(booksMst,oldBooksId)
    }

    def appPurchase() {

        String poType=null
        Integer poNo=null
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        //freebook
        if("freebook".equals(params.payId)){
            poType="ADDEDFORFREE"
        }else{
            PurchaseOrder po = new PurchaseOrder()
            po.dateCreated = new Date()
            po.itemCode = new Integer(params.bookId)
            po.amount = new Double(params.amount)
            po.currency = params.currency
            po.status = 'Active'
            po.username=springSecurityService.currentUser.username
            po.siteId = getSiteId(request)
            po.paymentId = params.payId

            po.save(failOnError: false,flush: true)
            poNo=po.id
            poType='PURCHASE'
        }

        BooksPermission booksPermission = new BooksPermission()
        booksPermission.bookId = new Long(params.bookId)
        booksPermission.username = springSecurityService.currentUser.username
        booksPermission.poNo=poNo
        booksPermission.poType=poType
        booksPermission.save(failOnError: true,flush: true)
        if(siteMst.allBooksLibrary=="true")dataProviderService.getBooksListForUser()
        else redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null

        def json = ['status':"success"]

        render json as JSON
    }

    @Transactional
    def orders() {
        dataProviderService.usersCartBooksDetailsByUsername(springSecurityService.currentUser.username,session["siteId"])
        session["userCartCount"]="0"

        String publisherName = ""
        if ("".equals(params.bookId)||params.bookId==null||"null".equals(params.bookId)) {
            String sql = "select bm.id bookId,bm.title title,bm.cover_image,bm.price,po.amount,bm.publisher_id,po.book_type from purchase_order po,books_mst bm where po.item_code=bm.id and  po.payment_id='"+  params.paymentId+"'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            boolean hasEbook = false
            boolean hasPrintBook = false
            boolean hasRecharge = false
            def currentDoubtBalance
            List purchasedBooks = results.collect { books ->
                if (dataProviderService.getPublisher(books.publisher_id) != null) {
                    publisherName = dataProviderService.getPublisher(books.publisher_id).name
                }
                if("printbook".equals(books.book_type)) hasPrintBook = true
                    else if("combo".equals(books.book_type)){
                    hasPrintBook = true
                    hasEbook = true
                } else if("recharge".equals(books.book_type)) {
                    hasRecharge = true
                 }
                    else hasEbook = true

                return [bookId: books.bookId, title:books.title,coverImage:books.cover_image,price: books.price,poAmount:books.amount,publisherName:publisherName,
                        bookType:books.book_type]
            }
            [purchasedBooks:purchasedBooks,hasEbook:hasEbook,hasPrintBook:hasPrintBook,hasRecharge:hasRecharge]

        } else {

            BooksMst book = BooksMst.findById(new Integer(params.bookId))
            PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentId(params.paymentId)

            if (dataProviderService.getPublisher(book.publisherId) != null) {
                publisherName = dataProviderService.getPublisher(book.publisherId).name
            }

            [
                    title         : book.title,
                    displayName   : "Order Confirmation",
                    bookId        : params.bookId,
                    coverImage    : book.coverImage,
                    price         : book.price,
                    poAmount      : purchaseOrder ? purchaseOrder.amount : "",
                    listPrice     : book.listprice,
                    publisherName : publisherName,
                    newCss        : true,
                    bookType      : book.bookType,
            ]
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def addPaidVideo(){
        def file = params.file
        def resourceDtlInstance = new ResourceDtl()
        def chapterId = params.chapterId

        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {
            resourceDtlInstance.filename = file.originalFilename.replaceAll("\\s+", "")
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.chapterId = new Integer(chapterId)
            resourceDtlInstance.resourceName = (params.resourceName != null ? params.resourceName : file.originalFilename)
            resourceDtlInstance.resLink = "blank"
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.modifiedFile = ((int)(Math.random()*101))+resourceDtlInstance.filename.substring(0, resourceDtlInstance.filename.lastIndexOf("."))+((int)(Math.random()*501))+resourceDtlInstance.filename.substring(resourceDtlInstance.filename.lastIndexOf("."))

            User user = User.findByUsername(springSecurityService.currentUser.username)
            if(!user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_CREATOR"
            }){
                resourceDtlInstance.sharing="createdbyuser"
            }

            resourceDtlInstance.save(flush: true, failOnError: true)

            if (resourceDtlInstance.sharing == null) {
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            } else {
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
            }

            File uploadDir = new File(grailsApplication.config.grails.basedir.path+"tmp/")
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+"tmp/"+resourceDtlInstance.modifiedFile))

            if(!System.properties['os.name'].toLowerCase().contains('windows') && !System.properties['os.name'].toLowerCase().contains('mac')) {
                def scriptCom = "${grailsApplication.config.grails.basedir.path}/aws_s3_copy.d ${grailsApplication.config.grails.basedir.path}tmp/${resourceDtlInstance.modifiedFile}"
                def proc = scriptCom.execute()

                //kill after 10 minutes if still running
                proc.waitForOrKill(600000)

                if(proc.exitValue() != 0) {
                    println "[[return code: ${proc.exitValue()}]]"
                    println "[[stderr: ${proc.err.text}]]"
                }
            }
        }
        if("sage".equals(session["entryController"]))  {
            redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': chapterId, 'toc': false])
        }
        else{
            redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': chapterId, 'toc': false])
        }
    }

    @Secured(['ROLE_USER'])
    def getPaidVideoURL(){
        def status = "Fail"
        def data = ""

        if(params.resId!=null) {
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))

            def scriptCom

            if(resourceDtl!=null && resourceDtl.count()>=0) {
                if(!System.properties['os.name'].toLowerCase().contains('windows') && !System.properties['os.name'].toLowerCase().contains('mac')) {
                    scriptCom = ['java', '-cp', "${grailsApplication.config.grails.basedir.path}../jars/*:${grailsApplication.config.grails.basedir.path}../bin",
                            'GetEncryptedSignedURL',"${resourceDtl.modifiedFile}","${grailsApplication.config.grails.appServer.default}"]
                } else {
                    scriptCom = ['java', '-cp', "${grailsApplication.config.grails.basedir.path}/libs/*;${grailsApplication.config.grails.basedir.path}/bin",
                            'GetEncryptedSignedURL',"${resourceDtl.modifiedFile}","${grailsApplication.config.grails.appServer.default}"]
                }

                def proc = scriptCom.execute()

                //kill after 10 minutes if still running
                proc.waitForOrKill(600000)

                data = proc.in.text.replace(System.getProperty("line.separator"),"")

                if(proc.exitValue() != 0) {
                    println "[[return code: ${proc.exitValue()}]]"
                    println "[[stderr: ${proc.err.text}]]"
                }

                status = "Ok"
            }
        }

        def json = ['status':status,'data':data]
        render json as JSON
    }




    @Transactional
    def quizcreator(){
           boolean  reAttempt =true
           boolean  allowReAttempt =false
           String sort=""
            if(redisService.("competitiveExams")==null){
                dataProviderService.getCompetitiveExams()
            }
            List competitiveExams = new JsonSlurper().parseText(redisService.("competitiveExams"))
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        def publisherUser = true
        if(!user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
        }){
            publisherUser = false
        }
        if(params.ChaptersMst==null) reAttempt=false
            if("edit".equals(params.mode)){
                if(params.id ==null || ''.equals(params.id))  redirect(action:'index')
                else{
                    ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.id))
                    if(publisherUser||resourceDtl.createdBy.equals(user.username)) {
                        def testStartDate = null, testEndDate = null, testResultDate = null
                        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm")
                        if (resourceDtl.testStartDate != null) {
                            testStartDate = dateFormat.format(convertDate(resourceDtl.testStartDate, "UTC", "IST"))
                        }
                        if (resourceDtl.testEndDate != null) {
                            testEndDate = dateFormat.format(convertDate(resourceDtl.testEndDate, "UTC", "IST"))
                        }
                        if (resourceDtl.testResultDate != null) {
                            testResultDate = dateFormat.format(convertDate(resourceDtl.testResultDate, "UTC", "IST"))
                        }
                        if("Yes".equals(resourceDtl.allowReAttempt) || params.ChaptersMst!=null)allowReAttempt=true
                        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
                        if(objectiveMst != null) sort = "quizSort"
                        else sort = "id"
                        List objectives = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort])
                        [chapterId    : params.chapterId, bookId: params.bookId, resourceDtl: resourceDtl,
                         objectives   : objectives, competitiveExams: competitiveExams,
                         testStartDate: testStartDate, testEndDate: testEndDate, testResultDate: testResultDate,
                         langMstList: com.wonderslate.data.LangMst.listOrderBySortBy(), publisherUser: publisherUser,allowReAttempt:allowReAttempt,reAttempt:reAttempt]
                    }else
                        redirect(controller: 'books', action:'index')
                }
            }
            else
                [chapterId: params.chapterId,bookId:params.bookId,
                 competitiveExams:competitiveExams, hideBanner: true,resourceDtl:null,langMstList: com.wonderslate.data.LangMst.listOrderBySortBy(),publisherUser:publisherUser,allowReAttempt:true]

    }

    def mylibrary() {[showDrift : true]}

    @Secured(['ROLE_USER']) @Transactional

    def mybooks() {

        Integer siteId = getSiteId(request)
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }
        def testBookId=null
        if(session["testBookId"]!=null){
            testBookId = session["testBookId"]
            session.removeAttribute("testBookId")
            BooksMst booksMst = dataProviderService.getBooksMst(testBookId)
            if(booksMst.testStartDate < convertDate(Calendar.getInstance().getTime(),"UTC","IST") ) {
                if (redisService.("test_series_" + testBookId) == null) dataProviderService.getTestSeriesDtl(testBookId)
                List quizDetails = new JsonSlurper().parseText(redisService.("test_series_" + testBookId))
                if (quizDetails != null && quizDetails.size() > 0)
                    redirect(controller: 'funlearn', action: 'quiz', params: ['quizId': quizDetails[0].quizId, 'resId': quizDetails[0].resourceID, 'fromMode': 'library', 'quizMode': 'practice', 'testSeries': 'true', 'bookId': testBookId])
            }
        }

        if(!"true".equals(session["NumberExceeded"])||((siteId.intValue()==1||siteId.intValue()==25)&&userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId()))) {
            Boolean instructor = false
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }


            if (springSecurityService.currentUser != null && session.getAttribute("publisherLogoId")== null && session['siteId']==25) {
                def publisherLogo
                String sql = "select im.id,im.publisher_id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + session['siteId'];
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                results.collect { publisher ->
                    publisherLogo=publisher[1];
                }
                session["publisherLogoId"]=publisherLogo;
            }


            
            if (springSecurityService.currentUser != null) {
                 if (session["userIsInstructor"] == null) {
                    session["userIsInstructor"] = userManagementService.isInstructor()
                }
                instructor = session["userIsInstructor"]

            }

            if (session['bookIdForPurchase'] != null) {
                def bookId = session['bookIdForPurchase']
                session.removeAttribute('bookIdForPurchase')
                BooksMst booksMst = BooksMst.findById(bookId)
                redirect(controller: 'wonderpublish', action: 'bookdtl', params: ['bookId': bookId, mode: 'purchase', bookTitle: booksMst.title, bookPrice: booksMst.price])
            } else {
                def siteMst = dataProviderService.getSiteMst(new Long(session['siteId']!=null?""+session['siteId']:"1"))
                [displayName: "<a href='mybooks'>MY LIBRARY</a>", instructor: instructor,
                 otpReg:siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,
                 'showTestsHeader':("eutkarsh".equals(session["entryController"])?true:false),showDrift : true, siteMst: siteMst,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
            }
        }
        else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect(uri: "/logoff")

        }
        [showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    def commonfooter(){}

    @Transactional
    def getInstituteId(String siteName){
        Long siteId;
        if(siteName.equals("evidya")){
             siteId=12
        }else if(siteName.equals("etexts")){
             siteId=23
        }else if(siteName.equals("ebouquet")){
            siteId=24
        }
        String ipAddress = utilService.getIPAddressOfClient(request)
        def instituteId = null
        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress==null){
            if(springSecurityService.currentUser!=null) {
                String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + session["siteId"];
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    instituteId = results[0][0]

                } else {
                    def json = ['status': "Access Denied!"]
                    render json as JSON
                }
            }
            else {
                def json = ['status': "Access Denied!"]
                render json as JSON
            }
        }else{
            instituteId = instituteIPAddress.institute_id
        }

        return instituteId
    }

    @Transactional
    def book(){
        Integer siteId = utilService.getSiteId(request,session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if(session['entryController']==null && params.siteName!=null) {
            userManagementService.setEntrySession(params.siteName,session,servletContext)
        }

        if("true".equals(siteMst.mainResourcesPage)||"books".equals(params.siteName)||"books".equals(""+session['entryController'])){
            forward(controller: 'resources', action:'ebook')
            return
        }else {
            boolean sageAdmin =false
            boolean hasBookEbouquet =false
            def expiry=true
            Integer checkoutDays
            Calendar c = Calendar.getInstance()
            String entryPoint=session['entryController'];
            if (session["userdetails"] != null && session["userdetails"].authorities.any { it.authority == "ROLE_BOOK_CREATOR" }) {
                sageAdmin = true
            }
            if (("evidya".equals(params.siteName)|| "etexts".equals(params.siteName)
                    ||"evidya".equals(entryPoint)||"etexts".equals(entryPoint))) {

                def bookidcom = params.bookId;
                if (!sageAdmin) {
                    def instituteId = getInstituteId(params.siteName);
                    if (instituteId != null) {
                        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedBy(new Long(instituteId));
                        List booksBatchDtl = BooksBatchDtl.findAllByBatchId(new Long(courseBatchesDtl.id))
                        def urlcheck = "";
                        for (int k = 0; k < booksBatchDtl.size(); k++) {
                            def book = booksBatchDtl[k].bookId;
                            if (book.toString() == bookidcom.toString()) {
                                urlcheck = "pass"
                                break
                            }
                        }
                        if (urlcheck != "pass") {
                            redirect([uri: '/' + params.siteName + '/store'])
                            return
                        }
                    } else {
                        redirect([uri: '/' + params.siteName + '/store'])
                        return
                    }
                }

            }

            if(("ebouquet".equals(params.siteName)|| "ebouquet".equals(entryPoint))) {
                if (springSecurityService.currentUser != null) {
                    if (!sageAdmin) {
                        def userBooks = redisService.(springSecurityService.currentUser.username + "_" + "bookIds")
                        List booksIds = userBooks != null ? Arrays.asList(redisService.(springSecurityService.currentUser.username + "_" + "bookIds").split("\\s*,\\s*")) : null
                        if (booksIds != null && booksIds.indexOf("" + params.bookId) > -1) hasBookEbouquet = true
                        if (!hasBookEbouquet) {
                            redirect([uri: '/ebouquet/store'])
                            return
                        }
                    }
                } else {
                    render "Access Denied"
                }
            }

            def createdByWsEditor = false;
            if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }
            if (!params.notesCreationMode) {
                session["bookRefererPage"] = request.getHeader('referer')
            }

            if (params.resId != null) {
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
                if (resourceDtl != null && "Multiple Choice Questions".equals(resourceDtl.resType))
                    redirect(controller: 'funlearn', action: 'quiz', params: ['resId': resourceDtl.id, 'quizMode': "practice", 'quizId': resourceDtl.resLink])
                return
            }

            if ((params.bookId == null || ''.equals(params.bookId))) redirect(action: 'index')
            else {
                def libraryAccess = null
                Boolean allowReview = false
                Boolean downloadBookChapters =false
                def chapterId = null
                def lastReadTopicId = null
                def previewMode = "true".equals(params.preview) ? true : false
                List boughtChapters
                Boolean fullBookBought = false
                boolean hasBookAccess = false
                BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
                Boolean instructor = false
                Boolean instructorControlledBook = false
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(booksMst.id)

                if (redisService.("chapters_" + booksMst.id) == null) {
                    dataProviderService.getChaptersList(booksMst.id)
                }
                def chaptersMst = new JsonSlurper().parseText(redisService.("chapters_" + booksMst.id))
                if (springSecurityService.currentUser != null) {
                    if (session["userIsInstructor"] == null) session["userIsInstructor"] = userManagementService.isInstructor()
                    instructor = session["userIsInstructor"]
                    //should add cache .. maybe a set
                    //see if the
                    if (redisService.(springSecurityService.currentUser.username + "_" + "booksList") == null) {
                        dataProviderService.getBooksListForUser()
                    }
                    List booksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username + "_" + "booksList"))


                    if (session["siteId"] != null && (session["siteId"].intValue() == 9) && (session["siteId"].intValue() == booksMst.siteId.intValue())) {
                        hasBookAccess = true
                        fullBookBought = true
                    }
                    if(session["siteId"].intValue() != 25) {
                    booksList.each { book ->
                        if (Long.parseLong(""+book.id) == booksMst.id.longValue()) {
                            hasBookAccess = true
                            if ("true".equals("" + book.instructorControlled)) {
                                instructorControlledBook = true
                                if (!instructor) {
                                    hasBookAccess = false
                                    String sql = "select distinct chapter_id from wsuser.batch_resources_dtl where batch_id=" + book.batchId + " and chapter_id not in (" + redisService.("previewchapter_" + booksMst.id) + ")"
                                    def dataSource = grailsApplication.mainContext.getBean('dataSource')
                                    def sql1 = new Sql(dataSource)
                                    def results = sql1.rows(sql)

                                    boughtChapters = results.collect { resource ->
                                        return [chapterId: resource[0]]
                                    }

                                }
                            }
                        }
                    }

                    }


                    if(siteMst.appInApp=="true"||siteMst.sageOnly==null) {
                        if (wsLibraryService.bookAccessForUser(booksMst.id + "", request, session)) {
                            hasBookAccess = true;
                            dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
                        }else{
                            if(!"".equals(params.batchId) && !null.equals(params.batchId) && BooksPermission.findByBatchIdAndBookIdAndPoTypeAndUsername(new Long(params.batchId), new Long(booksMst.id),'ADDEDFROMINSTITUTE', springSecurityService.currentUser.username)==null){
                                if(BooksBatchDtl.findByBatchIdAndBookId(new Long(params.batchId),new Long(booksMst.id)).numberOfLicenses==null|| BooksBatchDtl.findByBatchIdAndBookId(new Long(params.batchId),new Long(booksMst.id)).numberOfLicenses>BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(params.batchId),new Long(booksMst.id),"ADDEDFROMINSTITUTE").size()) {
                                    checkoutDays =
                                            (InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays != ""
                                                    && InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays != null)
                                                    ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays)
                                                    : null
                                    if(checkoutDays!=null && checkoutDays!="") {
                                        c.add(Calendar.DATE, checkoutDays)
                                    }else{
                                        c.add(Calendar.DATE, 14)
                                    }
//                        }else{
//                            expiry=false
//                        }
                                    BooksPermission booksPermission = new BooksPermission(
                                            bookId: new Long(booksMst.id),
                                            username: springSecurityService.currentUser.username,
                                            poType: 'ADDEDFROMINSTITUTE',
                                            batchId: new Long(params.batchId),
                                            addedBy: springSecurityService.currentUser.username,
                                            expiryDate: expiry==true?c.getTime():null
                                    )
                                    booksPermission.save()
                                    BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                                            bookId: new Long(booksMst.id),
                                            username: springSecurityService.currentUser.username,
                                            poType: 'ADDEDFROMINSTITUTE',
                                            batchId: new Long(params.batchId),
                                            addedBy: springSecurityService.currentUser.username,
                                            bpId: booksPermission.id,
                                            expiryDate: expiry==true?c.getTime():null
                                    )
                                    booksPermissionCopy.save()
                                    BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByBookIdAndBatchIdAndUsername(new Long(booksMst.id),new Long(params.batchId),springSecurityService.currentUser.username)
                                    if(booksQueueDtl!=null) booksQueueDtl.delete()
                                    redisService.("userMyLibraryInstituteBooks_"+params.batchId) = null
                                    redisService.("lastReadBooksIns_"+params.batchId+"_"+ springSecurityService.currentUser.username)=null
                                    hasBookAccess = true
                                    previewMode = false
                                }
                            }
                        }
                    }

                    if (!hasBookAccess) {

                        if (!("" + session["userdetails"].publisherId).equals("" + booksMst.publisherId) || session["userdetails"].authorities.any {
                            it.authority == "ROLE_WS_CONTENT_CREATOR"
                        })
                            previewMode = true
                    } else {

                        if (hasBookAccess) fullBookBought = true

                        ChapterAccess chapterAccess = ChapterAccess.findByBookIdAndUsername(booksMst.id, springSecurityService.currentUser.username)
                        if (chapterAccess != null && ChaptersMst.findByIdAndBookId(chapterAccess.chapterId, booksMst.id) != null) {
                            chapterId = chapterAccess.chapterId
                            lastReadTopicId = chapterAccess.resourceId
                        }

                        BookRatingReviews bookRatingReviews = BookRatingReviews.findByBookIdAndUsername(new Long(params.bookId), springSecurityService.currentUser.username)
                        if (bookRatingReviews == null) allowReview = true
                    }
                }
                else{
                    previewMode = true
                    //check for ip library access
                    if(session["siteId"] != null && session["siteId"].intValue() == 25 && userManagementService.doesIPAddressHasAccessForBook(booksMst.id,request,session["siteId"])){
                        hasBookAccess = true
                        fullBookBought = true
                    }
                }
                if(booksMst.price != null && booksMst.price.doubleValue() == 0&&"published".equals(booksMst.status)){
                    previewMode = false;
                }
                if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
                    session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

                }


                def resType
                def link

                if (params.chapterId != null && !"topic".equals(params.chapterId)) {
                    chapterId = params.chapterId
                }

                if (previewMode) {
                    if(ChaptersMst.findByIdAndBookId(redisService.("previewchapter_" + booksMst.id), booksMst.id) != null) {
                        chapterId = redisService.("previewchapter_" + booksMst.id)
                    }
                    if (getSiteId(request).intValue() == 12 || getSiteId(request).intValue() == 23 || getSiteId(request).intValue() == 24) {
                        updateBookView(params.bookId, "web", "library")
                    } else {
                        asyncLogsService.updateBookView(params.bookId, "web", "preview", getSiteId(request),
                                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),params.instituteId)
                    }

                } else {
                    if (getSiteId(request).intValue() == 12 || getSiteId(request).intValue() == 23 || getSiteId(request).intValue() == 24) {
                        updateBookView(params.bookId, "web", "library")
                    } else {
                        asyncLogsService.updateBookView(params.bookId, "web", "library", getSiteId(request),
                                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),params.instituteId)
                    }
                }

                if (chapterId == null) {
                    chapterId = chaptersMst[0].id
                }
                if (springSecurityService.currentUser != null) {
                    userManagementService.updateChapterAccess(booksMst.id, new Long("" + chapterId))
                }

                if (redisService.("bookHeader_" + booksMst.id) == null) {
                    dataProviderService.getBookHeader(booksMst.id)
                }

                def keywords = booksMst.title + " on Wonderslate"

                //logic for addind the seo friendly title
                String seoFriendlyTitle = booksMst.title
                //check if wonderslate is there in the title


                if (springSecurityService.currentUser != null) {
                    dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
                }

                if (booksMst.siteId.intValue() == 12 || booksMst.siteId.intValue() == 23) {
                    libraryAccess = "present"
                }


                if (siteMst.id.intValue() == 20) fullBookBought = true
                // Key for epub reader
                String workingKey = "9E74748742AAB8342432FCF15E225793"
                String encResp= "epub" + (int)(Math.random()*((9999-0000)+1))+0000, pdfResp= "pdf" + (int)(Math.random()*((9999-0000)+1))+0000

                session.setAttribute('epubKey',encResp)
                AesCryptUtil aesUtil=new AesCryptUtil(workingKey)
                String encryptStr = aesUtil.encrypt(encResp)
                String sessionEncKey = session.getAttribute('epubEncKey')
                if(sessionEncKey != null && !sessionEncKey.isEmpty()) session.removeAttribute('epubEncKey')

                session.setAttribute('pdfKey',pdfResp)
                AesCryptUtil aesUtilPdf=new AesCryptUtil(workingKey)
                String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)
                String sessionEncPdfKey = session.getAttribute('pdfEncKey')

                if(siteMst.downloadBookChapters=="true" && booksMst.downloadChapters=="Yes" && booksMst.singleEpub!="true")downloadBookChapters=true

                if(sessionEncPdfKey != null && !sessionEncPdfKey.isEmpty()) session.removeAttribute('pdfEncKey')
                [topicId                 : chapterId,
                 topicMst                : chaptersMst,
                 title                   : seoFriendlyTitle,
                 keywords                : keywords,
                 id                      : params.id,
                 authors                 : booksMst.authors,
                 resType                 : resType,
                 link                    : link,
                 booksPage               : "true",
                 bookId                  : params.bookId,
                 headerImage             : booksMst.headerImage,
                 coverImage              : booksMst.coverImage,
                 description             : booksMst.description,
                 addlMenu                : "yes",
                 bookName                : booksMst.title,
                 lastReadTopicId         : lastReadTopicId == null ? "-1" : lastReadTopicId,
                 previewMode             : previewMode,
                 book                    : booksMst,
                 newCss                  : true,
                 hideFooter              : true,
                 allowReview             : allowReview,
                 boughtChapters          : boughtChapters,
                 fullBookBought          : fullBookBought,
                 header                  : redisService.("bookHeader_" + booksMst.id),
                 hideBottomIcons         : true,
                 hideSearch              : true,
                 disciplines             : session["disciplines"],
                 instructor              : instructor,
                 instructorControlledBook: instructorControlledBook,
                 hideTopNavigation       : false,
                 notesCreationMode       : (params.notesCreationMode ? true : ""),
                 referer                 : session["bookRefererPage"],
                 resId                   : params.resId,
                 showLibrary             : utilService.hasLibraryAccess(request, getSiteId(request)),
                 isBookPage              : true,
                 apiKey                  : siteMst.googleApiKey,
                 instituteLibrary        : siteMst.instituteLibrary,
                 booksTagDtl             : booksTagDtl,
                 createdByWsEditor       : createdByWsEditor,
                 encryptEpubKey:encryptStr,
                 encryptPdfKey:encryptPdfStr,
                 downloadBookChapters : downloadBookChapters,
                 appInApp: siteMst.appInApp,
                 sageOnly: siteMst.sageOnly,
                 genericReader    : booksMst.genericReader||"80".equals(""+session["siteId"])?true:false ? true : false,
                ]
            }
        }
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def pubDesk(){
        SiteMst siteMst = dataProviderService.getSiteMst(session["siteId"])
        boolean showPublishServer = false
        showPublishServer = utilService.isPublishServer(request.getServerName())
        if(showPublishServer){
            if(session.getAttribute("userdetails")==null){
                session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
            }

            List booksMst

            if("print".equals(params.mode)){
                if (redisService.("getPrintBooksListForPubDesk") == null) {
                    dataProviderService.getPrintBooksListForPubDesk()
                }

                booksMst = new JsonSlurper().parseText(redisService.("getPrintBooksListForPubDesk"))
            } else {

                if (redisService.("getWSUnpublishedMyBooks_"+springSecurityService.currentUser.username) == null || redisService.("getWSUnpublishedMyBooks_"+springSecurityService.currentUser.username) == "null") {
                    dataProviderService.getWSUnpublishedMyBooks()
                }

                booksMst = new JsonSlurper().parseText(redisService.("getWSUnpublishedMyBooks_"+springSecurityService.currentUser.username))

            }

            List bookTypes = BookProductTypes.findAll()

            [booksMst:booksMst, hideSearch : true, printBooks:"print".equals(params.mode)?true:false, hideBanner: true,disciplines:getDisciplines(),
             siteMst: siteMst,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),institutePublisher:userManagementService.isInstitutePublisher(),bookTypes:bookTypes]
        }else{
            if("9".equals(""+session["siteId"])) {
                render "Publishing desk can be accessed only from publish server - <a href='https://publish.wonderslate.com/sage?isbn=9789352807383' target='_blank'>publish.wonderslate.com/sage?isbn=9789352807383</a>"
            } else if("privatelabel".equals(""+session['entryController'])) {
                render "Publishing desk can be accessed only from publish server - <a href='https://publish.wonderslate.com/sp/"+siteMst.siteName+"' target='_blank'>publish.wonderslate.com/sp/"+siteMst.siteName+"</a>"
            } else {
                render "Publishing desk can be accessed only from publish server - <a href='https://publish.wonderslate.com/"+siteMst.siteName+"' target='_blank'>publish.wonderslate.com/"+siteMst.siteName+"</a>"
            }
        }
    }

    @Transactional
    def getDisciplines(){
        def sql="SELECT discipline,count(id) FROM books_mst where site_id=9 and discipline is not null and status='published' group by discipline";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);


        List discipline = results.collect{ temp ->
            return [discipline:temp[0],noOfBooks:temp[1]];
        }

        return discipline;
    }

    def answerFixerInput(){

    }

    def answerFixerProcessor(){
        String issues = ""
        try {
            ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.resId))
            ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
            String sort=""
            if(objectiveMst != null) sort = "quizSort"
            else sort = "id"

            List objectiveMsts = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort ,order: "asc"]);
            int noOfQuestions = objectiveMsts.size()

            int startingQuestionNo = 1
            String numberingStyleAnswer = params.numberingStyleAnswer
            String[] correctAnswers = new String[noOfQuestions]
            String answerKeys = params.answerKeys
            answerKeys = answerKeys.replaceAll("\\s", "")
            boolean optionPrefixPresent = ".".equals(params.answeringStyle) ? false : true

            int k = 0

            String questionNumberString


            //this i+1 logic was implemented when i was starting 0 always. After adding the starting question number, should have removed that logic. Will do it sometime

            for (int i = (startingQuestionNo - 1); i < (startingQuestionNo + noOfQuestions - 1); i++) {

                questionNumberString = (i + 1) + numberingStyleAnswer
                if (optionPrefixPresent) {
                    int firstIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1
                    int secondIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 2

                    if (secondIndex > firstIndex) {
                        correctAnswers[k] = answerKeys.substring(answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1, answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 2)
                    } else {
                        issues += "Issue in the answers of Question No " + (i + 1) + "\n"
                    }
                } else {
                    int firstIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length()
                    int secondIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1

                    if (secondIndex > firstIndex) {
                        correctAnswers[k] = answerKeys.substring(answerKeys.indexOf(questionNumberString) + questionNumberString.length(), answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1)

                    } else {
                        issues += "Issue in the answers of Question No " + (i + 1) + "\n"
                    }
                }
                k++
            }

            String markedCorrectAnswer
            for(int i=0;i<correctAnswers.length;i++){
                markedCorrectAnswer = ""
                objectiveMst = objectiveMsts[i]
                if("Yes".equals(objectiveMst.answer1)) markedCorrectAnswer="a"
                else if("Yes".equals(objectiveMst.answer2)) markedCorrectAnswer="b"
                else if("Yes".equals(objectiveMst.answer3)) markedCorrectAnswer="c"
                else if("Yes".equals(objectiveMst.answer4)) markedCorrectAnswer="d"
                else if("Yes".equals(objectiveMst.answer5)) markedCorrectAnswer="e"
                if(!correctAnswers[i].toLowerCase().equals(markedCorrectAnswer)){
                    issues +=(i+1)+" "
                    println()
                    objectiveMst.answer1=null
                    objectiveMst.answer2=null
                    objectiveMst.answer3=null
                    objectiveMst.answer4=null
                    objectiveMst.answer5=null
                    if("a".equals(correctAnswers[i].toLowerCase())) objectiveMst.answer1="Yes"
                    else if("b".equals(correctAnswers[i].toLowerCase())) objectiveMst.answer2="Yes"
                    else if("c".equals(correctAnswers[i].toLowerCase())) objectiveMst.answer3="Yes"
                    else if("d".equals(correctAnswers[i].toLowerCase())) objectiveMst.answer4="Yes"
                    else if("e".equals(correctAnswers[i].toLowerCase())) objectiveMst.answer5="Yes"
                }
               // println("question number="+(i+1)+" answer is "+correctAnswers[i]+" and question is "+objectiveMst.question)

            }


        }catch(Exception e){
            println "the issue is "+issues+"\n"+e
        }
        if("".equals(issues)) issues = "All questions are marked correctly"
        else issues = "The following questions has wrongs answers marked "+issues+" and they are fixed."
        [issues:issues]
    }


    def quizcreatorbulkinput(){
        if(params.chapterId ==null || ''.equals(params.chapterId))  {
            [currentAffairsType: CurrentAffairsMst.findAllBySiteId(new Integer(""+session["siteId"]))]
        }
        else {
            ChaptersMst chaptersMst = ChaptersMst.findById(new Integer(params.chapterId))
            String chapterUrl="<a href='/wonderpublish/bookCreate?chapterId="+params.chapterId+"&bookId="+params.bookId+"'>"+chaptersMst.name+"</a>"
            [chapterId: params.chapterId,bookId:params.bookId, chaptersMst: chaptersMst, title:chaptersMst.name,displayName:chapterUrl, hideBanner: true,currentAffairsType:com.wonderslate.data.CurrentAffairsMst.list()]
        }
    }

    def quizcreatorbulk() {
        boolean problemExists = false
        String issues = ""
        try {
            int noOfQuestions = Integer.parseInt(params.noOfQuestions)

            String questionsString = params.questionsString
            int startingQuestionNo = Integer.parseInt(params.startingQuestionNo)
            String numberingStyleQuestion = params.numberingStyleQuestion
            String numberingStyleAnswer = params.numberingStyleAnswer
            String optionsStyle = params.optionsStyle
            String[] questionsArray = new String[noOfQuestions]
            String[] directionsArray = new String[noOfQuestions]
            int noOfOptions = Integer.parseInt(params.noOfOptions)
            String[][] optionKeys = new String[noOfQuestions][noOfOptions]
            String optionsHolder = ""
            String[] correctAnswers = new String[noOfQuestions]
            String[] answerExplanations = new String[noOfQuestions]
            String questionBlock = ""
            String questionNumberString = ""

            String answerKeys = params.answerKeys
            answerKeys = answerKeys.replaceAll("\\s", "")
            String answerDesc = params.answerDesc
            String[] optionHeaders
            boolean optionPrefixPresent = ".".equals(params.answeringStyle) ? false : true

            String originalQuestionBlock

            if ("A.".equals(optionsStyle))
                optionHeaders = ["A.", "B.", "C.", "D.", "E."]
            else if ("a.".equals(optionsStyle))
                optionHeaders = ["a.", "b.", "c.", "d.", "e."]
            else if ("A)".equals(optionsStyle))
                optionHeaders = ["A)", "B)", "C)", "D)", "E)"]
            else if ("(1)".equals(optionsStyle)) {
                optionHeaders = ["(1)", "(2)", "(3)", "(4)", "(5)"]
            } else if ("a)".equals(optionsStyle))
                optionHeaders = ["a)", "b)", "c)", "d)", "e)"]
            else if ("(a)".equals(optionsStyle)) {
                optionHeaders = ["(a)", "(b)", "(c)", "(d)", "(e)"]
            } else if ("(A)".equals(optionsStyle)) {
                optionHeaders = ["(A)", "(B)", "(C)", "(D)", "(E)"]
            }

            boolean invalid
            int k = 0

            for (int i = 0; i < noOfQuestions; i++) {
                for (int j = 0; j < noOfOptions; j++) {
                    optionKeys[i][j] = "-"
                }

                answerExplanations[i] = ""
            }

            //this i+1 logic was implemented when i was starting 0 always. After adding the starting question number, should have removed that logic. Will do it sometime

            for (int i = (startingQuestionNo - 1); i < (startingQuestionNo + noOfQuestions - 1); i++) {
                invalid = false

                if (i < (startingQuestionNo + noOfQuestions - 2)) {
                    if (questionsString.indexOf((i + 1) + numberingStyleQuestion) != -1 && questionsString.indexOf((i + 2) + numberingStyleQuestion) != -1) {
                        int firstIndex = questionsString.indexOf((i + 1) + numberingStyleQuestion)
                        int secondIndex = questionsString.indexOf((i + 2) + numberingStyleQuestion)
                        int firstOptionIndex = questionsString.indexOf(optionHeaders[0])
                        int secondOptionIndex = questionsString.indexOf(optionHeaders[1])

                        if(secondOptionIndex<firstOptionIndex){
                          //option index problem.
                            issues = "The options in question number "+(i+1)+" is having problem . Check the same and submit"
                            throw new Exception("issues")
                        }
                        else secondOptionIndex = questionsString.indexOf(optionHeaders[1], firstOptionIndex + 2)
                        int thirdOptionIndex = questionsString.indexOf(optionHeaders[2])
                        if(thirdOptionIndex<secondOptionIndex){
                            //option index problem.
                            issues = "The options in question number "+(i+1)+" is having problem. Check the same and submit"
                            throw new Exception("issues")
                        }
                        else
                        thirdOptionIndex = questionsString.indexOf(optionHeaders[2], secondOptionIndex + 2)

                        int fourthOptionIndex = questionsString.indexOf(optionHeaders[3])
                        if(fourthOptionIndex<thirdOptionIndex){
                            //option index problem.
                            issues = "The options in question number "+(i+1)+" is having problem. Check the same and submit"
                            throw new Exception("issues")
                        }
                        else
                        fourthOptionIndex = questionsString.indexOf(optionHeaders[3], thirdOptionIndex + 2)


                        int lastOptionIndex = questionsString.indexOf(optionHeaders[noOfOptions - 1])

                        secondIndex = questionsString.indexOf((i + 2) + numberingStyleQuestion, fourthOptionIndex + 2)


                        if (secondIndex > firstIndex) {
                            questionBlock = questionsString.substring(questionsString.indexOf((i + 1) + numberingStyleQuestion), secondIndex)
                            originalQuestionBlock = questionBlock

                            if (questionBlock.indexOf("Directions") != -1) {
                                directionsArray[k + 1] = questionBlock.substring(questionBlock.indexOf("Directions"))
                                questionBlock = questionBlock.substring(0, questionBlock.indexOf("Directions"))
                            }

                            //replace double $
                            questionBlock = questionBlock.replace('$', "#")
                            if (questionBlock.indexOf("##") > -1) {
                                boolean hasFormula = true
                                while (hasFormula) {
                                    questionBlock = questionBlock.replaceFirst("##", "<span class=\"math-tex\">\\\\(")
                                    questionBlock = questionBlock.replaceFirst("##", "\\\\)</span>")
                                    if (questionBlock.indexOf('##') == -1) hasFormula = false
                                }
                            }

                            if (questionBlock.indexOf('#') > -1) {
                                boolean hasFormula = true

                                while (hasFormula) {
                                    questionBlock = questionBlock.replaceFirst("#", "<span class=\"math-tex\">\\\\(")
                                    questionBlock = questionBlock.replaceFirst("#", "\\\\)</span>")
                                    if (questionBlock.indexOf('#') == -1) hasFormula = false
                                }
                            }


                        } else {
                            invalid = true
                            problemExists = true
                            issues += " Question No " + (i + 1) + "\n"
                        }
                    } else {
                        invalid = true
                        problemExists = true
                        issues += "B Question No " + (i + 1) + "\n"
                    }
                } else {
                    if (questionsString.indexOf((i + 1) + numberingStyleQuestion) != -1) {
                        questionBlock = questionsString.substring(questionsString.indexOf((i + 1) + numberingStyleQuestion))
                        originalQuestionBlock = questionBlock
                        //replace double $$

                        questionBlock = questionBlock.replace('$', "#")
                        if (questionBlock.indexOf("##") > -1) {
                            boolean hasFormula = true

                            while (hasFormula) {
                                questionBlock = questionBlock.replaceFirst("##", "<span class=\"math-tex\">\\\\(")
                                questionBlock = questionBlock.replaceFirst("##", "\\\\)</span>")
                                if (questionBlock.indexOf('##') == -1) hasFormula = false
                            }
                        }


                        if (questionBlock.indexOf('#') > -1) {
                            boolean hasFormula = true
                            while (hasFormula) {
                                questionBlock = questionBlock.replaceFirst("#", "<span class=\"math-tex\">\\\\(")
                                questionBlock = questionBlock.replaceFirst("#", "\\\\)</span>")
                                if (questionBlock.indexOf('#') == -1) hasFormula = false
                            }
                        }
                    } else {
                        invalid = true
                        problemExists = true
                        issues += "C Question No " + (i + 1) + "\n"
                    }
                }
                questionsString = questionsString.substring(originalQuestionBlock.length())

                questionNumberString = (i + 1) + numberingStyleAnswer
                if (optionPrefixPresent) {
                    int firstIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1
                    int secondIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 2

                    if (secondIndex > firstIndex) {
                        correctAnswers[k] = answerKeys.substring(answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1, answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 2)
                    } else {
                        problemExists = true
                        issues += "Issue in the answers of Question No " + (i + 1) + "\n"
                    }
                } else {
                    int firstIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length()
                    int secondIndex = answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1

                    if (secondIndex > firstIndex) {
                        correctAnswers[k] = answerKeys.substring(answerKeys.indexOf(questionNumberString) + questionNumberString.length(), answerKeys.indexOf(questionNumberString) + questionNumberString.length() + 1)

                    } else {
                        problemExists = true
                        issues += "Issue in the answers of Question No " + (i + 1) + "\n"
                    }
                }

                if (invalid) {
                    questionsArray[k] = "-"
                } else {
                    if (questionBlock.indexOf(optionsStyle) != -1) {
                        questionsArray[k] = questionBlock.substring(questionBlock.indexOf((i + 1) + numberingStyleQuestion) + ((i + 1) + numberingStyleQuestion).length(), questionBlock.indexOf(optionHeaders[0]))
                        optionsHolder = questionBlock.substring(questionBlock.indexOf(optionsStyle))

                        for (int j = 0; j < noOfOptions; j++) {
                            if (j < (noOfOptions - 1)) {
                                if (optionsHolder.indexOf(optionHeaders[j]) != -1 && optionsHolder.indexOf(optionHeaders[j + 1]) != -1) {
                                    int firstIndex = optionsHolder.indexOf(optionHeaders[j]) + optionHeaders[j].length()
                                    int secondIndex = optionsHolder.indexOf(optionHeaders[j + 1])
                                    if (secondIndex > firstIndex) {
                                        optionKeys[k][j] = optionsHolder.substring(optionsHolder.indexOf(optionHeaders[j]) + optionHeaders[j].length(), optionsHolder.indexOf(optionHeaders[j + 1]))
                                    } else {
                                        problemExists = true
                                        issues += "Issue in the options of Question No " + (i + 1) + "\n"
                                        optionKeys[k][j] = "-"
                                    }
                                } else {
                                    problemExists = true
                                    issues += "Issue in the options of Question No " + (i + 1) + "\n"
                                    optionKeys[k][j] = "-"
                                }
                            } else {
                                if (optionsHolder.indexOf(optionHeaders[j]) != -1) {
                                    optionKeys[k][j] = optionsHolder.substring(optionsHolder.indexOf(optionHeaders[j]) + optionHeaders[j].length())
                                } else {
                                    optionKeys[k][j] = "-"
                                }
                            }
                        }
                    } else {
                        questionsArray[k] = "-"
                        for (int j = 0; j < noOfOptions; j++) {
                            optionKeys[k][j] = "-"
                        }
                    }
                }

                //extract answerExplanations
                if (answerDesc != null) {


                    if (i < (startingQuestionNo + noOfQuestions - 2)) {
                        if (answerDesc.indexOf((i + 1) + numberingStyleQuestion) != -1 && answerDesc.indexOf((i + 2) + numberingStyleQuestion) != -1) {
                            int firstIndex = answerDesc.indexOf((i + 1) + numberingStyleQuestion)
                            int secondIndex = answerDesc.indexOf((i + 2) + numberingStyleQuestion)

                            if (secondIndex > firstIndex) {
                                answerExplanations[k] = answerDesc.substring((firstIndex + ((i + 1) + numberingStyleQuestion).length()), secondIndex)

                            }
                        }
                    } else {
                        if (answerDesc.indexOf((i + 1) + numberingStyleQuestion) != -1)
                            answerExplanations[k] = answerDesc.substring(answerDesc.indexOf((i + 1) + numberingStyleQuestion))


                    }
                    answerDesc = answerDesc.substring(answerExplanations[k].length())

                    //replace double $
                    answerExplanations[k] = answerExplanations[k].replace('$', "#")
                    if (answerExplanations[k].indexOf("##") > -1) {
                        boolean hasFormula = true
                        while (hasFormula) {
                            answerExplanations[k] = answerExplanations[k].replaceFirst("##", "<span class=\"math-tex\">\\\\(")
                            answerExplanations[k] = answerExplanations[k].replaceFirst("##", "\\\\)</span><br>")
                            if (answerExplanations[k].indexOf('##') == -1) hasFormula = false
                        }
                    }

                    if (answerExplanations[k].indexOf('#') > -1) {
                        boolean hasFormula = true

                        while (hasFormula) {
                            answerExplanations[k] = answerExplanations[k].replaceFirst("#", "<span class=\"math-tex\">\\\\(")
                            answerExplanations[k] = answerExplanations[k].replaceFirst("#", "\\\\)</span><br>")
                            if (answerExplanations[k].indexOf('#') == -1) hasFormula = false
                        }
                    }


                }

                k++
            }

            [questionsArray : questionsArray, optionKeys: optionKeys, correctAnswers: correctAnswers, noOfOptions: noOfOptions, chapterId: params.chapterId, bookId: params.bookId,
             directionsArray: directionsArray, answerExplanations: answerExplanations, page: params.page]
        }catch(Exception e){
            render "the issue is "+issues+"\n"+e.toString()
        }
    }

    def updateFileSize(){
        List resources = ResourceDtl.findAllByFilenameIsNotNull()
        resources.each { resource ->
            File f =  new File(resource.resLink)
            if(f!=null && f.length()>0) {
                ResourceDtl.executeUpdate("update ResourceDtl set fileSize=" + f.length() + " where id=" + resource.id)
            }
        }
    }


    @Transactional
    def loginChecker(){
    }

    @Transactional
    def bookPurchased(){
        BooksPermission booksPermission=null
        if(params.bookId!=null && springSecurityService.currentUser!=null && springSecurityService.currentUser.username!=null)
            booksPermission = BooksPermission.findByBookIdAndUsernameAndBatchIdIsNull(new Long(params.bookId),springSecurityService.currentUser.username)

        def json = ['bookAlreadyPurchased':booksPermission!=null]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def notescreator(){
        def resLink=null,filename=null,resourceName=null,chapterUrl=null,testStartDate=null,subType=null
        ChaptersMst chaptersMst = null
        if(params.chapterId!=null) {
            chaptersMst = ChaptersMst.findById(new Integer(params.chapterId))
            chapterUrl="<a href='/wonderpublish/bookCreate?chapterId="+params.chapterId+"&bookId="+params.bookId+"'>"+chaptersMst.name+"</a>"
        }


            if(params.mode=='edit') {
                session.setAttribute("htmlId",new Integer(params.id))
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.id))
                resLink  = resourceDtl.resLink
                filename = resourceDtl.filename
                resourceName = resourceDtl.resourceName
                subType = resourceDtl.resSubType
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm")
                if(resourceDtl.testStartDate!=null) {
                    testStartDate = dateFormat.format(convertDate(resourceDtl.testStartDate,"UTC","IST"))
                }
            } else if(params.mode=='create' && session.getAttribute("htmlId")!=null)
                session.removeAttribute("htmlId")

            [chapterId: params.chapterId,bookId:params.bookId, chaptersMst: chaptersMst, title:chaptersMst!=null?chaptersMst.name:"",displayName:chapterUrl,
             resLink:resLink, filename:filename,testStartDate: testStartDate, resourceName:resourceName ,page:params.page,subType:subType]
    }

    @Secured(['ROLE_USER'])
    def addnotes(){
    }
    @Secured(['ROLE_USER'])
    def addnotesScripts(){
    }

    @Secured(['ROLE_USER'])
    def uploadContent() {
        def file = request.getFile('upload')
        def resourceId,htmlId,resLink

        if(session.getAttribute("htmlId")==null) {
            def resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.resLink = "empty"
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.chapterId = (params.chapterId!=null&&!"".equals(params.chapterId))?new Integer(params.chapterId):new Integer(-999)
            resourceDtlInstance.resourceName = "empty"
            resourceDtlInstance.quizMode=params.quizMode
            resourceDtlInstance.save(failOnError: true,flush: true)
            if("notes".equals(params.page)) {
                if (resourceDtlInstance.sharing == null) {
                    dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)

                } else {
                    dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
                }
            }

            htmlId = resourceDtlInstance.id
            session.setAttribute("htmlId",htmlId)
        } else {
            htmlId = session.getAttribute("htmlId")
        }
        String uploadParentDir="supload"

        if("notes".equals(params.page)) resLink = uploadParentDir+"/books/"+params.bookId+"/chapters/"+params.chapterId+"/"+htmlId+"/extract/OEBPS/Images/"+file.originalFilename
        else resLink = uploadParentDir+"/resources/"+htmlId+"/extract/OEBPS/Images/"+file.originalFilename
        if(file!=null && !file.empty) {
        println("reslink=${resLink}")
            File uploadDir

            if("notes".equals(params.page))  uploadDir = new File(grailsApplication.config.grails.basedir.path+uploadParentDir+"/books/"+params.bookId+"/chapters/"+params.chapterId+"/"+htmlId+"/extract/OEBPS/Images/")
            else uploadDir =  new File(grailsApplication.config.grails.basedir.path+uploadParentDir+"/resources/"+htmlId+"/extract/OEBPS/Images/")
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resLink))
        }

        String cdnPath = ""+grailsApplication.config.grails.cdn.path
        String imageUrl = '/funlearn/downloadEpubImage?source='+resLink
        if(!"localhost".equals(cdnPath))
            imageUrl = cdnPath+"/"+resLink
        def json =  [
                "uploaded": 1,
                "fileName": file.originalFilename,
                "url": imageUrl
        ]

        render json as JSON
    }


    def storeBookIdForPurchase(){
        session['bookIdForPurchase']=params.bookId
        def json =  [
                "stored":"true"
        ]

        render json as JSON
    }





    @Secured(['ROLE_USER'])
    def addFIBByFile() {
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')
        def msgStr
        WorkbookSettings settings
        Workbook workbook
        Sheet sheet
        flash.message=""
        def ranNum = 0

        if(!file.originalFilename.endsWith(".xml")) {
            settings = new WorkbookSettings()
            settings.setSuppressWarnings(true)
            workbook = Workbook.getWorkbook(file.getInputStream(),settings)
            sheet = workbook.getSheet(0)

            flash.message = "Document being processed is <b>"+file.originalFilename+"</b><br>"
            // skip first row (row 0) by starting from 1
            flash.message += "Total number of rows is "+sheet.getRows()+"<br>"
        }

        //checks before actual writing
        if("Multiple Choice Questions XML".equals(params.resourceType) && file!=null && !file.empty) {
            ranNum = ((int)(Math.random()*1001))
            File uploadDir = new File(grailsApplication.config.grails.basedir.path+"upload/books/"+params.bookId+"/chapters/"+params.chapterId+"/processed/")
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(uploadDir.getAbsolutePath()+File.separator+ranNum+"_"+file.originalFilename))
            msgStr = validateXmlFile(uploadDir.getAbsolutePath()+File.separator+ranNum+"_"+file.originalFilename)
            if (msgStr) parseXmlFile1(uploadDir.getAbsolutePath()+File.separator+ranNum+"_"+file.originalFilename)
        } else if("Multiple Choice Questions".equals(params.resourceType))
            msgStr = quizExtractorService.checkMCQ(sheet)?"":"false"
        else if("Fill in the blanks".equals(params.resourceType))
            msgStr = quizExtractorService.checkFillintheblanks(sheet)
        else
            msgStr = quizExtractorService.checkOthers(sheet)

        //writing to db only if spreadsheet passes all checks
        if(msgStr==null || msgStr.equals("")) {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()

            if("Multiple Choice Questions".equals(params.resourceType))
                quizExtractorService.extractMCQ(sheet,quizIdGenerator.id,params.resourceType,params.topicId,springSecurityService.currentUser.username)
            else if("Fill in the blanks".equals(params.resourceType))
                quizExtractorService.extractFillintheblanks(sheet,quizIdGenerator.id,params.resourceType)
            else
                quizExtractorService.extractOthers(sheet,quizIdGenerator.id,params.resourceType)

            def resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.filename = file.originalFilename
            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            if(params.chapterId!=null&&""!=params.chapterId)
                resourceDtlInstance.chapterId = new Integer(params.chapterId)
            else
                resourceDtlInstance.topicId = new Integer(params.topicId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true)
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }

            flash.message += "<br><br>Quiz for <b><u>"+params.resourceName+"</u></b> was added successfully! Thanks for your contribution. You can use this quiz and share it with your friends now. In order to share with everybody, please use the <b>Publish</b> option."
        } else if(!msgStr) { //handling xml processing mcqs boolean
            flash.message += msgStr
            flash.message += "<br><br><font color=red>Please rectify the above error before uploading the document again.</font>"
        }
        if("sage".equals(session["entryController"]))  {
            redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId])
        }
        else{
            redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId])
        }
    }

    @Secured(['ROLE_USER'])
    def qandaCreator(){

        if(params.chapterId ==null || ''.equals(params.chapterId))

            redirect(action:'index')

        else {

            ChaptersMst chaptersMst = ChaptersMst.findById(new Integer(params.chapterId))


            String chapterUrl="<a href='/wonderpublish/bookCreate?chapterId="+params.chapterId+"&bookId="+params.bookId+"'>"+chaptersMst.name+"</a>"



            if(redisService.("competitiveExams")==null){

                dataProviderService.getCompetitiveExams()

            }



            List competitiveExams = new JsonSlurper().parseText(redisService.("competitiveExams"))



            if("edit".equals(params.mode)){

                if(params.id ==null || ''.equals(params.id))  redirect(action:'index')

                else{

                    ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.id))

                    List objectives = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink),[sort:"id"])

                    [chapterId: params.chapterId, chaptersMst: chaptersMst,bookId:params.bookId, resourceDtl: resourceDtl, objectives: objectives, title:chaptersMst.name, displayName:chapterUrl,competitiveExams:competitiveExams, hideBanner: true]

                }

            } else {

                [chapterId: params.chapterId,bookId:params.bookId, chaptersMst: chaptersMst, title:chaptersMst.name,displayName:chapterUrl, hideBanner: true]

            }

        }

    }

    def uploadTex() {
    }


    @Transactional
    def addFreeBookToLibrary(){
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        if(booksMst!=null&&(booksMst.price==0&&!"print".equals(booksMst.bookType))) {
            Integer siteId = getSiteId(request)
            if(siteId.intValue()==12 || siteId.intValue()==23) {
                String ipAddress = utilService.getIPAddressOfClient(request)
                InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
                Long instituteId
                if (instituteIPAddress == null) {
                    String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                            " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + session["siteId"]

                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql1 = new Sql(dataSource)
                    def results = sql1.rows(sql)
                    if (results.size() > 0) {
                        instituteId = results[0][0]

                    } else {
                        def json = ['status': "Invalid IP range"]
                        render json as JSON
                        return
                    }

                } else {
                    instituteId = instituteIPAddress.institute_id

                }
                String sql = "select bbd.* from wsuser.course_batches_dtl cbd, wsuser.books_batch_dtl bbd where" +
                        " cbd.conducted_by="+instituteId+" and bbd.batch_id=cbd.id and" +
                        " bbd.book_id="+booksMst.id
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                if(results.size()==0){
                    def json = ['status': "Book Not Available For This IP"]
                    render json as JSON
                    return
                }
            }
            BooksPermission booksPermission = new BooksPermission()
            booksPermission.bookId = new Long(params.bookId)
            booksPermission.username = springSecurityService.currentUser.username
            booksPermission.poType = 'ADDEDFORFREE'
            booksPermission.save(failOnError: true, flush: true)
            if(siteMst.allBooksLibrary=="true" || siteId.intValue()==25) dataProviderService.getBooksListForUser()
            if(siteMst.allBooksLibrary!="true")redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
            if("test".equals(booksMst.bookType)){
                session["testBookId"]=booksMst.id
            }
            def json = ['status': "added",bookType:booksMst.bookType]
            render json as JSON
        } else {
            def json = ['status': "invalid"]
            render json as JSON
        }

    }

    def boolean validMathjax(String mathStr){
        if(mathStr.indexOf('^')!=-1||mathStr.indexOf('_')!=-1||mathStr.indexOf('\\')!=-1)
            return true
        else return false
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    def addImagesZip(){
        def file = params.file

        if(file.empty) {
            flash.message = "File c" +
                    " be empty"

        } else {
            File uploadDir = new File("upload/books/" +params.bookId+"/chapters/"+params.chapterId+"/imagesfrompdf/")
            if(!uploadDir.exists()) uploadDir.mkdirs()

            file.transferTo(new File(uploadDir.absolutePath+"/"+file.originalFilename.replaceAll("\\s+","")))
            unzip(uploadDir.getAbsolutePath()+"/"+file.originalFilename.replaceAll("\\s+",""),uploadDir.getAbsolutePath(),false,null,false)

        }
        if("sage".equals(session["entryController"]))  {
            redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId, 'toc': "false", "imageFileuploaded": "true"])
        }
        else{
            redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId, 'toc': "false", "imageFileuploaded": "true"])
        }
    }

    def buyOrAdd(){}

    def generateSiteMap(){
        String url=request.getRequestURL()
        url = url.substring(0,url.indexOf('/',8))
        sitemapService.sitemap(url)
        render "done"
    }

    def generatePrintSiteMap(){
        String url=request.getRequestURL()
        url = url.substring(0,url.indexOf('/',8))
        sitemapService.generatePrintbooksSiteMap(url)
        render "done"
    }


    @Secured(['ROLE_WS_CONTENT_CREATOR'])
    def flushRedis(){
          redisService.flushDB()
    }


    @Secured(['ROLE_USER']) @Transactional
    def annotateSave() {

        String jsonObject = request.getJSON()
        def jsonList = new JsonSlurper().parseText(jsonObject)
        //print jsonList
        //print jsonList.tags
        boolean createdByWsEditor = false
        String epubChapterLink = jsonList.ebupChapterLink,pdfReaderLibrary = jsonList.pdfReaderLibrary,indexes = new JsonBuilder(jsonList.indexes).toPrettyString();
        AnnotatorMst annotator = null
        if(jsonList.uri != null && (epubChapterLink == null || epubChapterLink.isEmpty())) createdByWsEditor = dataProviderService.getResourceDtl(new Long(jsonList.uri)).modifiedFile == null?true:false
        if(epubChapterLink != null && !epubChapterLink.isEmpty()){
            ResourceDtl resourceDtl = null
            if(jsonList.bookId!=null) {
                if (redisService.("BookResIds_" + jsonList.bookId) == null) {
                    dataProviderService.getResIdsOfBook(jsonList.bookId)
                }
                String resIds = redisService.("BookResIds_" + jsonList.bookId)
                resourceDtl = ResourceDtl.findByIdInListAndEbupChapterLink(Arrays.asList(resIds.split(",")),epubChapterLink)
                annotator = new AnnotatorMst(username: springSecurityService.currentUser.username,
                        siteId: getSiteId(request), quote: jsonList.quote, text: jsonList.text, uri: resourceDtl.id,
                        ranges: new JsonBuilder(jsonList.ranges).toPrettyString(), dateCreated: new Date(),
                        tags: new JsonBuilder(jsonList.tags).toPrettyString(), status: 'A',
                        isSingleEpubFile: true,annotateColor: jsonList.annotateColor)
            }
        }else if(pdfReaderLibrary != null && !pdfReaderLibrary.isEmpty() && pdfReaderLibrary.equals("yes")){
            annotator = new AnnotatorMst(username: springSecurityService.currentUser.username,
                    siteId: getSiteId(request), quote: jsonList.quote, text: jsonList.text, uri: jsonList.uri,
                    ranges: indexes, dateCreated: new Date(),
                    tags: new JsonBuilder(jsonList.tags).toPrettyString(), status: 'A',annotateColor: jsonList.annotateColor)
        } else {
            annotator = new AnnotatorMst(username: springSecurityService.currentUser.username,
                    siteId: getSiteId(request), quote: jsonList.quote, text: jsonList.text, uri: jsonList.uri,
                    ranges: new JsonBuilder(jsonList.ranges).toPrettyString(), dateCreated: new Date(),
                    tags: new JsonBuilder(jsonList.tags).toPrettyString(), status: 'A',annotateColor: jsonList.annotateColor)
        }

        annotator.save(failOnError: true,flush: true)
       if(jsonList.quoteAndClassNamesArr != null && createdByWsEditor == false){
           def json1 = new groovy.json.JsonBuilder()
         def quoteArr = json1(jsonList.quoteAndClassNamesArr)
        for(int i=0;i< quoteArr.size();i++){

            if(quoteArr.get(i).get("className") != null && quoteArr.get(i).get("className") != ""){
                AnnotatorQuoteDetails annotatorQuoteDetails = new AnnotatorQuoteDetails(quoteText: quoteArr.get(i).get("quote"), annoTatorMstId: annotator.id, qoutrTextClassNames: quoteArr.get(i).get("className"))
                annotatorQuoteDetails.save(failOnError: true,flush: true)
            }
        }
       }

        def json
        if(annotator!=null) {
            if(pdfReaderLibrary == null || pdfReaderLibrary.isEmpty() || !pdfReaderLibrary.equals("yes")){
                json = [
                        'status':'success',
                        'id':annotator.id
                ]
            }else {
                json = [
                        'status':'success',
                        'id':annotator.id,
                        'text':annotator.text,
                        'pdfReaderLibrary':pdfReaderLibrary
                ]
            }
        } else {
            json = [ 'status':'error' ]
        }

        render json as JSON
    }
    @Secured(['ROLE_USER']) @Transactional
    def annotateSearch() {
        String epubChapterLink = params.ebupChapterLink,uri = ""

        if(epubChapterLink != null && !epubChapterLink.isEmpty()){
            ResourceDtl resourceDtl = null
            if(params.bookId!=null) {
                if (redisService.("BookResIds_" + params.bookId) == null) {
                    dataProviderService.getResIdsOfBook(params.bookId)
                }
                String resIds = redisService.("BookResIds_" + params.bookId)
                resourceDtl = ResourceDtl.findByIdInListAndEbupChapterLink(Arrays.asList(resIds.split(",")),epubChapterLink)
            }
            uri = resourceDtl.id + ""
        }else uri = params.uri

        List annotator = AnnotatorMst.findAllByUriAndUsernameAndStatus(uri,springSecurityService.currentUser.username,'A')

        def cnt = 0

        List annotator1 = annotator.collect{annotator2 ->
            cnt++
            return [ quote: annotator2.quote, ranges:new JsonSlurper().parseText(annotator2.ranges), text:annotator2.text, id: annotator2.id, uri: annotator2.uri, tags: annotator2.tags!=null?new JsonSlurper().parseText(annotator2.tags):"", user: annotator2.username,  permissions: [read: [annotator2.username],admin: [annotator2.username],update: [annotator2.username],delete: [annotator2.username]], annotateColor: annotator2.annotateColor]
        }
        if(params.bookId!=null) {
            //firt step is to find if the user in any Batches
            List userBatches = BatchUserDtl.findAllByUsernameAndInstructorIsNull(springSecurityService.currentUser.username)
            userBatches.each { userBatch ->
                BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBatchIdAndBookId(userBatch.batchId,new Long(params.bookId))
                if(booksBatchDtl!=null) {
                    List instructors = BatchUserDtl.findAllByBatchIdAndInstructorIsNotNull(userBatch.batchId)
                    instructors.each { instructor ->
                        annotator = AnnotatorMst.findAllByUriAndUsernameAndStatus(uri, instructor.username, 'A')
                        annotator.each { annotator2 ->
                            cnt++
                            annotator1.add([quote      : annotator2.quote, ranges: new JsonSlurper().parseText(annotator2.ranges), text: annotator2.text,
                                            id         : annotator2.id, uri: annotator2.uri,
                                            tags       : annotator2.tags != null ? new JsonSlurper().parseText(annotator2.tags) : "",
                                            user       : annotator2.username,
                                            permissions: [read: [annotator2.username]],annotateColor: annotator2.annotateColor])
                        }

                    }
                }
            }
        }

        def json =  [
                'total': annotator!=null?cnt:0,
                'rows': annotator1
        ]


        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAnnotationsForFullBook() {
        String resIds
        if(params.bookId!=null) {
            if (redisService.("BookResIds_" + params.bookId) == null) {
                dataProviderService.getResIdsOfBook(params.bookId)
            }

            resIds = redisService.("BookResIds_" + params.bookId)
        }else{
            if(redisService.("defaultResourceIDs_"+params.chapterId)==null)
            dataProviderService.getChapterDefaultResourcesAsString(new Long(params.chapterId))
            resIds = redisService.("defaultResourceIDs_"+params.chapterId)
        }
        List annotator = AnnotatorMst.findAllByUriInListAndUsernameAndStatusNotEqual(Arrays.asList(resIds.split(",")),springSecurityService.currentUser.username,'D')
        def cnt = 0
        ChaptersMst chaptersMst
        ResourceDtl resourceDtl
        List annotator1 = annotator.collect{annotator2 ->
            resourceDtl = dataProviderService.getResourceDtl(new Long(annotator2.uri))
            chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            cnt++

            List annotatorQuoteDetails = AnnotatorQuoteDetails.findAllByAnnoTatorMstId(annotator2.id)
             return [ quote: annotator2.quote.replaceAll("__"," "), ranges:new JsonSlurper().parseText(annotator2.ranges),
                     text:annotator2.text, id: annotator2.id, uri: annotator2.uri,
                     tags: annotator2.tags!=null?new JsonSlurper().parseText(annotator2.tags):"",
                     user: annotator2.username,
                      annotateColor: annotator2.annotateColor,
                     permissions: [read: [annotator2.username],admin: [annotator2.username],
                                   update: [annotator2.username],delete: [annotator2.username]],
            resId:resourceDtl.id,chapterId: resourceDtl.chapterId,chapterName:chaptersMst.name]
        }

        def json =  [
                'total': annotator!=null?cnt:0,
                'rows': annotator1,
        ]


        render json as JSON
    }
    @Secured(['ROLE_USER']) @Transactional
    def annotateUpdate() {
        String jsonObject = request.getJSON()
        def jsonList = new JsonSlurper().parseText(jsonObject)
        boolean updated = false

        AnnotatorMst annotator = AnnotatorMst.findById(new Long(params.id))
        if(annotator.username.equals(springSecurityService.currentUser.username)) {
            annotator.quote = jsonList.quote
            annotator.text = jsonList.text
            annotator.tags = new JsonBuilder(jsonList.tags).toPrettyString()
            annotator.dateUpdated = new Date()
            updated = true
        }

        annotator.save(failOnError: true,flush: true)

        def json
        if(updated) {
            json = [
                    'status':'success',
                    'id':annotator.id
            ]
        } else {
            json = [ 'status':'error' ]
        }

        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def pdfAnnotateUpdate() {
        String jsonObject = request.getJSON()
        def jsonList = new JsonSlurper().parseText(jsonObject)
        boolean updated = false

        AnnotatorMst annotator = AnnotatorMst.findById(new Long(params.id))
        if(annotator.username.equals(springSecurityService.currentUser.username)) {
            annotator.text = jsonList.text
            annotator.dateUpdated = new Date()
            updated = true
        }

        annotator.save(failOnError: true,flush: true)

        def json
        if(updated) {
            json = [
                    'status':'success',
                    'id':annotator.id
            ]
        } else {
            json = [ 'status':'error' ]
        }

        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def annotateDestroy() {
        boolean deleted=false
        AnnotatorMst annotator = AnnotatorMst.findById(new Long(params.id))
        //annotator.delete(failOnError: true,flush: true)
        if(annotator.username.equals(springSecurityService.currentUser.username)) {
            annotator.status = 'D'
            annotator.save(failOnError: true, flush: true)
            deleted = true
        }
        def json
        if(deleted) {
            json = [
                    'status':'success',
                    'id':null
            ]
        } else {
            json = [ 'status':'error' ]
        }

        render json as JSON
    }

    def tester(){
        render "working fine"
    }


    def evaluateAnswers(){

        render  sorensenDiceService.comparePassage(params.idealAnswer,params.userAnswer)
    }



   @Secured(['ROLE_USER']) @Transactional
   def getInstructorResources() {
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        if (session["siteId"] != null && (session["siteId"].intValue() == 9) && (session["siteId"].intValue() == booksMst.siteId.intValue())) {
           updateBookView(params.bookId,"web","library")
            String sql = "select ir.id,ir.book_id, ir.created_by, ir.date_created, ir.link,ir.link_name,ir.sub_tab"+
                    " from instructor_resources ir where book_id="+params.bookId+"";
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            List instructorResources = new ArrayList()
                    results.each {user ->
                        instructorResources.add([ id: user[0], bookId: user[1], createdBy: user[2], dateCreated: user[3],link: user[4],linkName: user[5],subTab:user[6]])
            }
           def json = ["instructorResources":instructorResources,"book":booksMst]
            render json as JSON
        } else render ""
    }




    def deleteResource(){
        if (params.id==null) return

        ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.id))

        def json
        if(resourceDtl!=null) {
            def chapId = resourceDtl.chapterId
            resourceDtl.chapterId = new Long(resourceDtl.chapterId.intValue()*-1)
            resourceDtl.deletedBy=springSecurityService.currentUser.username
            resourceDtl.save(flush:true,failOnError: true)
            dataProviderService.getChapterDefaultResources(chapId)
            dataProviderService.getChapterDefaultResourcesAsString(chapId)
            dataProviderService.getChapterResourcesForUser(chapId,springSecurityService.currentUser.username)
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapId)
            if(chaptersMst!=null) {
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
                dataProviderService.quizPresent(chaptersMst.bookId)
            }
            json = ["status":"deleted"]
        } else {
            json = ["status":"not found"]
        }

        render json as JSON
    }

    def infiniteScroll() {
        render ""
    }

    @Transactional
    def updateBookViewCount(){
        asyncLogsService.updateBookView(params.bookId, params.viewSource, params.viewType, getSiteId(request),
                (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),params.instituteId)
    }

    @Transactional
    def updateBookView(bookId,viewSource,viewType){
        if("preview".equals(viewType)) {
            BooksMst booksMst  = dataProviderService.getBooksMst(new Long(bookId))
            if (booksMst.noOfViews == null) booksMst.noOfViews = new Integer(1)
            else booksMst.noOfViews = new Integer((booksMst.noOfViews.intValue() + 1))
            booksMst.save(flush: true, failOnError: true)

        }

        // now save booksviewdtl
        Integer siteId = getSiteId(request)
        BooksViewDtl booksViewDtl
        if(siteId.intValue()==12 || siteId.intValue()==23 ||siteId.intValue()==24) {
            int instituteId
            String ipAddress = utilService.getIPAddressOfClient(request)
            InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
            if (instituteIPAddress != null) {
                instituteId = instituteIPAddress.institute_id
                booksViewDtl = new BooksViewDtl(bookId: new Long(bookId), viewSource: viewSource, viewType: viewType, username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                        siteId: getSiteId(request),instituteId:instituteId)
            }else{
                String sql =   "SELECT im.id FROM wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.batch_user_dtl bud"+
                        " WHERE im.id = cbd.conducted_by" +
                        "  AND bud.batch_id = cbd.id" +
                        "   AND bud.username='"+springSecurityService.currentUser.username+"'"
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                results.each { result ->
                    instituteId=result[0]
                }
                booksViewDtl = new BooksViewDtl(bookId: new Long(bookId), viewSource: viewSource, viewType: viewType, username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                        siteId: getSiteId(request),instituteId: instituteId)
            }
        }else{
            booksViewDtl = new BooksViewDtl(bookId:new Long(bookId), viewSource: viewSource, viewType:viewType,username:(springSecurityService.currentUser!=null?springSecurityService.currentUser.username:""),
                    siteId:getSiteId(request))
        }
        booksViewDtl.save(flush:true, failOnError: true)

    }

    def readingSection(){}
    def qaSection(){}
    def quizSection(){}
    def videoSection(){}
    def flashCardSection(){}
    def additionalReferenceSection(){}
    def notesHighlightSection(){}

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def manageTabs(){
        Integer siteId = getSiteId(request)
      List levelsMst = com.wonderslate.data.LevelsMst.findAllBySiteId(siteId,[sort: "name"])
        if(levelsMst.size()==0) levelsMst = com.wonderslate.data.LevelsMst.findAllBySiteId(new Integer(1),[sort: "name"])
        [levelsMst:levelsMst,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addSyllabus(){
        String gradeType=null
        String status="OK"
        Integer siteId = getSiteId(request)
        if("College".equals(params.level)) gradeType = "Semester"
        else if(!("School".equals(params.level))) gradeType = "Seperate"
        LevelSyllabus levelSyllabus = LevelSyllabus.findByLevelIlikeAndSyllabusIlikeAndSiteId("%"+params.level+"%","%"+params.syllabus+"%",siteId)
        if(levelSyllabus==null) {
            levelSyllabus = new LevelSyllabus(level: params.level, syllabus: params.syllabus,
                    createdBy: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                    gradeType: gradeType,degreeType: params.degreeType,siteId: getSiteId(request))
            levelSyllabus.save(flush:true, failOnError: true)

            //add all subjects for college
            if(!"School".equals(params.level)){
                SyllabusSubject syllabusSubject = new SyllabusSubject(subject: "All subjects", syllabus: params.syllabus,
                        createdBy: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""))
                syllabusSubject.save(flush: true, failOnError: true)
            }
        }
        else{
            status="Duplicate"
        }

        def json = ['status' : status]
        render json as JSON
    }
    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addSubject(){
        String status="Duplicate"
        SyllabusSubject syllabusSubject = SyllabusSubject.findBySubjectIlikeAndSyllabusIlike(params.subject,params.syllabus)
        if(syllabusSubject==null) {
            syllabusSubject = new SyllabusSubject(subject: params.subject, syllabus: params.syllabus,
                    createdBy: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""))
            syllabusSubject.save(flush: true, failOnError: true)
            status="OK"

        }
        def json = ['status' : status]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addPublisher() {
        Publishers  publishers = Publishers.findByNameAndSiteId(params.publisher,getSiteId(request))
        String status = "Already exists"
        if(publishers==null) {
            publishers = new Publishers(name: params.publisher,
                    createdBy:(springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                    siteId: getSiteId(request))
            publishers.save(failOnError: true, flush: true)
            dataProviderService.getPublishers(getSiteId(request))
            status = "Created"
        }
        def json =  [
                'publisherId': publishers.id, 'publisher': publishers.name, status: status
        ]

        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addExam(){
        String status="Duplicate"
        Integer siteId = getSiteId(request)
        SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(params.syllabus,params.grade)
        if(syllabusGradeDtl==null) {
            syllabusGradeDtl = new SyllabusGradeDtl(grade: params.grade, syllabus: params.syllabus,state:params.state,
                    createdBy: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                    sort: new Integer(0),siteId: getSiteId(request))
            syllabusGradeDtl.save(flush: true, failOnError: true)
            status="OK"
        }
        dataProviderService.getMainCategories(siteId,null)
        def json = ['status' : status]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def getSyllabusDetails() {
        List exams = SyllabusGradeDtl.findAllBySyllabus(params.syllabus)
        List subjects =   SyllabusSubject.findAllBySyllabus(params.syllabus)
        Integer siteId = getSiteId(request)
        List featuredGrades = FeaturedGradeDtl.findAllBySiteId(siteId)
        def json = ["grades":exams, "subjects":subjects, "featuredGrades":featuredGrades]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def manageExams(){
        [showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    def getExamMstDtls(){
        ExamMst examMst = ExamMst.findById(new Long(params.examId))
        def json = [status:examMst==null?"No data":"Data present", examMst:examMst]
        render json as JSON
    }

    def saveExamMst(){
        ExamMst examMst = ExamMst.findById(new Long(params.examId))
        if(examMst==null){
            examMst = new ExamMst(gradeId: new Long(params.gradeId))
            examMst.save(failOnError: true, flush: true)
        }
        if(params.examDetails!=null) examMst.examDetails = params.examDetails else examMst.examDetails=null
        if(params.noOfSections!= null && params.noOfSections.length()>0) examMst.noOfSections = new Integer(params.noOfSections) else examMst.noOfSections = null
        if(params.noOfQuestions!= null&& params.noOfQuestions.length()>0) examMst.noOfQuestions = new Integer(params.noOfQuestions) else examMst.noOfQuestions = null
        if(params.totalMarks!= null&& params.totalMarks.length()>0) examMst.totalMarks = new Integer(params.totalMarks) else examMst.totalMarks = null
        if(params.rightAnswerMarks!= null&& params.rightAnswerMarks.length()>0) examMst.rightAnswerMarks = new Double(params.rightAnswerMarks) else examMst.rightAnswerMarks = null
        if(params.wrongAnswerMarks!= null&& params.wrongAnswerMarks.length()>0) examMst.wrongAnswerMarks = new Double(params.wrongAnswerMarks) else examMst.wrongAnswerMarks = null
        if(params.totalTime!= null&& params.totalTime.length()>0) examMst.totalTime = new Integer(params.totalTime) else examMst.totalTime = null
        if(params.examInstructions!=null) examMst.examInstructions = params.examInstructions else examMst.examInstructions=null
        examMst.save(failOnError: true, flush: true)

        def json = ["status":"saved",examMst: examMst]
        render json as JSON
    }

    @Transactional
    def updateFeaturedGrade(){
        String featured = params.featured
        String gradeId = params.gradeId
        Integer siteId =  getSiteId(request)
        FeaturedGradeDtl featuredGradeDtl = FeaturedGradeDtl.findByGradeIdAndSiteId(new Long(gradeId), siteId)
        String status =""
        if("true".equals(featured)) {
            if(featuredGradeDtl==null){
                featuredGradeDtl =  new FeaturedGradeDtl(gradeId:new Long(gradeId), siteId: siteId)
                featuredGradeDtl.save(failOnError: true, flush: true)
            }else{
                //already exists nothing to do
            }
            status = "Added"
        }else{
            if(featuredGradeDtl!=null){
                featuredGradeDtl.delete(flush:true)
            }else{
                //nothing to do as the record is not there
            }
            status = "Removed"
        }
        dataProviderService.getFeaturedCategories(siteId)

        def json = ["status": status]
        render json as JSON

    }


    def saveExamDtl(){
        ExamDtl examDtl = ExamDtl.findByExamIdAndSubject(new Long(params.examId),params.subject)
        if(examDtl==null){
            examDtl = new ExamDtl(examId: new Long(params.examId),subject: params.subject)
            examDtl.save(failOnError: true, flush: true)
        }
        if(params.sectionDetails!=null) examDtl.sectionDetails = params.sectionDetails else examDtl.sectionDetails=null
        if(params.noOfQuestions!= null&& params.noOfQuestions.length()>0) examDtl.noOfQuestions = new Integer(params.noOfQuestions) else examDtl.noOfQuestions = null
        if(params.totalMarks!= null&& params.totalMarks.length()>0) examDtl.totalMarks = new Integer(params.totalMarks) else examDtl.totalMarks = null
        if(params.rightAnswerMarks!= null&& params.rightAnswerMarks.length()>0) examDtl.rightAnswerMarks = new Integer(params.rightAnswerMarks) else examDtl.rightAnswerMarks = null
        if(params.wrongAnswerMarks!= null&& params.wrongAnswerMarks.length()>0) examDtl.wrongAnswerMarks = new Double(params.wrongAnswerMarks) else examDtl.wrongAnswerMarks = null
        if(params.totalTime!= null&& params.totalTime.length()>0) examDtl.totalTime = new Integer(params.totalTime) else examDtl.totalTime = null
        examDtl.save(failOnError: true, flush: true)
        dataProviderService.getExamDtls(new Long(params.examId))
        def json = ["status":"saved",examDtl: examDtl]
        render json as JSON
    }

    def getExamDtls(){
        List examDtl = ExamDtl.findAllByExamId(new Long(params.examId))
        def json = [status:examDtl==null?"No data":"Data present", examDtl:examDtl]
        render json as JSON

    }

    def getExamSubjects(){
        ExamMst examMst = ExamMst.findById(params.examId)
        List examDtl=null
        if(examMst!=null) examDtl = ExamDtl.findAllByExamId(examMst.id)
        def json = [status:examDtl==null?"No data":"Data present", examDtl:examDtl]
        render json as JSON
    }



    private Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss"
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern)
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone))

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern)
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone))
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom))
        return dateTo
    }



    def millisecondstoTime(millis) {
        def hours = TimeUnit.MILLISECONDS.toHours(millis)
        def minutes = TimeUnit.MILLISECONDS.toMinutes(millis) - (hours * 60)
        def seconds = TimeUnit.MILLISECONDS.toSeconds(millis) - ((hours * 60 * 60) + (minutes * 60))
        millis = millis - ((hours * 60 * 60 * 1000) + (minutes * 60 * 1000) + (seconds * 1000))
        seconds = (seconds >= 10) ? seconds : "0" + seconds
        if (hours != 0) {
            return hours + ":" + minutes + ":" + seconds+ " (Hours : Minutes : Seconds)"
        } else {
            return minutes + ":" + seconds + " (Minutes : Seconds)"
        }
    }

    def time(){}


    @Secured(['ROLE_USER'])
    def getTestSeriesDtl(){
        if(redisService.("test_series_"+params.bookId)==null) dataProviderService.getTestSeriesDtl(params.bookId)
        List quizDetails =  new JsonSlurper().parseText(redisService.("test_series_"+params.bookId))

        def json = ['quizDetails':quizDetails]
        render json as JSON
    }





    def updateResourceData() {
        def downloadlink1=params.downloadlink1;
        def downloadlink2=params.downloadlink2;
        def downloadlink3=params.downloadlink3;
        Date startDate
        Date endDate
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm")
        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        resourceDtl.resLink = params.reslink
        resourceDtl.resourceName = params.restitle
        resourceDtl.videoPlayer = params.videoPlayer
        resourceDtl.allowComments = params.allowComments
        resourceDtl.displayComments = params.displayComments
        resourceDtl.quizMode = params.quizMode?params.quizMode:null
        resourceDtl.indContentType=params.indContentType?params.indContentType:null
        resourceDtl.downloadlink1 = downloadlink1?downloadlink1.replaceAll('~','&'):null;
        resourceDtl.downloadlink2 = downloadlink2?downloadlink2.replaceAll('~','&'):null;
        resourceDtl.downloadlink3 = downloadlink3?downloadlink3.replaceAll('~','&'):null;
        if(params.testStartDate != null && params.testStartDate != "" && params.testStartDate != 'Invalid date'){
            startDate = df.parse(params.testStartDate)
            startDate = utilService.convertDate(startDate,"IST","UTC")
            resourceDtl.testStartDate = startDate
        }
        if(params.testEndDate != null && params.testEndDate != "" && params.testEndDate != 'Invalid date'){
            endDate = df.parse(params.testEndDate)
            endDate = utilService.convertDate(endDate,"IST","UTC")
            resourceDtl.testEndDate = endDate
        }

        resourceDtl.save(flush: true, failOnError: true)
        dataProviderService.resourceCacheUpdate(resourceDtl.id)
         ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        if(chaptersMst!=null) dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
        Integer siteId = getSiteId(request)
        String siteIdList = siteId.toString()
        dataProviderService.getLatestVideos(siteIdList,siteId)
        dataProviderService.getLiveVideos(siteId)

        if(params.indContentType!=null && !"".equals(params.indContentType)) {
            redisService.("getindependentResourcDetails" + params.page + "_" + utilService.getSiteId(request,session)) = null
            redisService.("getindependentResourcDetails" + params.page + "_" + utilService.getSiteId(request,session) + "_recordCount") = null
            dataNotificationService.independentResourceUpdated(resourceDtl.id, utilService.getSiteId(request,session))
        }
        def json = ["status":"success"]
        render json as JSON
    }

    @Transactional
    def validateIsbn(){
        Integer siteId = getSiteId(request)
        def valid
        BooksMst booksMst = BooksMst.findByIsbnAndSiteId(params.isbn,siteId)
        if(booksMst!=null){
            valid="error"
        } else {
            valid="success"
            BooksMst.executeUpdate("update BooksMst set isbn ='" + params.isbn + "' where id=" + params.bookId)
            BooksMst.wsshop.executeUpdate("update BooksMst set isbn ='" + params.isbn + "' where id=" + params.bookId)
            BooksMst.wsuser.executeUpdate("update BooksMst set isbn ='" + params.isbn + "' where id=" + params.bookId)
            dataProviderService.getWSPublishedMyBooks();
            dataProviderService.getWSUnpublishedMyBooks();
        }

        def json = [
                'valid':valid,
        ]

        render json as JSON
    }

    def getPrintedBooksDetail(){
        def res
        def baseUrl = new URL('https://ws-in.amazon-adsystem.com/widgets/q?Operation=GetResults&' +
                'Keywords=harry%20potter' + // keywords parameter should contain the search string in url encoded form
                '&SearchIndex=All&multipageStart=0&InstanceId=0&multipageCount=10&TemplateId=MobileSearchResults&ServiceVersion=20070822&MarketPlace=IN')
        HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
        connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
        connection.with {
            doOutput = true
            requestMethod = 'GET'
            res = content.text
            res = res.substring(17,res.length()-2) // to get the data string
        }

        def  booksReceived = new JsonSlurper().parseText(addDoubleQuotes(res))
        def books = booksReceived.results
        for(int i=0;i<books.size();i++) {
            addPrintBooks(asin:books[i].ASIN)
        }

        render res
    }

    def addDoubleQuotes(inputString){
        return inputString.replace("results","\"results\"").replace(" , "," , \"").replace(" : \"","\" : \"").replace("\"{","{").replace("ASIN\"","\"ASIN\"").replace("NumRecords\"","\"NumRecords\"").replace("CorrectedQuery\"","\"CorrectedQuery\"").replace("MarketPlace","\"MarketPlace\"").replace("InstanceId","\"InstanceId\"")
    }

    @Transactional
    def addPrintBooks(){
        BooksMst booksMst = BooksMst.findByAsin(params.asin)
        if(booksMst==null) {
            booksMst = new BooksMst(title: params.title, description: params.title,
                    coverImage: params.coverImage, buylink1: params.bookLink, status: 'published', asin: params.asin, datePublished: new Date(),
                    siteId: getSiteId(request),bookType: "print")

            booksMst.save(failOnError: true, flush: true)
            BooksMst booksMst1,booksMst2
            booksMst1 = new BooksMst(id:booksMst.id,title: params.title, description: params.title,
                    coverImage: params.coverImage, buylink1: params.bookLink, status: 'published', asin: params.asin, datePublished: new Date(),
                    siteId: getSiteId(request),bookType: "print")
            booksMst2 = new BooksMst(id:booksMst.id,title: params.title, description: params.title,
                    coverImage: params.coverImage, buylink1: params.bookLink, status: 'published', asin: params.asin, datePublished: new Date(),
                    siteId: getSiteId(request),bookType: "print")
            booksMst1.save(failOnError: true, flush: true)
            booksMst2.save(failOnError: true, flush: true)
        } else {
            print("book exists "+booksMst.title)
        }

        BooksTagDtl booksTagDtl = BooksTagDtl.findByBookIdAndLevelAndSyllabusAndGradeAndSubject(booksMst.id,params.level,params.syllabus,params.grade,params.subject)
        if(booksTagDtl==null) {
            booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: params.level, syllabus: params.syllabus, grade: params.grade, subject: params.subject)
            booksTagDtl.save(failOnError: true, flush: true)
            booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: params.level, syllabus: params.syllabus, grade: params.grade, subject: params.subject)
            booksTagDtl.wsshop.save(failOnError: true, flush: true)
        }

        dataProviderService.getBookTagsForBook(booksMst.id)
        dataProviderService.getBooksTagList(params.level)
        dataNotificationService.tagUpdated(booksTagDtl.level,booksTagDtl.syllabus)
        dataProviderService.refreshCacheForPublishUnpublish("" + booksMst.id,getSiteId(request))

        def json = [status:"OK"]
        render json as JSON
    }


    @Transactional
    def getNewBooksList(){
       def json
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        boolean excludeCheck = false
        if (siteMst != null && (siteMst.id.intValue() == 1 || siteMst.prepjoySite == "true")){
           String url = request.getRequestURL()
            if(url.indexOf("http://localhost")>-1||url.indexOf("wonderslate.com")>-1||url.indexOf("prepjoy.com")>-1) {
                excludeCheck = true
            }
        }else{
            String url = request.getServerName()
             if((siteMst.siteDomainName==null&&url.indexOf("www.wonderslate.com")>-1)||(""+siteMst.siteDomainName).indexOf(url)>-1||siteId.intValue()==46&&(url.equals("www.mtgcoach.online")||url.equals("mtgcoach.online")||url.equals("elearning.mtg.in"))) {
                excludeCheck = true
            }
            else if(url.startsWith("localhost")||url.indexOf("qa.wonderslate.com")>-1||url.indexOf("publish.wonderslate.com")>-1) excludeCheck = true
            else {
                siteMst = SiteMst.findBySiteDomainName(url)
                if(siteMst!=null){
                    siteId = new Integer(siteMst.id.intValue())
                    excludeCheck=true
                }
            }
         }
        if(siteId.intValue()==90) siteId=new Integer(1)

        if(excludeCheck) {
            int pageNo = 0
            if (params.pageNo != null && !"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
            if(params.fromApp==null&&!"".equals(params.fromApp)) params.put("fromApp","true")
            HashMap booksAndPublishers = wsshopService.getBooksList(params, siteId, pageNo)
            json = [
                    'books'             : booksAndPublishers.get("books"),
                    'publishers'        : booksAndPublishers.get("publishers"),
                    'bookTags'          : booksAndPublishers.get("bookTags"),
                    'level'             : booksAndPublishers.get("level"),
                    'syllabus'          : booksAndPublishers.get("syllabus"),
                    'grade'             : booksAndPublishers.get("grade"),
                    'subject'           : booksAndPublishers.get("subject"),
                    'publisherId'       : booksAndPublishers.get("publisherId"),
                    'newlyReleasedEbook': booksAndPublishers.get("newlyReleasedEbook"),
                    'ebookOfTheDay'     : booksAndPublishers.get("ebookOfTheDay"),
                    'trendingNowEbook'  : booksAndPublishers.get("trendingNowEbook"),
                    'featuredPublishers': booksAndPublishers.get("featuredPublishers"),
                    'status'            : booksAndPublishers.get("books") ? "OK" : "Nothing present",
                    'serverName'        :request.getServerName()
            ]
        }else{
            json = [
                    'books'             : null,
                    'serverName'        :request.getServerName()
                    ]
        }
        render json as JSON
    }



    def videoExplanation(){
        if("edit".equals(params.mode)){
            if(params.id ==null || ''.equals(params.id))  redirect(action:'index')
            else{
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.id))
                List videoExplanation = VideoExplanation.findAllByResourceId(new Integer(params.id),[sort:"id" ])
                [objectives: videoExplanation,resourceDtl: resourceDtl]
            }
        }
    }

    //automated video add page
    @Secured(['ROLE_USER'])
    def addVideoHTML(){
        boolean addedQuiz=false
        def  resourceDtlId, objectiveMstId
        ResourceDtl resourceDtlInstance
        VideoExplanation videoexplanation
        String fileData
        if("create".equals(params.mode)) {
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.dateCreated = new Date()
            resourceDtlInstance.resLink="empty"
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.chapterId = new Integer(params.chapterId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true,flush: true)
            Integer resId=resourceDtlInstance.getId()
            videoexplanation = new VideoExplanation()
            videoexplanation.image = params.notes
            videoexplanation.slideName = params.slideName
            videoexplanation.point1 = params.notes1
            videoexplanation.explanation1 = params.description
            videoexplanation.point2 = params.notes2
            videoexplanation.explanation2 = params.description2
            videoexplanation.point3 = params.notes3
            videoexplanation.explanation3 = params.description3
            videoexplanation.point4 = params.notes4
            videoexplanation.explanation4 = params.description4
            videoexplanation.point5 = params.notes5
            videoexplanation.explanation5 = params.description5
            videoexplanation.resourceId = resId
            videoexplanation.save(failOnError: true,flush: true)
            resourceDtlId = resourceDtlInstance.id
            objectiveMstId = videoexplanation.id
            addedQuiz=true
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
        } else if ("edit".equals(params.mode)) {
            if(params.objectiveMstId!=null) {
                if(params.resourceName!=null && !"".equals(params.resourceName) && !" ".equals(params.resourceName)){
                    ResourceDtl resourceDtl1 = ResourceDtl.findById(new Long(params.resourceDtlId))
                    resourceDtl1.resourceName = params.resourceName
                    resourceDtl1.save(failOnError: true,flush: true)
                }
                VideoExplanation videoexplanation1 = VideoExplanation.findById(new Integer(params.objectiveMstId))
                videoexplanation1.image = params.notes
                videoexplanation1.slideName = params.slideName
                videoexplanation1.point1 = params.notes1
                videoexplanation1.explanation1 = params.description
                videoexplanation1.point2 = params.notes2
                videoexplanation1.explanation2 = params.description2
                videoexplanation1.point3 = params.notes3
                videoexplanation1.explanation3 = params.description3
                videoexplanation1.point4 = params.notes4
                videoexplanation1.explanation4 = params.description4
                videoexplanation1.point5 = params.notes5
                videoexplanation1.explanation5 = params.description5
                videoexplanation1.save(failOnError: true, flush: true)
                objectiveMstId = params.objectiveMstId
                resourceDtlId = params.resourceDtlId
                addedQuiz = true

            }
        }
        else if ("add".equals(params.mode)) {
            videoexplanation = new VideoExplanation()
            videoexplanation.image = params.notes
            videoexplanation.slideName = params.slideName
            videoexplanation.point1 = params.notes1
            videoexplanation.explanation1 = params.description
            videoexplanation.point2 = params.notes2
            videoexplanation.explanation2 = params.description2
            videoexplanation.point3 = params.notes3
            videoexplanation.explanation3 = params.description3
            videoexplanation.point4 = params.notes4
            videoexplanation.explanation4 = params.description4
            videoexplanation.point5 = params.notes5
            videoexplanation.explanation5 = params.description5
            videoexplanation.resourceId = new Integer(params.resourceDtlId)
            videoexplanation.save(failOnError: true, flush: true)
            resourceDtlId = params.resourceDtlId
            objectiveMstId = videoexplanation.id
            addedQuiz=true
        }

        if(addedQuiz) {
            ChaptersMst chaptersMst = ChaptersMst.findById(new Integer(params.chapterId))
            if ("true".equals(params.finished)) {
                if("sage".equals(session["entryController"]))  {
                    redirect(controller: 'wonderpublish', action: 'bookCreate', params: [chapterId: params.chapterId, bookId: params.bookId, ChaptersMst: chaptersMst])
                }
                else{
                    redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: [chapterId: params.chapterId, bookId: params.bookId, ChaptersMst: chaptersMst])
                }
            }
            render "resourceDtlId=" + resourceDtlId + "&objectiveMstId=" + objectiveMstId
        }
    }


    @Secured(['ROLE_USER'])
    def deleteQuestion() {
        VideoExplanation videoExplanation2 = VideoExplanation.findById(new Long(params.objectiveId))
        videoExplanation2.delete(flush: true, failOnError: true)
        render "success"
    }



    def openPDF(){

    }
    @Secured(['ROLE_WS_EDITOR'])
    def wseditor(){}
    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def getUserAndBooksDetails(){
        def username=params.username
        User user = User.findByUsername(username)
        if(user!=null) {
            //adding the site id condition here. This is a quick fix. Correct fix should get the site information and then decide.
            def sql = "select bm.id,bm.title,bp.expiry_date,'Added/Purchased' book_type" +
                    " from books_mst bm, wsuser.books_permission bp" +
                    " where bp.book_id=bm.id and bp.username='" + username + "' and bm.site_id!=12 and (bm.show_in_library is null or bm.show_in_library='Yes')" +
                    " union " +
                    " SELECT book_id,bm.title,null expiry_date,'Batch' book_type" +
                    " FROM wsuser.books_batch_dtl bbd,wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud, books_mst bm" +
                    " where bm.id=bbd.book_id and cbd.id=bbd.batch_id" +
                    " and cbd.status='active' and bud.username='" + username + "' and bm.site_id!=12 and bud.batch_id=cbd.id  and (bm.show_in_library is null or bm.show_in_library='Yes')" +
                    " union " +
                    //include below books along with user books
                    " select bm.id," +
                    " bm.title,null expiry_date,'Support book' book_type" +
                    " from books_mst bm, key_value_mst kvm" +
                    " where bm.site_id!=12 and bm.id=kvm.key_value and kvm.key_name='booksToInclude'   and (bm.show_in_library is null or bm.show_in_library='Yes') " +
                    "order by title"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)


            List books = results.collect { book ->

                return [id: book[0], title: book[1],expiryDate:book[2],bookType: book[3]]
            }


            def json = ['status':"OK",'books': books, name: user.name, email: user.email]
            render json as JSON
        }
        else{
            def json = ['status':"User not found"]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def getUserPaidBooksDetails(){
        def username=params.username
        User user = User.findByUsername(username)
        if(user!=null) {
            def sql ="select bm.id,bm.title" +
                    " from books_mst bm, wsuser.books_permission bp" +
                    " where bp.book_id=bm.id and bp.username='" + username + "'  and  (bm.price!=0 or bm.price!=null) and bp.po_type='PURCHASE' "+
                    " order by title";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            List books = results.collect { book ->
                return [title: book[1],id: book[0] ]
            }
            def json = ['status' : books ? "OK" : "false",'books': books,'username':user.username,'name':user.name,'email':user.email]
            render json as JSON
        }
        else{
            def json = ['status':"User not found"]
            render json as JSON
        }
    }

    def getChapterIdandbookIdbyResID() {
        String sql = "select cm.id,cm.book_id,cm.name,rd.filename,rd.resource_name from chapters_mst cm,resource_dtl rd where rd.id=" + params.resId + " and cm.id=rd.chapter_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List ids = results.collect { chapter ->
            return [chapterId: chapter[0], bookId: chapter[1],chapterName:chapter[2],fileName: chapter[3],ResourceName:chapter[4]]
        }
        def json = [
                'ids': ids,
        ]
        render json as JSON
    }



    def downloadEncodedPdf(){
        ResourceDtl documentInstance = ResourceDtl.get(Long.parseLong(params.id))
        if(utilService.canSeeResource(documentInstance,request,session)) {
            String allowDownLoad = documentInstance.videoPlayer
            if (!allowDownLoad.equals("yes")) {
                flash.message = "File cannot be downloaded."
                redirect(action: 'mybooks')
            } else {
                def file = new File(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
                response.setHeader("Content-Disposition", "inline;Filename=\"${documentInstance.filename}\"")
                response.setHeader("Content-Length", "${file.length()}")

                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;

                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }
        }
    }
    

    @Secured(['ROLE_BOOK_CREATOR','ROLE_DIGITAL_MARKETER'])
    def bannerManagement(){
        List bannersMstList  = BannersMst.findAllBySiteIdAndPublisherIdAndInstituteId(getSiteId(request), null,null);
        [bannersMstList:bannersMstList,commonTemplate:"true"]
    }



    def showImage(String fileName, String id,String imgType) {
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            if("webp".equals(imgType)){
              String  picFileName = fileName.substring(0, fileName.indexOf(".")) + '.webp'
                fileName=picFileName
            }
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file
            if(params.fromPub==null) file = new File("upload/banner/"+id+"/"+fileName)
            else file = new File("upload/banner/" + fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }

    def  Integer getImageSite(request){
        Integer siteId = new Integer(21)
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }
        return siteId
    }

    @Transactional
    def getBannerdetails(){
        def json
        List bannerList
        String siteId=params.siteId
        if(redisService.("bannerList_"+params.siteId)==null) dataProviderService.getBanners(siteId)
        bannerList = new JsonSlurper().parseText(redisService.("bannerList_"+params.siteId))
        json = ['status': bannerList? "OK" : "Nothing Present", 'banners': bannerList]
        render json as JSON

    }
    @Transactional
    def getPublisherBannerdetails(){
        def json
        List bannerList
        String siteId=params.siteId
        String publisherId = params.publisherId
        if(redisService.("publisherBannerList_"+params.siteId+"_"+publisherId)==null) dataProviderService.getBannersForPublishers(siteId,publisherId)
        bannerList = new JsonSlurper().parseText(redisService.("publisherBannerList_"+params.siteId+"_"+publisherId))
        json = ['status': bannerList? "OK" : "Nothing Present", 'banners': bannerList]
        render json as JSON

    }


    @Secured(['ROLE_BOOK_CREATOR'])
    def independentContent(){
        def siteId = getSiteId(request)
        SiteMst   siteMst = dataProviderService.getSiteMst(siteId)
        [apiKey: siteMst.googleApiKey]
    }





    def String convertToJsonString(String inputString){
        String jsonString = inputString
        if(jsonString!=null&&jsonString.length()>0) {
            jsonString = jsonString.substring(1, (jsonString.length() - 1))
            jsonString = jsonString.replace('[', '{');
            jsonString = jsonString.replace(']', '}');
            jsonString = "[" + jsonString + "]";
        }

        return jsonString
    }
    def addDoubleQuotes1(inputString){
        return inputString.replace("{","{\"").replace("}","\"}").replace(":","\":\"").replace(", ","\", \"").replace("}\"","}").replace("\"{","{");
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    def deleteIndependentResId(){
        Integer siteId = getSiteId(request)
        ResourceDtl.executeUpdate("delete ResourceDtl where id=" + params.id)
        redisService.("getindependentResourcDetails"+params.page+"_"+siteId) = null
        redisService.("getindependentResourcDetails"+params.page+"_"+siteId+"_recordCount") = null
        def json = ['status' :"OK"]
        render json as JSON
    }


    
    @Transactional
    def updateCreatedBy(){
        def siteId = getSiteId(request)+"_";
        BooksMst booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
        BooksMst.executeUpdate("update BooksMst set createdBy ='" + siteId+params.transferto + "' where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set createdBy ='" +siteId+ params.transferto + "' where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set createdBy ='" +siteId+params.transferto + "' where id=" + params.bookId)
        dataProviderService.getWSPublishedMyBooks();
        dataProviderService.getWSUnpublishedMyBooks();
        redisService.("getWSUnpublishedMyBooks_"+siteId+params.transferto)= null
        redisService.("getWSPublishedMyBooks_"+siteId+params.transferto)= null
        def json = ["status":"success"]
        render json as JSON
    }

    def addEpubChapters() {
        def chaptersMst
        List chapterDetails = new ArrayList()
        List chapterId = new ArrayList()
        String filename = params.fileName
        filename = filename.replaceAll("\\s+", "")
        BooksMst booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
        BooksMst.executeUpdate("update BooksMst set singleEpub ='true' where id=" + params.bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set singleEpub ='true' where id=" + params.bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set singleEpub ='true' where id=" + params.bookId)
        if(params.chapterDetails != null) chapterDetails =  new JsonSlurper().parseText(params.chapterDetails)
        for(int i=0;i<chapterDetails.size();i++) {
            chaptersMst  = addChapterDetails(chapterDetails[i],params)
            if(chapterDetails[i].subitems){
                for(int j=0;j<chapterDetails[i].subitems.size();j++){
                    chaptersMst  = addChapterDetails(chapterDetails[i].subitems[j],params)
                    chapterId.add(chaptersMst.id)
                }
            }
            chapterId.add(chaptersMst.id)
        }
        dataProviderService.getChaptersList(new Long(params.bookId))
        dataProviderService.refreshCacheForPublishUnpublish("" + params.bookId,getSiteId(request))
        def json =  [
                'chapterId': chapterId
        ]
        render json as JSON
    }

    def addChapterDetails(chapterInfo,params){
        ChaptersMst chaptersMst
        String filename = params.fileName
        println(params.fileName)
        filename = filename.replaceAll("\\s+", "")
        chaptersMst = new ChaptersMst(name: chapterInfo.label, bookId: new Long(params.bookId))
        chaptersMst.save(failOnError: true)

        def resourceDtlInstance = new ResourceDtl()

        resourceDtlInstance.filename = filename
        resourceDtlInstance.siteId = siteId
        resourceDtlInstance.createdBy = springSecurityService.currentUser.username
        resourceDtlInstance.resType = "Notes"
        resourceDtlInstance.chapterId = chaptersMst.id
        resourceDtlInstance.resourceName = chapterInfo.label
        resourceDtlInstance.quizMode = params.quizMode
        resourceDtlInstance.noOfPages = 0
        resourceDtlInstance.resSubType = params.subType
        resourceDtlInstance.resLink = "upload/epub/"+params.bookId+"/"+filename
        resourceDtlInstance.ebupChapterLink =chapterInfo.href
        resourceDtlInstance.save(flush: true, failOnError: true)
        return chaptersMst
    }

    @Secured(['ROLE_BOOK_CREATOR','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def uploadEpubFile(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file")
        if(params.bookId != null){
            File uploadDir = new File("upload/epub/"+params.bookId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            String filename=file.originalFilename
            filename=filename.replaceAll("\\s+","")
            file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))
            def json = ['fileName':file.filename]
            render json as JSON
        }else {
            def json = ['error':"bookId param is not present"]
            render json as JSON
        }
    }



    @Transactional
    def updateChaptersSortId(){
        BooksMst booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
        String[] chapterId = params.chaptersSortId.split(",");
        for(int i=0;i<chapterId.length;i++){
            ChaptersMst.executeUpdate("update ChaptersMst set sortOrder =" +i+ " where id=" + chapterId[i])
        }
        dataProviderService.getChaptersList(booksMst.id);
        metainfoService.getAllChaptersMetaInfo(booksMst.id)
        def json = ['status':"success"]
        render json as JSON

    }

    def pdfReader(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        boolean showPdf = true
        if("false".equals(booksMst.showPdf)){
            showPdf = false
        }
       [showPdf:showPdf]
    }

    @Secured(['ROLE_BOOK_CREATOR','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def relatedVideosAdmin(){
        List relatedVideos = RelatedVideosNew.findAllByChapterId(new Integer(params?.chapterId))

        if(relatedVideos!=null) {
            ['searchResultList': relatedVideos, 'chapterId': params?.chapterId,'relatedVideoStatus': "present"]
        }
        else{
            ['relatedVideoStatus': "notPresent", 'chapterId': params?.chapterId]
        }
    }

    def getSingleBookDetail(){
        String sql = "select bk.id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,p.name,p.id publisherId," +
                " null,null,bk.test_type_book,bk.book_type,date_published,bk.test_start_date,bk.test_end_date" +
                " from books_mst bk, wsshop.books_tag_dtl btd,publishers p "+
                " where bk.id=btd.book_id  and bk.status in ('free','published') and bk.id="+params.bookId+
                " and p.id= bk.publisher_id and  bk.sell_chapter_wise is null" +
                " group by bk.id  order by book_type,date_published desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def totalMarks,noOfQuestions,language1,language2,resourceId
        List books = results.collect { book ->
            totalMarks=""
            noOfQuestions=""
            language1=""
            language2=""
            resourceId=""
            if("test".equals(book[12])){
                if(redisService.("test_series_"+book[0])==null) getTestSeriesDtl(""+book[0])
                List testDetails = new JsonSlurper().parseText(redisService.("test_series_"+book[0]))
                if(testDetails.size()>0) {
                    totalMarks = testDetails[0].totalMarks
                    noOfQuestions = testDetails[0].noOfQuestions
                    language1 = testDetails[0].language1
                    language2 = testDetails[0].language2
                    resourceId = testDetails[0].resourceID
                }
            }
            return [id: book[0], title: (book[1]).replace(':',' ').replace(',',' '),coverImage:book[2],subjectyear:book[3],
                    'listPrice':book[4],'rating':book[5],'offerPrice':book[6], 'publisher':book[7],'publisherId':book[8],
                    'chapterDisplayPrice':book[9],'chapterSellPrice':book[10],'chapterType':book[11], 'bookType':book[12],
                    'testStartDate':book[14]!=null?(""+book[14]).replace(':','~'):"",'testEndDate':book[15]!=null?(""+book[15]).replace(':','~'):""
                    ,'totalMarks':totalMarks,'noOfQuestions':noOfQuestions,'language1':language1,'language2':language2,'resourceId':resourceId]
        }

        def json  = [books:books]
        render json as JSON
    }
    @Transactional
    def indexHome(){
        String userName = ""
        User user = null
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                }else{
                }
            }
            session['appType']=params.appType
        }
        if(user == null){
        }
        String level=null,syllabus=null,grade=null,gradeid=null,title,displayGrade="",urlName=null
        def resLink=null,filename=null,resourceName=null
        Publishers publishers = null
        if(params.urlName==null) {
            redirect(controller: 'books', action: 'index')
        }else{
            publishers = dataProviderService.getPublisherByUrlname(params.urlName)
            title = publishers.name +" Current Affairs, PDF, Tests"
            urlName = params.urlName
            displayGrade = title
            if(springSecurityService.currentUser != null) {
                user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                userName = user.username
            }
            [title:title,urlName:urlName,publisher: publishers,resourceName:resourceName,"user":userName, commonTemplate:"true"]
        }


    }

    @Transactional
    def getPublisherBooksList(){
        def json

        Integer siteId = getSiteId(request)
        String siteIdList=siteId.toString()
        String urlName = params.urlName
        Publishers publishers = Publishers.findByUrlname(urlName)


        if(redisService.("publishersBooksList_"+publishers.id)==null) {
            dataProviderService.getPublisherBooksList(siteIdList,publishers.id)
        }


        def books = redisService.("publishersBooksList_" + publishers.id)

        json = [
                'books':books,
                'publisherName':publishers.name,
                'status' : books ? "OK" : "Nothing present"

        ]


        render json as JSON
    }

    @Transactional
    def lockEditUpdate() {
        def json
        try {
            // Check if user has required role and site ID
            def siteId = getSiteId(request)
            def userDetails = session["userdetails"]

            if (siteId != 71) {
                json = ['status': 'error', 'message': 'This feature is only available for site ID 71']
                render json as JSON
                return
            }

            if (!userDetails?.authorities?.any { it.authority == "ROLE_PDF_EXTRACTOR" }) {
                json = ['status': 'error', 'message': 'You do not have permission to perform this action']
                render json as JSON
                return
            }

            if (!params.bookId) {
                json = ['status': 'error', 'message': 'Book ID is required']
                render json as JSON
                return
            }

            // Get the book to verify it exists
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            if (!booksMst) {
                json = ['status': 'error', 'message': 'Book not found']
                render json as JSON
                return
            }

            // Update lockEdit field in all three datasources
            String lockEditValue = "true".equals(params.lockEdit) ? "true" : "false"

            BooksMst.executeUpdate("update BooksMst set lockEdit='" + lockEditValue + "' where id=" + params.bookId)
            BooksMst.wsuser.executeUpdate("update BooksMst set lockEdit='" + lockEditValue + "' where id=" + params.bookId)
            BooksMst.wsshop.executeUpdate("update BooksMst set lockEdit='" + lockEditValue + "' where id=" + params.bookId)

            // Refresh cache
            dataProviderService.getBooksMst(new Long(params.bookId))

            json = ['status': 'success', 'message': 'Lock edit status updated successfully', 'lockEdit': lockEditValue]

        } catch (Exception e) {
            log.error("Error updating lock edit status: " + e.getMessage(), e)
            json = ['status': 'error', 'message': 'An error occurred while updating lock edit status']
        }

        render json as JSON
    }


}

