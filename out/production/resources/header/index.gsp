<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<!--<div>-->
<div>
    <div class="container-fluid my-5 px-5">
        <div  class='' id="bookdtl" style="min-height: calc(100vh - 156px);">
%{--            <div class="col-md-2 wpsidebar">--}%
%{--                <div class="hidden-xs"></div>--}%
%{--            </div>--}%
            <div class="container">
            <h3 class="mb-3">Header Management</h3>
            <div class='col-md-12 main mx-auto p-5'>
                <div id="content-books">

                    <table class="table table-bordered table-hover" id="pubTable">
                        <thead class="bg-primary text-white"> <tr> <th width="70%">&nbsp;Title</th>

                            <th>Status</th></tr></thead>
                        <tbody>
                        <g:each in="${books}" var="book" status="i">
                            <tr>
                                <td><a href="/book-create?bookId=${book.id}">${book.title}</a></td>
                                <td><a href='/header/editIndex?siteName=${session['entryController']}&bookId=${book.id}&title=${book.title}'><%= "hasHeader".equals(book.header)?"Edit Header":"Add Header" %></a></td>
                            </tr>
                        </g:each>
                        </tbody>
                    </table></div>

                </div>
            </div>
        </div>
    </div>
    <div class="push"></div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<!--http://tablesorter.com/docs/#Download-->
<asset:javascript src="jquery.tablesorter.min.js"/>
<script>
    $(document).ready(function() {
        $("#pubTable").tablesorter();
    });
</script>
</body>
</html>