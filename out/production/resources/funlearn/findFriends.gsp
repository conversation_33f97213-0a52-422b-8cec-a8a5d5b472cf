<g:render template="navheader"></g:render>
<!--<div>-->
<div class="wrapper">
    <div class="container-fluid">
        <div class='row maincontent'>
            <div class='col-md-2 col-md-offset-1 sidebar'>
            </div>
            <div class='col-md-7 col-md-offset-1 main'>
                <div class="row">
                    <div class="col-md-12">
                        <h3 class="text-center">Find Friends</h3>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-offset-2 col-md-8">
                        <form role="form" action="/funlearn/findFriends" method="post" name="findFriends">
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="friend" id="friend" placeholder="Name or W.I.N (Wonderslate Identification Number)">
                                    <span class="input-group-btn">
                                        <a class="btn btn-primary" type="button" href="javascript:submitFriend()" name="find" id="find">Find</a>
                                    </span>
                                </div>
                            </div>
                            <div class="alert alert-warning col-sm-12 text-left" style="display: none;">
                                ** Please enter Name or W.I.N of your friend.
                            </div>
                            <input type="hidden" name="mode" value="search">
                        </form>
                    </div>
                </div>
              <div id="friendsList">
                </div>
            </div>
            <div class='col-md-2 col-md-offset-1 sidebar'>
               <h3>Friends</h3>
                <div id="currentFriends"></div>
            </div>
        </div>
    </div>
    <div class="push"></div>
</div>
<g:form name="join" action="messages">
    <g:hiddenField name="friendId" value=""/>
    <g:hiddenField name="friendNm" value=""/>
    <g:hiddenField name="usertype" value=""/>
    <g:hiddenField name="profilepic" value=""/>
</g:form>
    <g:render template="footer"></g:render>
<!--</div>-->
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="generic.js"/>
<script>
    function submitFriend() {
        var flds = new Array ('friend');

        if (genericValidate(flds)) {

            var friend = document.findFriends.friend.value;
            <g:remoteFunction controller="funlearn" action="findFriendsJSON"  onSuccess='showFriends(data);'
                params="'friend='+friend" />
            }
    }

    function showFriends(data){
    var htmlStr="";
        var friends = data.results;
        var imageStr;
        var options;
        if(friends.length >0) {
            for (var i = 0; i < friends.length; ++i) {
                options = "";

                if ("null" != ("" + friends[i].status)) {
                    if ("1" == ("" + friends[i].status)) options = "<span class='green'><b><span class='smallerText'>Connected</span></b></span>";
                    else {
                        if ("true" == friends[i].friend1currentuser) { // if the friend request was already sent to this person
                            if ("0" == ("" + friends[i].status) || "2" == ("" + friends[i].status)) options = "<span class='green smallerText'><b>Friend request sent</b></span>";

                        }
                        else { //if friend request recieved from this person
                            if ("0" == ("" + friends[i].status)) {
                                options = "<a class='btn btn-success' type='button' href='javascript:friendAccept(1," + friends[i].friendId + ")'>Accept</a>&nbsp;&nbsp;<a class='btn btn-default' type='button' href='javascript:friendAccept(2," + friends[i].friendId + ")'>Ignore</a>";

                            }
                            if ("2" == ("" + friends[i].status)) {
                                options = "<span class='green smallerText'><b>Friend request ignored</b></span>";
                            }
                        }
                    }
                }
                else {
                    options = "<a class='btn btn-success' type='button' href='javascript:friendRequest(1," + friends[i].friendId + ")'>Add Friend</a>"
                }

                if ("null" == "" + friends[i].profilepic) imageStr = "<a href='#'> <i class='fa fa-user fa-3x'></i></a>";
                else imageStr = "<img src='/funlearn/showProfileImage?id=" + friends[i].friendId + "&fileName=" + friends[i].profilepic + "&type=user&imgType=icon' width='50'>";
                htmlStr += "<div class='row'>" +
                        "<div class='col-md-offset-2 col-md-1 vcenter'>" + imageStr + "</div>" +
                        "<div class='col-md-3 vcenter smalltext'><b><span class='smallerText'>" + friends[i].name + "</span></b><br></div>" +
                        "<div class='col-md-4 vcenter smalltext' id='friendoption" + friends[i].friendId + "'>" + options + "</div>" +
                        "<hr class='myhrline'>" +
                        "</div>";

            }
        }else{
            htmlStr = "<div class='row'><div class='col-md-offset-2 col-md-10'><b><span class='smallerText'>No users found with the given search inputs. Kindly modify and try again</span></b></div></div>";
        }
        document.getElementById('friendsList').innerHTML= htmlStr;

    }

    function friendRequest(accept,friendid){
        <g:remoteFunction controller="friends" action="addFriend"  onSuccess='friendAdded(data);'
                params="'friendid='+friendid+'&status='+accept" />
        document.getElementById("friendoption"+friendid).innerHTML ="<span class='green smallerText'><b>Friend request sent</b></span>";
    }

    function friendAdded(data){
    }

    function getCurrentFriends(){
        <g:remoteFunction controller="friends" action="currentFriends"  onSuccess='displayCurrentFriends(data);'
                />
    }

    function displayCurrentFriends(data){
        var friends = data.results;
        var imageStr;
        var htmlStr="";
        if(friends.length ==0)  htmlStr="<div class='row'><div class=' col-md-12 '><b><span class='smallerText'>Learning with friends is fun. Start adding friends and build your group</span></b></div></div>";
		
        for(var i = 0; i < friends.length; ++i) {
            if("null"==""+friends[i].profilepic) imageStr="<a href='#'> <i class='fa fa-user fa-2x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id="+friends[i].friendId+"&fileName="+friends[i].profilepic+"&type=user&imgType=icon' width='40'>";
			
            htmlStr+="<div class='row'>"+
                    "<div class='col-md-2 vcenter'>"+imageStr+"</div>"+
                    "<div class='col-md-8 vcenter'><b><span class='smallerText'><a href='/funlearn/profile?id="+friends[i].friendId+"'> "+friends[i].name+"</a></span></b></div>"+
					//"<div class='col-md-1 vcenter'><a href='javascript:chat("+friends[i].friendId+",\""+friends[i].name+"\",\""+friends[i].profilepic+"\",\"user\")' name='chat' id='chat'><i class='fa fa-envelope fa-x'></i></a></div>"+
                    "<hr class='myhrline'>"+
                    "</div>";
        }
		
        document.getElementById('currentFriends').innerHTML= htmlStr;
    }

    function getPendingRequests() {
        <g:remoteFunction controller="friends" action="pendingRequests"  onSuccess='displayPendingRequests(data);'/>
    }
	
    function displayPendingRequests(data){
        var htmlStr="";
        var friends = data.results;
        var imageStr;
        var options;
        if(friends.length >0)  htmlStr="<div class='row'><div class='col-md-offset-2 col-md-6 vcenter'><h5>Pending Requests</h5></div></div>";
		
        for (var i = 0; i < friends.length; ++i) {
            options = "<a class='btn btn-success' type='button' href='javascript:friendAcceptMain(1," + friends[i].friendId + ")'>Accept</a>&nbsp;&nbsp;<a class='btn btn-default' type='button' href='javascript:friendAcceptMain(2," + friends[i].friendId + ")'>Ignore</a>";
            if ("null" == "" + friends[i].profilepic) imageStr = "<a href='#'> <i class='fa fa-user fa-3x'></i></a>";
            else imageStr = "<img src='/funlearn/showProfileImage?id=" + friends[i].friendId + "&fileName=" + friends[i].profilepic + "&type=user&imgType=icon' width='50'>";
            htmlStr += "<div class='row'>" +
                    "<div class='col-md-offset-2 col-md-1 vcenter'>" + imageStr + "</div>" +
                    "<div class='col-md-3 vcenter smalltext'><b><span class='smallerText'>" + friends[i].name + "</span></b><br><span class='smallerText'></span></div>" +
                    "<div class='col-md-4 vcenter smalltext' id='friendoptionmain" + friends[i].friendId + "'>" + options + "</div>" +
                    "<hr class='myhrline'>" +
                    "</div>";
        }

        document.getElementById('friendsList').innerHTML= htmlStr;
    }
	
    getCurrentFriends();
    getPendingRequests();

    window.onload = function() {
        document.getElementById('friend').onkeypress = function(e) {
            doClick('find', e);
        };

        function doClick(buttonName,e) {
            //the purpose of this function is to allow the enter key to
            //point to the correct button to click.
            var ev = e || window.event;
            var key = ev.keyCode;

            if (key == 13) {
                //Get the button the user wants to have clicked
                var btn = document.getElementById(buttonName);
                if (btn != null) {
                    //If we find the button click it
                    btn.click();
                    ev.preventDefault();
                }
            }
        }
    };
	
    function chat(friendId, friendNm,profilepic,usertype){
		document.join.action="/messaging/messages";
		document.join.friendNm.value=friendNm;
		document.join.friendId.value=friendId;
        document.join.profilepic.value=profilepic;
        document.join.usertype.value=usertype;
		document.join.submit();
    }	
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>
</body>
</html>