<link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

<%if("1".equals(""+session["siteId"])||"true".equals(session['prepjoySite'])){%>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<%}%>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.4/lottie.min.js"</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<style>

.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}

.ordText{
    border-bottom: 1px solid #999;
    padding-bottom: 10px;
}
#addbk,#freeBk{
    position: relative;
    margin: 0 auto;
    top: 0;
}
#addBookToLibContent,#purchasePepjoyBook{
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='450' height='390' preserveAspectRatio='none' viewBox='0 0 450 390'%3e%3cg clip-path='url(%26quot%3b%23SvgjsClipPath1963%26quot%3b)' fill='none'%3e%3crect width='450' height='390' x='0' y='0' fill='rgba(15%2c 8%2c 57%2c 0.74)'%3e%3c/rect%3e%3ccircle r='27.995' cx='90.82' cy='22.23' fill='url(%23SvgjsLinearGradient1964)'%3e%3c/circle%3e%3ccircle r='18.41' cx='345.73' cy='8.55' fill='url(%23SvgjsLinearGradient1965)'%3e%3c/circle%3e%3ccircle r='31.615' cx='117.63' cy='340.56' fill='url(%23SvgjsLinearGradient1966)'%3e%3c/circle%3e%3ccircle r='14.57' cx='97.27' cy='191.19' fill='%2343468b'%3e%3c/circle%3e%3c/g%3e%3cdefs%3e%3cclipPath id='SvgjsClipPath1963'%3e%3crect width='450' height='390' x='0' y='0'%3e%3c/rect%3e%3c/clipPath%3e%3clinearGradient x1='34.82999999999999' y1='22.229999999999997' x2='146.81' y2='22.229999999999997' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1964'%3e%3cstop stop-color='%23ab3c51' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%234f4484' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient x1='308.91' y1='8.55' x2='382.55' y2='8.55' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1965'%3e%3cstop stop-color='%2332325d' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%23424488' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient x1='54.4' y1='340.56' x2='180.85999999999999' y2='340.56' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1966'%3e%3cstop stop-color='%23ab3c51' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%234f4484' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e");
    border-radius: 10px;
}
.modal-modifier .modal-content-modifier{
    border-radius: 13px !important;
}
<%if(session['wileySite'] ==true){%>

@media only screen and (min-width: 1024px) {
    .ebook_detail .image_wrapper .book_image .bookShadow img,
    .ebook_detail .image_wrapper .book_image .bookShadow {
        height: 350px;
    }

    .ebook_detail .image_wrapper .book_image {
        height: 350px;
        width: 280px;
    }
}
<%}%>
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
    .ebook_detail .details-book-about-cart .wrp-details-about-cart{
        display: flex;
    }
    .ebook_detail .details-book-about-cart .wrp-details-about-cart .mrp_price{
        width: 50%;
    }
}
.ebook_detail .image_wrapper .book_image{
    width: 300px !important;
    height: 420px !important;
}
.ebook_detail .image_wrapper .book_image .bookShadow {
    height: 420px !important;
}
.ebook_detail .image_wrapper .book_image .bookShadow img{
    height: 420px !important;
}
.bookTitleH1 {
    font-size: 28px !important;
    color: black !important;
}
</style>
<section class="page-main-wrapper mdl-js pb-5 pt-0 ebook_detail">
    <div class="custom_container d-flex flex-wrap py-4 py-md-5 mt-3 mt-md-0">

        <div class="col-sm-12 d-flex flex-wrap px-0">

            <div class="col-12 page-row-filter px-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','','');">${booksTagDtl.level}</a></li>
                        <li class="breadcrumb-item"><a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','');">${booksTagDtl.syllabus}</a></li>
                        <li class="breadcrumb-item"><a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','${booksTagDtl.grade}');">${booksTagDtl.grade}</a></li>
                    </ol>
                </nav>
            </div>
           <%String bookCoverImage = ""%>
            <div class="image_wrapper col-12 col-sm-3 mb-4 text-center px-0">
                <div class="book_image">
                    <div class="bookShadow">
                        <div class="image-wrapper">
                            <% if(booksMst.coverImage==null){%>
                            <div class="uncoverdetail">
                                <p>${booksMst.title}</p>
                            </div>
                            <%  }else{
                                if(booksMst.coverImage.startsWith("http")){
                              bookCoverImage = booksMst.coverImage
                            %>
                            <img src='${booksMst.coverImage}' class="img-responsive" alt="${booksMst.title}"/>
                            <%}else{
                            bookCoverImage = "/funlearn/showProfileImage?id="+booksMst.id+"&fileName="+booksMst.coverImage+"&type=books&imgType=webp"

                            %>
                            <img src='/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.coverImage}&type=books&imgType=webp' class="img-responsive" alt="${booksMst.title}"/>
                            <%}
                            }%>
                        </div>

                        <%if(!("3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "1".equals(""+session["siteId"]) || "66".equals(""+session["siteId"]) )){%>
                        <div class="book-tag">
                            <%if("print".equals(booksMst.bookType)){%>
                            <span>Print Book</span>
                            <% }else if("ebook".equals(booksMst.bookType)){%>
                            <span>eBook</span>
                            <% }else if("testseries".equals(booksMst.bookType)){%>
                            <span>Test Series</span>
                            <% }else if("onlinecourse".equals(booksMst.bookType)){%>
                            <span>Online Course</span>
                            <% }else if("liveclasses".equals(booksMst.bookType)){%>
                            <span>Live Classes</span>
                            <% }else if("mcqsebook".equals(booksMst.bookType)){%>
                            <span>MCQs eBook</span>
                            <% }else if("previouspapers".equals(booksMst.bookType)){%>
                            <span>Previous Papers</span>
                            <% }else{%>
                            <span>eBook</span>
                            <%}%>
                        </div>
                        <%}%>
                    </div>
                </div>

                <div class="featureIcons">
                    <ul>
                        <li>
                            <img src="/assets/resource/ebook.png" alt="ebook" />
                            <p>eBook</p>
                        </li>
                        <li>
                            <img src="/assets/resource/online-test.png" alt="online-test" />
                            <p style="line-height: 1;margin-top: 8px">MCQs</p>
                        </li>
                        <li>
                            <img src="/assets/resource/flashcard.png" alt="flashcard" />
                            <p>Flashcards</p>
                        </li>
                        <li>
                            <img src="/assets/resource/dtl-notes.png" alt="current affairs" />
                            <p style="line-height: 1;margin-top: 8px">Notes & Highlights</p>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="book_info col-12 col-sm-8 pl-md-5 px-0">
                <div class="book_description">
                    <div class="book_briefDetail">
                        <h1 class="mb-3 text-left bookTitleH1">${(publisherNameForTitle!=null?publisherNameForTitle:"")+" "+booksMst.title}</h1>
                        <%if("true".equals(session["commonWhiteLabel"])){%>
                        <p class="mb-3 book-publisher-name text-left">By <a href="/sp/${session["siteName"]}/store?linkSource=bookDetail&publisher=${publisherName.replaceAll(" ","-")}" target="_blank">${publisherName}</a></p>
                        <%} else if("true".equals(session["prepjoySite"])){ %>
                        <p class="mb-3 book-publisher-name text-left">By <a href="/${session["entryController"]}/eBooks?linkSource=bookDetail&publisher=${publisherName.replaceAll(" ","-")}" target="_blank">${publisherName}</a></p>
                        <% }else { %>
                        <p class="mb-3 book-publisher-name text-left">By <a href="/${session["entryController"]}/store?linkSource=bookDetail&publisher=${publisherName.replaceAll(" ","-")}" target="_blank">${publisherName}</a></p>
                        <%}%>
                        <div class="details-book-about-cart mb-3">
                            <div class="wrp-details-about-cart">
                                <span class="first-table-cart">Author(s)</span>
                                <span class="second-table-cart">:</span>
                                <span class="mrp_price">${booksMst.authors}</span>
                            </div>
                            <% if(booksMst.isbn == null || booksMst.isbn == '') { %>
                            <%} else { %>
                            <div class="wrp-details-about-cart">
                                <span class="first-table-cart">ISBN</span>
                                <span class="second-table-cart">:</span>
                                <span class="isbn_no">${booksMst.isbn}</span>
                            </div>
                            <%}%>
                            <% if(booksMst.bookCode == null || booksMst.bookCode == '') { %>
                            <%} else { %>
                            <div class="wrp-details-about-cart">
                                <span class="first-table-cart">Book Code</span>
                                <span class="second-table-cart">:</span>
                                <span class="isbn_no">${booksMst.bookCode}</span>
                            </div>
                            <%}%>

                            <%
                                boolean ebook
                                boolean printbook
                                if (bookPriceDtls.bookType.contains('printbook') || bookPriceDtls.bookType.contains('combo')) {
                                    printbook = true
                                }
                                if(bookPriceDtls.bookType.contains('eBook') || bookPriceDtls.bookType.contains('testSeries')){
                                    ebook=true
                                }
                            %>
                            <%if((ebook && printbook)  || (ebook && !printbook)){%>
                                <div class="freeChapterDiv  mb-2 mt-4">
                                    <%if(!"test".equals(booksMst.bookType)){%>

                                    <%
                                        String text = session['wileySite'] ? 'Look Inside' : 'Try Free Chapter' %>
                                    <%
                                            boolean hideTOC = false

                                            if (session['wileySite'] && showPrice){
                                                hideTOC = true
                                            }
                                    %>

                                    <%if(!hideTOC){%>
                                    <a href="javascript:openBook('paid','${booksMst.title}','${booksMst.id}');" class="freeChapterLink">${text}</a>
                                    <%}%>
                                    <%}%>
                                </div>
                            <%}%>
                        </div>



                        <div class="d-flex align-items-center">
                            <span class="dicprice d-none" style="color: #555">Discount &#x20b9<span class="discp"></span></span>
%{--                            <i class="fa fa-check-circle ml-2 d-none" style="color: green"></i>--}%
                        </div>
                    </div>
                    <div class="book_variants">

                    <%
                        String bookType
                        bookPriceDtls.each{ bookPriceDtl ->
                            if(("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType))&&!sellPrintBook||!showPrice){
                                if(!showPrice){%>
                        <div class="book_variant">
                            <div class="book_variant_card">
                                <div class="book_variant_card-body">
                                    <h6 class="book_variant-name">
                                        <div class="variantType">
                                            This eBook is part of the collection.
                                        </div>
                                       </h6>

                                </div>
                            </div>

                        <a href="/${wileyCollectionBookTitle.replace(' ','-').replace('&','-')}/ebook-details?siteName=${session["siteName"]}&bookId=${wileyCollectionBookId}" id="buyNow" class="addtoCartBtn btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2" data-upgraded=",MaterialButton,MaterialRipple">Show Collection<span class="mdl-button__ripple-container"><span class="mdl-ripple"></span></span></a>

                    </div>
                               <% }

                            }else{
                        if(!"upgrade".equals(bookPriceDtl.bookType)) {
                            switch (bookPriceDtl.bookType){
                                case "eBook" :
                                    bookType = "eBook"
                                    break
                                case "testSeries" :
                                    bookType = "Online test series + eBook"
                                    break
                                case "upgrade" :
                                    bookType = "Test series upgrade"
                                    break
                                case "printbook" :
                                    bookType = "Paperback"
                                    break
                                case "combo" :
                                    bookType = "Paperback + eBook"
                                    break
                                case "ebookupgrade" :
                                    bookType = "Extend validity"
                                    break
                                default:
                                    bookType = "eBook"
                                    break
                            }

                            Float offerPrice = bookPriceDtl.sellPrice
                            Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
                            Double calculatedVal = actualPrice - offerPrice
                            Double percentageVal = calculatedVal * 100 / actualPrice
                            String finalPerVal
                            if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
                            else finalPerVal=String.format("%.0f",percentageVal) +" %"

                            String bookPrice=""
                            String listPrice=""
                            if (offerPrice % 1 != 0) {
                                bookPrice=String.format("%.2f",offerPrice)
                            } else {
                                bookPrice=String.format("%.0f",offerPrice)
                            }
                            if (actualPrice % 1 != 0) {
                                listPrice=String.format("%.2f",actualPrice)
                            } else {
                                listPrice=String.format("%.0f",actualPrice)
                            }
                    %>

                        <div class="book_variant">
                            <div class="book_variant_card">
                                <div class="book_variant_card-body">
                                    <h6 class="book_variant-name">
                                        <div class="variantType">
                                            ${bookType}
                                            <% if("eBook".equals(bookType) ||"Online test series + eBook".equals(bookType) ) {%>
                                                <img src="${assetPath(src: 'wonderslate/ebook.svg')}" alt="${booksMst.title}">
                                            <%} else if("Paperback".equals(bookType)){%>
                                                <img src="${assetPath(src: 'wonderslate/paperback.svg')}" alt="${booksMst.title}">
                                            <%} else  if ("Paperback + eBook".equals(bookType)){%>
                                                <div class="d-flex align-items-center" style="margin-left: 2px;">
                                                    <img src="${assetPath(src: 'wonderslate/ebook.svg')}" alt="${booksMst.title}">
                                                    <span style="margin:-4px 2px 0 2px"><strong>+</strong></span>
                                                    <img src="${assetPath(src: 'wonderslate/paperback.svg')}" alt="${booksMst.title}" style="width: 17px !important;">
                                                </div>
                                            <%}%>
                                        </div>

                                        <span class="d-none badge badge-info shimmer bestPrice" style="font-size: 12px !important;width: 75px !important;">Best Price</span>
                                    </h6>
                                    <div class="book_variant-price">
                                        <div>
                                            <% if(!("0".equals(""+bookPriceDtl.sellPrice)||"0.0".equals(""+bookPriceDtl.sellPrice))) {%>
                                            <span class="offer_price"><span class="rupee-symbol">&#x20b9</span>${bookPrice}</span>
                                            <%}else{%>
                                            <span class="offer_price text-success">Free</span>
                                            <%}%>

                                            <% if(bookPriceDtl.listPrice != bookPriceDtl.sellPrice) {%>
                                            <span class="list_price" style="margin-left: 4px;"><del><span class="rupee-symbol">&#x20b9</span>${listPrice}</del></span>
                                            <%}%>

                                            <% if("365".equals(""+booksMst.validityDays)) {%>
                                            <span class="d-block" style="font-size: 13px;color: #EE3539;">for 1 year</span>
                                            <%}else if(bookExpiry!=null&&!"".equals(bookExpiry)&&(!("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType)))){
                                            %>

                                            <% if(session['wileySite']) {%>
                                            <span class="d-block" style="font-size: 10px;color: #EE3539;"><b>Access valid till ${bookExpiry}</b></span>
                                            <%}else{%>
                                            <span class="d-block" style="font-size: 13px;color: #EE3539;"><b>Valid till ${bookExpiry}</b></span>
                                            <%}%>
                                            <%}%>
                                        </div>
                                        <div class="offerDiv">
                                            <% if(bookPriceDtl.listPrice != bookPriceDtl.sellPrice) {%>
                                                <% if("${finalPerVal}" > 0){%>
                                                    <% if(!("0".equals(""+bookPriceDtl.sellPrice)||"0.0".equals(""+bookPriceDtl.sellPrice))) {%>
                                                        <p class="text-success mb-0 savePrice">Save ${finalPerVal} </p>
                                                    <%}%>
                                                <%}%>
                                            <%}%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <%if(("published").equals(booksMst.status)){%>
                            <% if(!("print".equals(""+booksMst.bookType))) {%>
                            <%if(!canSell){%>
                        <span style="color: red">Available only in India</span>
                            <%}else if(("printbook".equals(bookPriceDtl.bookType)||"combo".equals(bookPriceDtl.bookType))&&booksMst.currentStock!=null&&booksMst.currentStock.intValue()<1) {%>
                        <span style="color: red">Out of stock</span>

                        <% }else if(!("0".equals(""+bookPriceDtl.sellPrice)||"0.0".equals(""+bookPriceDtl.sellPrice))) {%>

                            <a href="javascript:addToCartFromDtl('${booksMst.id}','${bookPriceDtl.bookType}');" id="buyNow" class="addtoCartBtn btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Add to Cart</a>

                            <%} else {%>

                            <sec:ifLoggedIn>
                                <a id="linkOthers" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Open</a>
                            </sec:ifLoggedIn>
                            <sec:ifNotLoggedIn>
                                <a href="javascript:openRegister('free','${booksMst.title}','${booksMst.id}');" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Get this eBook</a>
                            </sec:ifNotLoggedIn>


                            <%}%>
                            <%} else {%>
                            <a href="javascript:openAffliatePage('${booksMst.buylink1}','${booksMst.id}');" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Check Price</a>
                            <%}%>
                            <%} else {%>
                            <a class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" href="javascript:">Book Unavailable</a>
                            <%}%>
                        </div>
                  <%}
                  }
                  }%>

                        <div class="col pl-2 pl-lg-3 pr-0"></div>


                    </div>
                    <%if(subscriptionMst!=null){%>
                    <div class="affiliationPrices shadow-sm subscription">
                        <div class="affiliationPrices_title subscription__sec-1 subscription__sec">
                            <label for="subscriptionId">Subscription options</label>
                            <select id="subscriptionId" name="subscriptionId">
                                <option value="">Select </option>
                                <option value="${subscriptionMst.duration1}-${subscriptionMst.price1.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration1)?"selected":""%>>Rs.${subscriptionMst.price1.intValue()} for ${subscriptionMst.duration1} year</option>
                                <%if(subscriptionMst.price2!=null){%>
                                    <option value="${subscriptionMst.duration2}-${subscriptionMst.price2.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration2)?"selected":""%>>Rs.${subscriptionMst.price2.intValue()} for ${subscriptionMst.duration2} years</option>
                                <%}%>
                                <%if(subscriptionMst.price3!=null){%>
                                    <option value="${subscriptionMst.duration3}-${subscriptionMst.price3.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration3)?"selected":""%>>Rs.${subscriptionMst.price3.intValue()} for ${subscriptionMst.duration3} years</option>
                                <%}%>
                                <%if(subscriptionMst.price4!=null){%>
                                    <option value="${subscriptionMst.duration4}-${subscriptionMst.price4.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration4)?"selected":""%>>Rs.${subscriptionMst.price4.intValue()} for ${subscriptionMst.duration4} years</option>
                                <%}%>
                                <%if(subscriptionMst.price5!=null){%>
                                    <option value="${subscriptionMst.duration5}-${subscriptionMst.price5.intValue()}" <%=params.subsDuration!=null&&params.subsDuration.equals(""+subscriptionMst.duration5)?"selected":""%>>Rs.${subscriptionMst.price5.intValue()} for ${subscriptionMst.duration5} years</option>
                                <%}%>
                            </select>
                        </div>
                        <div class="affiliationPrices_title subscription__sec-2 subscription__sec">
                            <label for="subscriptionMonthBookId">Select month</label>
                            <select  id="subscriptionMonthBookId" name="subscriptionMonthBookId">
                                <option value="">Choose starting month</option>
                                <% subsReleasedVersions.each {subs ->%>
                                    <option value="${subs.id}" <%=params.subsStartingBookId!=null&&params.subsStartingBookId.equals(""+subs.id)?"selected":""%>>${subs.month_published+" "+subs.year_published}</option>
                                <%}%>
                            </select>
                        </div>
                        <div class="subscription__sec-3 subscription__sec">
                            <p class="text-danger mt-1 mb-3 d-none" id="subDurErr">Please select subscription duration.</p>
                            <p class="text-danger mt-1 mb-3 d-none" id="subMonErr">Please select starting month..</p>
                            <sec:ifLoggedIn>
                                <a href="javascript:subscribeNow();" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Add to cart</a>
                            </sec:ifLoggedIn>
                            <sec:ifNotLoggedIn>
                                <a href="javascript:signupModal();" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2">Add to cart</a>
                            </sec:ifNotLoggedIn>
                        </div>
                    </div>
                    <%}%>
                    <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
                    <div class="affiliationPrices shadow-sm">
                        <div class="affiliationPrices_title">
                            <p>Paperback & Hardcover prices</p>
                        </div>
                        <div class="affiliationLinks">
                            <div class="affiliationLinksLoader shimmer">
                                <p>Fetching price from Amazon <span class="aa"></span></p>
                            </div>
                            <div class="affiliationLinksLoader shimmer">
                                <p>Fetching price from Flipkart <span class="aa"></span></p>
                            </div>
                        </div>
                    </div>
                    <%}%>
                    <%if(("3".equals(""+session["siteId"] )|| "38".equals(""+session['siteId'])) && !inLibrary ){%>
                    <p class="d-block mt-3">Do you have access code?<a  href="javascript:openAccessCode('${booksMst.title}','${booksMst.id}')"> Click here</a></p>
                    <%}%>

                </div>
            </div>



            <div class="col-12 px-0">
                <div class="nav-tabs-book-details pt-4">
                    <ul class="nav" role="tablist">
                        <li class="nav-item">
                            <%String textDes = session['wileySite'] ? ' Description' : 'Overview' %>
                            <%
                                boolean hideTOC1 = false

                                if (session['wileySite'] && showPrice){
                                    hideTOC1 = true
                                }
                            %>
                            <%if(!session['wileySite']){%>
                            <a class="nav-link active show" data-toggle="tab" href="#overview">${textDes}</a>
                            <%}else if(session['wileySite'] && !showPrice){%>
                                <a class="nav-link " data-toggle="tab" href="#overview">${textDes}</a>
                            <%}else if(hideTOC1){%>
                            <a class="nav-link active show" data-toggle="tab" href="#overview">${textDes}</a>
                            <%}%>
                        </li>
                        <%if(!session['wileySite']){%>
                        <li class="nav-item">
                            <a class="nav-link" data-toggle="tab" href="#features">Features</a>
                        </li>
                        <%}%>
                        <% if(bookType !='Paperback'){%>

                        <%
                                boolean hideTOC = false

                                if (session['wileySite'] && showPrice){
                                    hideTOC = true
                                }
                        %>
                        <li class="nav-item tableOfContent">
                        <%if(!session['wileySite']){%>
                            <a class="nav-link" data-toggle="tab" href="#table-contents">Table of Contents</a>
                        <%}else if(session['wileySite'] && !showPrice){%>
                        <a class="nav-link active show" data-toggle="tab" href="#table-contents">Table of Contents</a>
                        <%}%>
                        </li>
                        <%}%>
                    </ul>
                    <div class="tab-content pl-3">
                        <%
                            boolean hideTOC2 = false

                            if (session['wileySite'] && showPrice){
                                hideTOC2 = true
                            }
                        %>

                        <%if(!session['wileySite']){%>
                        <div class="tab-pane fade show active" id="overview">
                        <%}else if(session['wileySite'] && !showPrice){%>
                        <div class="tab-pane fade" id="overview">
                        <%}else if(hideTOC2){%>
                        <div class="tab-pane fade show active" id="overview">
                        <%}%>
                            <br>
                            <% if(booksMst.description == null || booksMst.description == '') { %>
                            <div class="book-desc" id="book-desc">No description available.</div>
                            <input value="" id="book-desc-hidden" class="form-control hidden">
                            <% } else { %>
                            <div class="book-desc" id="book-desc"></div>
                            <a href='javascript:' id="readMoreToggle" class='exp' style="display: none;">... Read more</a>
                            <input value="${booksMst.description}" id="book-desc-hidden" class="form-control hidden">
                            <%}%>
                        </div>
                        <div class="tab-pane fade" id="features">
                            <br>
                            <div class="details-book-about-cart mb-4">
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Author</span>
                                    <span class="second-table-cart">:</span>
                                    <span class="mrp_price">${booksMst.authors}</span>
                                </div>
                                <% if(booksMst.isbn == null || booksMst.isbn == '') { %>
                                <%} else { %>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">ISBN</span>
%{--                                    <span class="second-table-cart">:</span>--}%
                                    <span class="isbn_no">${booksMst.isbn}</span>
                                </div>
                                <%}%>

                                <% if(booksMst.bookCode == null || booksMst.bookCode == '') { %>
                                <%} else { %>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Book Code</span>
                                    <span class="second-table-cart">:</span>
                                    <span class="isbn_no">${booksMst.bookCode}</span>
                                </div>
                                <%}%>

                                <% if(bookType !='Paperback'){%>
                                <h5 class="mt-4">Book Contains</h5>
                                <%}%>
                                <div class="wrp-details-about-cart" id="notesCount" style="display: none;">
                                    <span class="first-table-cart">Notes</span>
                                    <span class="second-table-cart">:</span>
                                    <span id="notes-count"></span>
                                </div>
                                <div class="wrp-details-about-cart" id="videosCount" style="display: none;">
                                    <span class="first-table-cart">Videos</span>
                                    <span class="second-table-cart">:</span>
                                    <span id="video-count"></span>
                                </div>
                                <div class="wrp-details-about-cart" id="flashcardsCount" style="display: none;">
                                    <span class="first-table-cart">Flashcard</span>
                                    <span class="second-table-cart">:</span>
                                    <span id="flashcard-count"></span>
                                </div>
                                <div class="wrp-details-about-cart" id="solutionsCount" style="display: none;">
                                    <span class="first-table-cart">MCQs</span>
                                    <span class="second-table-cart">:</span>
                                    <span id="solution-count"></span>
                                </div>
                            </div>

                        </div>

                        <%
                            boolean hideTOC = false

                            if (session['wileySite'] && showPrice){
                                hideTOC = true
                            }
                        %>


                        <%if(!session['wileySite']){%>
                            <div class="tab-pane fade" id="table-contents">
                            <br>
                            <h5 class="mb-4">
                                <span id="chapter-count"></span>
                            </h5>
                            <ol id="chapter-list">

                            </ol>
                        </div>
                        <%}else if(session['wileySite'] && !showPrice){%>
                            <div class="tab-pane fade show active" id="table-contents">
                            <br>
                            <h5 class="mb-4">
                                <span id="chapter-count"></span>
                            </h5>
                            <ol id="chapter-list">

                            </ol>
                        </div>
                        <%}%>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for coupon code -->
    <div class="modal modal-modifier fade" id="bookNewDescription" >
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
            <div class="modal-content modal-content-modifier">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>

                <!-- Modal body -->
                <div class="modal-body modal-body-modifier text-left p-4" id="bookNewDescriptionModal">
                    <h2 class="text-center text-primary-modifier font-weight-bold ordText">ORDER DETAILS</h2>
                    <div class="orderDetailsModal">
                        <div class="image-wrapper-od">
                            <div class="od-img">
                                <% if(booksMst.coverImage==null){%>
                                <div class="uncoverdetail">
                                    <p>${booksMst.title}</p>
                                </div>
                                <%  }else{%>
                                <img src='/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.coverImage}&type=books&imgType=passport' class="img-responsive" />
                                <%}%>
                            </div>
                            <div class="od-title">
                                <h4 class="font-weight-bold" style="color: #6C757D !important;">${booksMst.title}</h4>
                            </div>
                        </div>
                        <div class="orderDetails-wrapper">
                            <div class="details-book-about-cart mb-4">
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Price</span>
                                    <span class="second-table-cart">:</span>
                                    <span class="mrp_price" id="orderDetailsPrice" >&#x20b9 <span id="odp">${eBookPrice}</span></span>
                                </div>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Type</span>
                                    <span class="second-table-cart">:</span>
                                    <% if(booksMst.bookType == 'ebook'){%>
                                    <span class="mrp_price" id="">Digital eBook</span>
                                    <%  }else{%>
                                    <span class="mrp_price">${booksMst.bookType}</span>
                                    <%  }%>
                                </div>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">PDF Download</span>
                                    <span class="second-table-cart">:</span>
                                    <span class="mrp_price" >No</span>
                                </div>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Return & Refund</span>
                                    <span class="second-table-cart">:</span>
                                    <span class="mrp_price">No</span>
                                </div>
                                <div class="wrp-details-about-cart">
                                    <span class="first-table-cart">Platform supported </span>
                                    <span class="second-table-cart">:</span>
                                    <% if("true".equals(session['prepjoySite'])){%>
                                    <span class="mrp_price">Android & IOS</span>
                                    <% } else {%>
                                    <span class="mrp_price">Web & Android</span>
                                    <% }%>
                                </div>

                                <% if("true".equals(session['prepjoySite'])){%>
                                <div class="wrp-details-about-cart text-center mt-3">
                                    <p style="color: #777 !important;" class="font-weight-bold">You can access the book only in the prepjoy mobile app.</p>
                                </div>
                                <% } else {%>
                                <div class="wrp-details-about-cart text-center mt-3">
                                    <p style="color: #777" class="font-weight-bold">Use eBooks save trees <i class="fa fa-tree text-success"></i></p>
                                </div>
                                <% }%>
                            </div>
                        </div>
                    </div>
                    <div class="row p-2" style="text-align: end">
                        <div class="col col-6">
                            <button class="btn btn-block" id="closeBookDesc">Cancel</button>
                        </div>
                        <div class="col col-6">
                            <button class="btn btn-primary-modifier btn-block" id="okBuy">Pay now</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for  prepjoy -->
    <div class="modal modal-modifier fade" id="addBookToLibPrep" >
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
            <div class="modal-content modal-content-modifier">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>

                <!-- Modal body -->
                <div class="modal-body modal-body-modifier text-left p-5" id="addBookToLibContent">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_f5l478qj.json" id="freeBk" background="transparent"  speed="1"  style="width: 200px; height:150px;"  loop autoplay></lottie-player>
                    <div>
                        <h2 class="text-center text-white mt-4">You can access this eBook only in prepjoy app</h2>
                        <div class="d-flex mt-5" style="gap: 10px">
                            <button class="btn btn-block" id="cancelFreeBk">Cancel</button>
                            <button class="btn btn-primary-modifier btn-block mt-0" id="continue" onclick="addFreeBook()">Continue</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal for  prepjoy -->
    <div class="modal modal-modifier fade" id="purchasePepjoyBookModal" >
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
            <div class="modal-content modal-content-modifier">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>
                <!-- Modal body -->
                <div class="modal-body modal-body-modifier text-left p-5" id="purchasePepjoyBook">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_f5l478qj.json" id="addbk" background="transparent"  speed="1"  style="width: 200px; height:150px;"  loop autoplay></lottie-player>
                    <div>
                        <h2 class="text-center text-white mt-4">You can access this eBook only in prepjoy app</h2>
                        <div class="d-flex mt-5" style="gap: 10px">
                            <button class="btn btn-block" id="cancelPurchase">Cancel</button>
                            <button class="btn btn-primary-modifier btn-block mt-0" id="contPurchase">Buy Book</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for  prepjoy -->
    <div class="modal modal-modifier fade" id="openBookModal" >
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
            <div class="modal-content modal-content-modifier" style="background: #0F0839">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>
                <!-- Modal body -->
                <div class="modal-body modal-body-modifier text-left p-5" id="openBookId">
                    <div class="linkWrapper">
                        <a target="_blank" class="d-flex align-items-center justify-content-center mr-3" id="playstr">
                            <img src="${assetPath(src: 'prepJoy/prepjoy-website/playstoreNew.svg')}" class="mr-2" style="width: 40px">
                            <div>
                                <p class="dwnp">Download from</p>
                                <h4 class="text-white dwnh">PLAY STORE</h4>
                            </div>
                        </a>
                        <a target="_blank" class="d-flex align-items-center justify-content-center" id="appstr">
                            <img src="${assetPath(src: 'prepJoy/prepjoy-website/appstoreNew.svg')}" class="mr-2" style="width: 40px">
                            <div>
                                <p class="dwnp">Download from</p>
                                <h4 class="text-white dwnh">APP STORE</h4>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

    <div id="relatedDoubts" class="books-list">
        <g:render template="/resources/relatedBooks"></g:render>
    </div>
    <%if(session["wileySite"]){%>
    <g:render template="/resources/collectionBooks"></g:render>
    <%}%>
</section>
<a href="https://www.wonderslate.com/${booksMst.title.replace(' ','-')}/ebook-details?${request.getQueryString()}"> </a>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>


<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<script src="/assets/prepjoy/lottie.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<asset:javascript src="searchContents.js"/>
<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<g:render template="/${session['entryController']}/footer_new"></g:render>
%{--<g:render template="/funlearn/topicscripts"></g:render>--}%
<g:render template="/wonderpublish/buyOrAdd"></g:render>
<asset:javascript src="wonderslate/trunk8.js"/>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<script>
    var tokenId="${params.tokenId}";
    var bookResources=JSON.parse("${bookResources}".replace(/&quot;/g,'"'));
    var totalMcq="${totalMcqs}";
    var booksData=JSON.parse((("${chapters}").replace(/&#92;/g,'\\')).replace(/&quot;/g,'"'));
    var encodedBookTitle=replaceAll("${booksMst.title}",' ','-');
    var count="";
    var notesCount="";
    var videocount="";
    var solutioncount="";
    var flashcardcount="";
    var level="${booksTagDtl.level}";
    var syllabus="${booksTagDtl.syllabus}";
    var grade="${booksTagDtl.grade}";
    var siteName="";
    var amazonPrice = "";
    var amazonLink = "";
    var flipkartPrice ="";
    var flipkartLink = "";

    <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
    siteName="${session['siteName']}";
    <%}else{%>
    siteName="${session['entryController']}";
    <% }%>

    if (siteName.includes("currentaffairs")) siteName="currentaffairs";

    function openBreadcrumbLink(level,syllabus,grade) {
        var paramLevel = replaceAll(level.replace('&', '~'),' ','-');
        var paramSyllabus = replaceAll(syllabus.replace('&', '~'),' ','-');
        var paramGrade = replaceAll(grade.replace('&', '~'),' ','-');

        <%if("true".equals(""+session["commonWhiteLabel"])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel;
        }
        <%}else if (!"true".equals(session['prepjoySite'])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel;
        }
        <%} else {%>
        if(grade!="" && grade!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel;
        }
        <%}%>
    }

    function openBook(bookId) {
        JSInterface.openBook(bookId);
    }


    function startPayment(bookId,price,title,clientName,publisher,coverImage) {
        JSInterface.startPayment(bookId,price,title,clientName,publisher,coverImage);
    }


    function openInLibrary(bookId) {
        JSInterface.openInLibrary(bookId);
    }

    function addToLibrary(bookId) {
        JSInterface.addToLibrary(bookId);
    }

    //ADDING FREE BOOKS TO LIBRARY1
    function addBookToLib(){
            addToMyLibrary('${booksMst.id}','${eBookPrice}','${booksMst.title.replace("'","&#39;")}');
    }

    function openAddBookModal(){
        $('#addBookToLibPrep').modal('show');
    }
    function closeAddBookModal(){
        $('#addBookToLibPrep').modal('hide');
    }

    function addFreeBook(){
        closeAddBookModal()
        addToMyLibrary('${booksMst.id}','${eBookPrice}','${booksMst.title.replace("'","&#39;")}');
    }
    $('#linkTestandLibraryPrepjoy').on('click',function (){
        bookOpenModal();
    })

    function showBookDetails() {
        if($('#book-desc-hidden').val()!="" && $('#book-desc-hidden').val()!=null)
        {
            document.getElementById("book-desc").innerHTML = $('#book-desc-hidden').val();
        }
        var resourceType=bookResources;
        var data = booksData;
        var cCount="";
        count=  data.length;
        if (count == 1) {
            cCount += count+"  Chapter";
        } else {
            cCount += count+"  Chapters";
        }
        document.getElementById("chapter-count").innerHTML = cCount;
        document.getElementById("chapter-count").style.display = 'none';
        var htmlStr = "";
        for(var i=0;i<data.length;i++){
            htmlStr = htmlStr + "<li class=\"chapter-title\">"+data[i].name+"</li>";
        }
        $('#chapter-list').html(htmlStr);
        for(var j=0;j<resourceType.length;j++){
            var res=resourceType[j].res_type;
            if(res=="Notes"){
                notesCount=resourceType[j].res_count;
                $("#notes-count").text(notesCount);
                $("#notesCount").css("display", "block");
            }else  if(res=="KeyValues"){
                flashcardcount=resourceType[j].res_count;
                $("#flashcard-count").text(flashcardcount);
                $("#flashcardsCount").css("display", "block");
            }else if(res=="Reference Videos") {
                videocount=resourceType[j].res_count;
                $("#video-count").text(videocount);
                $("#videosCount").css("display", "block");
            }else if(res=="Multiple Choice Questions"){
                solutioncount=totalMcq;
                $("#solution-count").text(solutioncount);
                $("#solutionsCount").css("display", "block");

            }
        }

    }

    showBookDetails();

    var cHtml;
    function truncateContent() {
        $(".book-desc").addClass("compressed").html(cHtml);
        $('#book-desc').trunk8({
            fill: '...',
            lines: 5,
            side: 'right'
        });
    }

    $(document).ready(function(){
        //Popular Searches Slider
        $("#relatedbook").slick({
            dots: false,
            arrows: true,
            infinite: false,
            speed: 1000,
            slidesToShow: 6,
            slidesToScroll: 1,
            adaptiveHeight: false,
            autoplay: false,
            autoplaySpeed: 5000,
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 4,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                },
                {
                    breakpoint: 767,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                }
            ]
        });

        // Read more or less description content
        length = 300;
        var cLength = $("#book-desc-hidden").val().length;
        cHtml = $("#book-desc-hidden").val();
        if(cLength>300) {
            truncateContent();
            $("#readMoreToggle").show();
        }
        encodeBookTitle();
        window.handler = function() {
            $('.exp').click(function(){
                if ($(".book-desc").hasClass("compressed")) {
                    $("#readMoreToggle").hide();
                    $(".book-desc").html(cHtml + "<a href='javascript:' class='exp d-block'>... Read less</a>");
                    $(".book-desc").removeClass("compressed");
                    handler();
                    if($(window).width()<767) {
                        $("#book-desc").find('a').addClass('d-inline-block text-truncate').css('max-width','270px');
                    }
                    return false;
                } else {
                    truncateContent();
                    $("#readMoreToggle").show();
                    $(".book-desc").addClass("compressed");
                    handler();
                    return false;
                }
            });
        }
        handler();
        <%if("purchase".equals(params.mode)){%>
        buyBook(${booksMst.id},originalPrice,discountId,'${booksMst.title.replace("'","&#39;")}','book');
        <%}%>
    });
    <%if("1".equals(""+session["siteId"]) || "true".equals(session["prepjoySite"])){%>
    getAffiliationPrices();
    <%}%>
    function getAffiliationPrices(){
        <g:remoteFunction controller="affiliation" action="getAffiliationPrices"  onSuccess='receivedAffiliationPrices(data);'
        params="'bookId=${booksMst.id}&bookType=eBook'" />
    }
    function openAffliatePage(bookId,linkType,productLink){
        <g:remoteFunction controller="log" action="createAffLog" params="'bookId='+bookId+'&bookType=eBook&source=web&siteId=${session["siteId"]}&linkType='+linkType+'&productLink='+productLink"/>
        window.open(productLink, '_blank');
    }
    function receivedAffiliationPrices(data){
        amazonPrice = data.amazonPrice;
        amazonLink = data.amazonLink;
        flipkartPrice = data.flipkartPrice;
        flipkartLink = data.flipkartLink;
        var htmlStr="";
        if((amazonPrice!=null && amazonLink !=null) && (!amazonPrice==""&&!amazonPrice=="0")){
            htmlStr =  "<a href=\"javascript:openAffliatePage(${booksMst.id},'Amazon','"+amazonLink+"');\" class='amazonPrice fieldSet'>"+
                "<p class='fieldSet_legend'>&#x20b9 <span>"+amazonPrice+"</span></p>"+
                "<span>" +
                "<img src='${assetPath(src: 'resource/amazonAff.svg')}' class='amazonLogo'>"+
                "<p class='ctaPrice'>Click to check details</p>"+
                "</span>"+
                "</a>";
        }else if (amazonLink!=null && amazonPrice==null){
            htmlStr =  "<a href=\"javascript:openAffliatePage(${booksMst.id},'Amazon','"+amazonLink+"');\" class='amazonPrice fieldSet'>"+
                "<p class='fieldSet_legend'><span>G0</span></p>"+
                "<span>" +
                "<img src='${assetPath(src: 'resource/amazonAff.svg')}' class='amazonLogo'>"+
                "<p class='ctaPrice'>Click to check details</p>"+
                "</span>"+
                "</a>";
        }

        if((flipkartPrice!=null && flipkartLink!=null) && (!flipkartPrice==""&&!flipkartPrice=="0")){
            htmlStr +=  "<a href=\"javascript:openAffliatePage(${booksMst.id},'Flipkart','"+flipkartLink+"');\" class='amazonPrice fieldSet'>"+
                "<p class='fieldSet_legend'>&#x20b9 <span>"+flipkartPrice+"</span></p>"+
                "<span>" +
                "<img src='${assetPath(src: 'resource/flipkartAff.svg')}' class='flipkartLogo'>"+
                "<p class='ctaPrice'>Click to check details</p>"+
                "</span>"+
                "</a>";
        }else if (flipkartLink!=null && flipkartPrice==null){
            htmlStr +=  "<a href=\"javascript:openAffliatePage(${booksMst.id},'Flipkart','"+flipkartLink+"');\" class='amazonPrice fieldSet'>"+
                "<p class='fieldSet_legend'><span>Go</span></p>"+
                "<span>" +
                "<img src='${assetPath(src: 'resource/flipkartAff.svg')}' class='flipkartLogo'>"+
                "<p class='ctaPrice'>Click to check details</p>"+
                "</span>"+
                "</a>";
        }

        if ((amazonPrice==null && amazonLink==null) && flipkartPrice==null && flipkartLink==null){
            document.querySelector('.affiliationPrices').style.display='none';
            document.querySelector('.affiliationPrices').innerHTML = '';
        }
        setTimeout(function (){
            if ((flipkartPrice > '${eBookPrice}' && amazonPrice > '${eBookPrice}') && (document.querySelector('.bestPrice')!=null)){
                document.querySelector('.bestPrice').classList.remove('d-none');
            }
            document.querySelector('.affiliationLinks').innerHTML = htmlStr
        },4000)
    }
    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function encodeBookTitle(){
        var bookLink = "/library/"+encodedBookTitle+"?";
        if("${mainResourcesPage}" == "true") {
            bookLink = "/"+encodedBookTitle+"/ebook?";
        }
        if((!("print"===(""+"${booksMst.bookType}"))) ){
            if(!("0"===(""+"${eBookPrice}")||"0.0"===(""+"${eBookPrice}")))  {
                if ("${inLibrary}"=="true" && "${session["appType"]}" != "android") {
                    document.getElementById("linkLibrary").href=bookLink+"siteName="+siteName+'&bookId='+'${booksMst.id}'

                }

            }
            else
            {
                if (("${inLibrary}"=="true") && "${session["appType"]}" != "android") {
                    document.getElementById("linkTestandLibrary").href=bookLink+"siteName="+siteName+'&bookId='+'${booksMst.id}'

                }
                else if("${session["appType"]}" != "android"){
                    document.getElementById("linkOthers").href=bookLink+"siteName="+siteName+'&bookId='+'${booksMst.id}'

                }
            }

        }

    }

    function openAccessCode(bookTitle,bookId){
        <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")==0){%>
        showRegistration();
        <% } else {%>
        var bookTitle=bookTitle;
        var bookId=bookId;
        window.location.href = '/wsLibrary/accessCode?bookLevel=true&bookId='+bookId+'&bookTitle='+bookTitle;
        <% }%>
    }


    function buyBookAppinAppPurchase(razorpayPaymentId,bookId,paymentFrom){
        <g:remoteFunction controller="wonderpublish" action="purchase"  onSuccess='buyBookAppinAppStatusPurchase(data);'
        params="'razorpay_payment_id='+razorpayPaymentId+'&bookId='+bookId+'&paymentFrom='+paymentFrom" />
    }

    function buyBookAppinAppStatusPurchase(data){
        if(data.status=="success" && data.poNo!=null){
            onBookPurchasePurchase("OK");
        }else{
            onBookPurchasePurchase("FAIL");
        }
    }

    function onBookPurchasePurchase(status) {
        JSInterface.onBookPurchasePurchase(status);
    }

</script>


%{--------DISCOUNT FUNCTIONALITY------}%
<script>
    var discountValue;
    var discountPercentage;
    var resultList;
    var discountId;
    var bookId = "${params.bookId}";
    var isloggedIn = false;
    <%if(session['userdetails']!=null){%>
    var username= "${session['userdetails'].username}";
    username = username.split('_')[1];
    isloggedIn = true;
    <%}%>

    var originalPrice = "${eBookPrice}";  //Getting original books price
    var tempDiscountVal;
    var tempFinalVal = 0; //Storing temporary original price
    var manualCouponInput; //Getting input for manual coupon
    var manualCouponList;
    var appliedCodeId;
    var checkedCodeId;
    var inLibrary = false;
    var appended = false;
    var siteId = "${session['siteId']}";
    var prepjoySite = "${session['prepjoySite']}";
    var psiteName = "${session['siteName']}";
    originalPrice = parseFloat(originalPrice);
    <% if(inLibrary){%>inLibrary = true;<% }%>
    // Initating payment
    function initatePayment(){
        if (tempFinalVal!=0){
            originalPrice = tempFinalVal.toString();
            buyBook(${booksMst.id},originalPrice,discountId,'${booksMst.title.replace("'","&#39;")}','book');
        }else{
            discountId = undefined;
            originalPrice = "${eBookPrice}";
            buyBook(${booksMst.id},originalPrice,discountId,'${booksMst.title.replace("'","&#39;")}','book');
        }
    }
</script>


<script>

    //ORDER DETAILS MOODAL
    function openBookDescriptionModal(){
        if (!prepjoySite){
            $('#bookNewDescription').modal('show');
        }else{
            $('#purchasePepjoyBookModal').modal('show')
        }
    }

    //INITIATING PAYMENT
    $('#okBuy').on('click',function (){
        initatePayment()
        $('#bookNewDescription').modal('hide');
    });

    $('#contPurchase').on('click',function (){
        initatePayment();
        $('#purchasePepjoyBookModal').modal('hide')
    });

    $('#closeBookDesc').on('click',function (){
        $('#bookNewDescription').modal('hide');
    });

    $('#cancelFreeBk').on('click',function (){
        $('#addBookToLibPrep').modal('hide')
    });
    $("#cancelPurchase").on('click',function (){
        $('#purchasePepjoyBookModal').modal('hide')
    });
</script>

<script>

    var playStoreLink;
    var appStoreLik;
    var appName = "${session['siteName']}";
    links(appName)
    function links(siteName){
        if (siteName.includes('currentaffairs')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy';
            appStoreLik = 'https://apps.apple.com/in/app/prepjoy-current-affairs/id1595285082';
        }

        if (siteName.includes('karnataka')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
            appStoreLik = 'https://apps.apple.com/us/app/id1611175104';
        }

        if (siteName.includes('neet')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.neet&hl=en-GB'
            appStoreLik = 'https://apps.apple.com/in/app/prepjoy-neet/id1613665117';
        }

        if (siteName.includes('enggentrances')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.engineering&hl=en-GB';
            appStoreLik = 'https://apps.apple.com/in/app/prepjoy-engg-entrance-exams/id1615699605';
        }
        if (siteName.includes('cacscma')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.ca';
            appStoreLik = 'https://apps.apple.com/in/app/prepjoy-ca-cs-cma/id1616647213';
        }
        if (siteName.includes('ctet')){
            playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.ctet&hl=en-GB';
            appStoreLik = 'https://apps.apple.com/us/app/prepjoy-teaching-exams/id1620819618';
        }
    }

    $('#playstr').attr('href',playStoreLink);
    $('#appstr').attr('href',appStoreLik);


    function openBook(bookType,inputBookTitle,inputBookId){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        registerBookTitle = replaceAll(inputBookTitle,' ','-');
        registerBookId = inputBookId;
        window.location.href = "/"+registerBookTitle+"/ebook?bookId="+registerBookId+"&siteName=${params.siteName}";
    }

    var fromEmail = "${params.emailCampaign}";
    var encryptedLoginId = "${params.username}";
    if(fromEmail) {
        <%if(session['userdetails']==null){%>
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="log" action="login"  onSuccess="autoLoginDone(data);" params="'source=web&autoLogin=true&loginId='+encryptedLoginId" />
        <%}%>
    }

    function autoLoginDone(data) {
        if ("ok" == data.status || "OK" == data.status) {
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            location.reload();
        }
    }

    $(".book_variants a.card").on("click", function() {
        $(".book_variants a.card.active").removeClass("active");
        $(this).addClass("active");
    });

    if (prepjoySite){
        function openRegister(){
            $('#prepjoySignupModal').modal('show');
        }
    }
<%if(subscriptionMst!=null){%>
   function subscribeNow(price){
        if(document.getElementById("subscriptionId").selectedIndex==0){
            document.getElementById('subDurErr').classList.remove('d-none');
            setTimeout(function (){
                document.getElementById('subDurErr').classList.add('d-none');
            },1500)
            document.getElementById("subscriptionId").focus();
        }
        else if(document.getElementById("subscriptionMonthBookId").selectedIndex==0){
            document.getElementById('subMonErr').classList.remove('d-none');
            setTimeout(function (){
                document.getElementById('subMonErr').classList.add('d-none');
            },1500)
            document.getElementById("subscriptionMonthBookId").focus();
        }else{
            var subscriptionDurationStr = document.getElementById("subscriptionId")[document.getElementById("subscriptionId").selectedIndex].value.split("-");
            var duration = subscriptionDurationStr[0];
            var price = subscriptionDurationStr[1];

            var subsStartingBookId = document.getElementById("subscriptionMonthBookId")[document.getElementById("subscriptionMonthBookId").selectedIndex].value;
            addSubscriptionToCartFromDtl('${booksMst.id}',${subscriptionMst.id},subsStartingBookId,duration);
        }
   }
   <%}%>

    <%if("true".equals(params.addToCart)){%>
    addToCartFromDtl('${booksMst.id}','${params.bookType}');
    <%}%>

    var variantElements = document.querySelectorAll('.book_variants-variant');
    if (navigator.userAgentData!=undefined){
        const isMobileDiv = navigator.userAgentData.mobile;
    }


</script>


<script type="application/ld+json">

{
"@context": "https://schema.org/",
  "@type": "Book",
  "name":"${booksMst.title}",
  "image": "${bookCoverImage}",
  "description": "${booksMst.description!=null?booksMst.description.replace('<p>','').replace('</p>',''):""}",
  "isbn": "${booksMst.isbn}",
  "offers": {
   "@type": "AggregateOffer",
   "url": "${request.getRequestURL()}",
   "priceCurrency": "INR",
   <%if(eBookPrice){%>
   "lowPrice": "${eBookPrice}",
   "highPrice": "${eBookListPrice}"
   <%}%>
   },
  "inLanguage": {
   "@type": "Language",
   "name": [
   "${booksMst.language!=null?booksMst.language:"English"}"
    ]
  },
  "educationalUse": "Study Materials",
  "publisher":"${publisherName}"

 }


</script>


</body>
</html>
