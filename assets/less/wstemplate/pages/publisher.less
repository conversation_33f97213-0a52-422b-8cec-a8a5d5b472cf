@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Publisher Page Styles
.publisher_page {
  #slider-desktop {
    background: #FFFDF5;
    min-height: 400px;
    margin-bottom: -2rem;
    @media @extraSmallDevices,@smallDevices {
      min-height: 350px;
    }
    .carousel-inner, .carousel-item {
      min-height: 400px;
      @media @extraSmallDevices,@smallDevices {
        min-height: 350px;
      }
      img.banner-img, .no-banners {
        min-height: 400px;
        @media @extraSmallDevices,@smallDevices {
          min-height: 350px;
        }
      }
    }
    .carousel-indicators {
      margin-bottom: 3rem;
      li {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: lighten(@theme-primary-color,50%);
        &.active {
          background: @theme-primary-color;
        }
      }
    }
  }
  #slider-mobile {
    background: #FFFDF5;
    min-height: 300px;
    margin-bottom: -1.5rem;
    @media @extraSmallDevices {
      min-height: 250px;
    }
    .carousel-inner, .carousel-item {
      min-height: 300px;
      @media @extraSmallDevices {
        min-height: 250px;
      }
      img.banner-img {
        min-height: 300px;
        @media @extraSmallDevices {
          min-height: 250px;
        }
      }
      .no-banners {
        min-height: 350px;
      }
    }
    .carousel-indicators {
      margin-bottom: 2rem;
      li {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: lighten(@theme-primary-color,50%);
        &.active {
          background: @theme-primary-color;
        }
      }
    }
  }
  #banner-carousel, #mobile-banner-carousel {
    h1 {
      font-size: 25px;
      color: @black;
      @media @extraSmallDevices,@smallDevices {
        font-size: 20px;
      }
    }
    .books-list {
      .topSchoolBooks {
        &:hover {
          box-shadow: 0 0 10px @gray-dark-shadow;
        }
        .image-wrapper, .uncover {
          height: 190px;
        }
        .image-wrapper img {
          height: 190px;
        }
      }
    }
  }
  .publisher_categories {
    #category-inner {
      .category-menu {
        white-space: nowrap;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        text-align: left;
      }
      .btn {
        font-size: 16px;
        @media @extraSmallDevices {
          font-size: 12px;
        }
      }
      .dropdown {
        .dropdown-menu {
          z-index: 991;
        }
        .dropdown-list-item {
          border-radius: 0;
          &:hover {
            background-color: #f5f5f5;
          }
        }
        //&:last-child {
        //  .dropdown-menu {
        //    right: 0;
        //    left: auto !important;
        //  }
        //}
      }
      .show_more_categories {
        position: absolute;
        right: 0;
        z-index: 1;
        @media @extraSmallDevices {
          i {
            font-size: 20px;
          }
        }
      }
    }
  }
  .carousel-inner, .carousel-item {
    .no-banners {
      @media @extraSmallDevices,@smallDevices {
        flex-direction: column;
      }
      .banner-books {
        margin-right: -1.5rem;
        margin-left: -1.5rem;
      }
    }
  }
}