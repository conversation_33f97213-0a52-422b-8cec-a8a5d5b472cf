<!doctype html>
<%if("true".equals(session["commonWhiteLabel"])){%>
<link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
<%}else{%>
<link rel="icon"  href="${assetPath(src: "favicons/${session['entryController']}.png")}" type="image/x-icon">
<%}%>

<style>
html,body{
    overflow: hidden;
}
</style>

<div class="mdl-layout__container_ebook">
    <div class="loading-icon hidden">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>

<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<g:render template="/resources/allSection"></g:render>
<g:render template="/resources/notesHighlightSection"></g:render>
<g:render template="/resources/resourceContents"></g:render>
<g:render template="/resources/readSection"></g:render>
<g:render template="/resources/readerView"></g:render>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var previousChapterId=${topicId};
    var chapterIdForPDF = ${topicId};
    var tempTopicId = JSON.parse(localStorage.getItem('lastReadPDF'));
    if (tempTopicId!=null && tempTopicId != "" && tempTopicId !=undefined){
        $('.loading-icon').removeClass('hidden');
        if((tempTopicId.pdfOpen=='true' || tempTopicId.pdfOpen==true) && tempTopicId.bookId==${params.bookId}){
            chapterIdForPDF = tempTopicId.chapterId;
        }
    }
    var loggedInUser = false;
    var masterChapterID = chapterIdForPDF;

    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>
    getHtmlsDataByChapter(masterChapterID);

</script>
