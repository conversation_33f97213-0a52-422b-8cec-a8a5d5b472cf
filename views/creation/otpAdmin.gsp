<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
.panel {
    margin-bottom: 20px;
}
.table th {
    background-color: #f5f5f5;
}
.alert {
    margin-bottom: 15px;
}
.panel-info .panel-heading {
    background-color: #d9edf7;
    border-color: #bce8f1;
}
.loading-icon {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
}
.loader-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.loader {
    color: white;
    font-size: 18px;
}
</style>
<style>
/* Main container styling for better centering and spacing */
.main-container {
    min-height: 100vh;
    display: flex;
    padding: 60px 0;
    background-color: #f8f9fa;
}

.main-panel {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: none;
}

.main-panel .panel-heading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 20px;
}

.main-panel .panel-body {
    padding: 30px;
}

.results-panel {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: none;
    margin-top: 30px;
}

.results-panel .panel-heading {
    background-color: #28a745;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
}

.results-panel .panel-body {
    padding: 25px;
}

/* Form styling improvements */
.form-group {
    margin-bottom: 25px;
}

.form-control {
    height: 45px;
    border-radius: 6px;
    border: 2px solid #e9ecef;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.control-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 6px;
    margin-right: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: transform 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-default {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-default:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    transition: transform 0.2s ease;
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(220, 53, 69, 0.3);
}

/* Table styling */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Alert styling */
.alert {
    border-radius: 6px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Panel info styling */
.panel-info {
    border-radius: 6px;
    border: 1px solid #bee5eb;
}

.panel-info .panel-heading {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 6px 6px 0 0;
    padding: 15px 20px;
}

.panel-info .panel-body {
    padding: 20px;
    background-color: #f8f9fa;
}

.panel-info h5 {
    color: #0c5460;
    font-weight: 600;
    margin-top: 15px;
    margin-bottom: 10px;
}

.panel-info h5:first-child {
    margin-top: 0;
}

.panel-info ul {
    margin-bottom: 15px;
}

.panel-info li {
    margin-bottom: 8px;
}

/* Loading icon improvements */
.loading-icon {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loader-wrapper {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.loader {
    color: #667eea;
    font-size: 18px;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main-container {
        padding: 20px 0;
    }

    .main-panel .panel-body,
    .results-panel .panel-body {
        padding: 20px 15px;
    }

    .btn-lg {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }

    .col-sm-offset-3 {
        margin-left: 0;
    }
}
</style>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="main-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10 col-sm-12">
                <div class="panel panel-default main-panel">
                    <div class="panel-heading text-center">
                        <h3 class="panel-title">OTP Admin - Search OTPs</h3>
                    </div>
                    <div class="panel-body">
                        <form id="otpSearchForm" class="form-horizontal">
                            <div class="form-group">
                                <label for="contact" class="col-sm-3 control-label">Contact (Email/Mobile):</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" id="contact" name="contact" placeholder="Enter email or mobile number" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="siteId" class="col-sm-3 control-label d-none">Site ID:</label>
                                <div class="col-sm-9">
                                    <input type="hidden" class="form-control" id="siteId" name="siteId" placeholder="Enter site ID" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" class="btn btn-primary btn-lg">Search OTPs</button>
                                    <button type="button" class="btn btn-default btn-lg" id="clearForm">Clear</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center" id="resultsSection" style="display: none;">
            <div class="col-lg-10 col-md-12 col-sm-12">
                <div class="panel panel-default results-panel">
                    <div class="panel-heading">
                        <h3 class="panel-title">Search Results</h3>
                    </div>
                    <div class="panel-body">
                        <div id="otpResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#otpSearchForm').on('submit', function(e) {
        e.preventDefault();
        searchOTPs();
    });

    $('#clearForm').on('click', function() {
        $('#otpSearchForm')[0].reset();
        $('#resultsSection').hide();
    });

    function searchOTPs() {
        var contact = $('#contact').val().trim();
        var siteId = "${session["siteId"]}";

        if (!contact) {
            alert('Please enter a contact (email or mobile number)');
            return;
        }

        if (!siteId) {
            alert('Please enter a site ID');
            return;
        }

        $('.loading-icon').removeClass('hidden');

        var requestData = {
            contact: contact,
            siteId: siteId
        };

        $.ajax({
            url: '/creation/getOtpsByContact',
            type: 'POST',
            data: requestData,
            success: function(response) {
                $('.loading-icon').addClass('hidden');
                displayResults(response);
            },
            error: function(xhr, status, error) {
                $('.loading-icon').addClass('hidden');
                alert('Error occurred while searching OTPs: ' + error);
            }
        });
    }


    function displayResults(response) {
        var resultsHtml = '';

        if (response.status === 'OK' && response.results && response.results.length > 0) {
            resultsHtml += '<div class="alert alert-success">';
            resultsHtml += '<strong>Found ' + response.count + ' OTP record(s) for contact: ' + response.contact + '</strong>';
            resultsHtml += '</div>';

            // Add Clear OTPs button
            resultsHtml += '<div class="text-right" style="margin-bottom: 15px;">';
            resultsHtml += '<button type="button" class="btn btn-danger" id="clearOtpsBtn">';
            resultsHtml += '<i class="fa fa-trash"></i> Clear All OTPs';
            resultsHtml += '</button>';
            resultsHtml += '</div>';

            resultsHtml += '<div class="table-responsive">';
            resultsHtml += '<table class="table table-striped table-bordered">';
            resultsHtml += '<thead><tr><th>ID</th><th>OTP</th><th>Contact</th><th>Date Created</th></tr></thead>';
            resultsHtml += '<tbody>';

            response.results.forEach(function(otp) {
                resultsHtml += '<tr>';
                resultsHtml += '<td>' + otp.id + '</td>';
                resultsHtml += '<td><strong>' + otp.otp + '</strong></td>';
                resultsHtml += '<td>' + otp.contact + '</td>';
                resultsHtml += '<td>' + new Date(otp.dateCreated).toLocaleString() + '</td>';
                resultsHtml += '</tr>';
            });

            resultsHtml += '</tbody></table></div>';
        } else {
            resultsHtml += '<div class="alert alert-warning">';
            resultsHtml += '<strong>No OTPs found for the given contact</strong>';
            if (response.siteId) {
                resultsHtml += ' and site ID';
            }
            resultsHtml += '</div>';

            resultsHtml += '<div class="panel panel-info">';
            resultsHtml += '<div class="panel-heading"><h4>Steps to Verify:</h4></div>';
            resultsHtml += '<div class="panel-body">';
            resultsHtml += '<h5>1. OTP Verification:</h5>';
            resultsHtml += '<p>Confirm whether the user has received the OTP and successfully validated it.</p>';

            resultsHtml += '<h5>2. Troubleshooting Registration & Purchase Issues:</h5>';
            resultsHtml += '<p>If the issue persists after OTP validation:</p>';
            resultsHtml += '<ul>';
            resultsHtml += '<li>Ask the user to try <strong>Sign up</strong> or <strong>Register</strong>.</li>';
            resultsHtml += '<li>Next, Guide them to <strong>purchase the book from the store</strong> after registration.</li>';
            resultsHtml += '<li>If the problem still occurs, request them to try using a <strong>different device</strong> or <strong>another browser</strong>.</li>';
            resultsHtml += '</ul>';

            resultsHtml += '<h5>3. Collect Technical Details:</h5>';
            resultsHtml += '<p>Ask the user to share their <strong>device type</strong> (e.g., mobile, tablet, laptop) and <strong>browser details</strong> (e.g., Chrome, Safari, Firefox).</p>';
            resultsHtml += '</div>';
            resultsHtml += '</div>';
        }

        $('#otpResults').html(resultsHtml);
        $('#resultsSection').show();

        // Bind click event to the clear button if it exists
        $('#clearOtpsBtn').off('click').on('click', function() {
            clearOTPs();
        });
    }
});

// Global function for clearing OTPs
function clearOTPs() {
    var contact = $('#contact').val().trim();
    var siteId = "${session["siteId"]}";

    if (!contact || !siteId) {
        alert('Contact and Site ID are required to clear OTPs');
        return;
    }

    if (!confirm('Are you sure you want to clear all OTPs for this contact and site ID? This action cannot be undone.')) {
        return;
    }

    $('.loading-icon').removeClass('hidden');

    $.ajax({
        url: '/creation/deleteUserOtp',
        type: 'POST',
        data: {
            contact: contact,
            siteId: siteId
        },
        success: function(response) {
            $('.loading-icon').addClass('hidden');
            if (response.status === 'success') {
                alert('OTPs cleared successfully');
                // Refresh the search results by calling the form submit
                $('#otpSearchForm').trigger('submit');
            } else {
                alert('Error clearing OTPs: ' + (response.message || 'Unknown error'));
            }
        },
        error: function(xhr, status, error) {
            $('.loading-icon').addClass('hidden');
            alert('Error occurred while clearing OTPs: ' + error);
        }
    });
}
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>