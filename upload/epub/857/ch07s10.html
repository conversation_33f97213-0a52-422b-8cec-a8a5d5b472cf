<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Adjusting retraction with Skeinforge</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Adjusting retraction with Skeinforge"><div class="titlepage"><div><div><h1 class="title"><a id="ch07lvl1sec99"/>Adjusting retraction with Skeinforge</h1></div></div></div><p>Our best Skeinforge plugin for eliminating ooze and stringing is the retraction setting in <span class="strong"><strong>Dimension</strong></span>. This plugin allows<a id="id536" class="indexterm"/> for adjustments that control how much filament is fed to the hot end.</p><p>In this recipe, we're going to make adjustments with retraction speeds and distances to better refine our printing results.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec240"/>Getting ready</h2></div></div></div><p>We'll use the SketchUp model<a id="id537" class="indexterm"/> for this recipe as well. An extruder using a Bowden cable system would be great for testing these parameters. Bowden cables have a tendency to ooze and string a filament.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec241"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">In Skeinforge, open the <span class="strong"><strong>Dimension</strong></span> plugin. Go to <span class="strong"><strong>Extruder Retraction Speed (mm/s)</strong></span> and change the default value <code class="literal">12.0</code> to <code class="literal">32.0</code>.</li><li class="listitem">Print the surface test model.</li><li class="listitem">Go back and change <span class="strong"><strong>Extruder Retraction Speed (mm/s)</strong></span> to <code class="literal">85.0</code>.</li><li class="listitem">Print the surface test model.</li><li class="listitem">Keep <span class="strong"><strong>Extruder Retraction Speed (mm/s)</strong></span> at <code class="literal">85.0</code> and scroll down to <span class="strong"><strong>Retraction Distance (millimeters)</strong></span> to change the default value from <code class="literal">0.0</code> to <code class="literal">1.2</code>.</li><li class="listitem">Print the surface test model.</li><li class="listitem">Go back and change <span class="strong"><strong>Retraction Distance (millimeters)</strong></span> to <code class="literal">2.4</code>.</li><li class="listitem">Print the surface test model.</li><li class="listitem">Go back and change <span class="strong"><strong>Retraction Distance (millimeters)</strong></span> to <code class="literal">4.8</code>.</li><li class="listitem">Print the surface test model.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec242"/>How it works...</h2></div></div></div><p>Ooze and stringing isn't the only cause of unwanted texture on a model; another cause is created by the tool path of the hot end. For each section on each layer, the hot end will begin and end from the same point before it travels to the next section of the model. When this happens, there'll be a minimal discharge of the filament; this will create a buildup of unwanted material. The best method for reducing this discharge is to instruct the extruder to pull<a id="id538" class="indexterm"/> back a specified amount of <a id="id539" class="indexterm"/>filament when the hot end reaches these points of change. This is called retraction.</p><p>At the beginning of the recipe, we made adjustments to the retraction speed. As we can see in the following image, there was very little difference between the speed changes:</p><div class="mediaobject"><img src="graphics/9888OS_07_35.jpg" alt="How it works..."/></div><p>In the latter half of the recipe, we made changes to the retraction distance of the filament, keeping the highest retraction speed, which we made earlier. We can see how this improved the ridge created by the buildup from the following image:</p><div class="mediaobject"><img src="graphics/9888OS_07_36.jpg" alt="How it works..."/></div><p>Even though there seemed to<a id="id540" class="indexterm"/> be no visible effect on the model's<a id="id541" class="indexterm"/> surface when we adjusted the retraction travel speed, we can see a dramatic affect with the next image:</p><div class="mediaobject"><img src="graphics/9888OS_07_37.jpg" alt="How it works..."/></div><p>When the travel speed was returned back to the default setting of 16 mm/s and the model was printed with only the adjusted retraction distances, the model printed with severe problems. It is really important to remember that most of the controls in Skeinforge are affected by each other. As a result of this, it is always best to make note of all the default values and the changes made.</p></div></div></body></html>
