<div id="content-data-weblinks"></div>










<div id="additional-refs" class="additional-ref-section" style="display: none"></div>

<div class="web-url">
    <div class="modal" id="addWeburl"  data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Add Reference</h4>
                </div>
                <div class="modal-body">
                    <form id='webForm'>
                        <input type="text" class="web-url" name="link" id="userWebLink" placeholder="Enter URL here">
                        <input type="text" class="web-url" name="resourceName" id="userWeblinkName" placeholder="Enter name">
                    </form>
                </div>
                <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="userWeblinkUploadAlert">
                    ** Enter both link and the name
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" onclick="closeAddLink()">CANCEL</button>
                    <button type="button" class="btn " onclick="addWebLink();">ADD</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

    function addWebLink(){
        $("#userWeblinkUploadAlert").hide(500);
        if(document.getElementById("userWeblinkName").value==""||document.getElementById("userWebLink").value==""){
            $("#userWeblinkUploadAlert").show(500);
        }
        else {
            linkName= document.getElementById("userWeblinkName").value;
            link = document.getElementById("userWebLink").value;
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="resourceCreator" action="addlink" params="'from=app&chapterId='+previousChapterId+'&link='+link+'&resourceName='+linkName+'&resourceType=Reference Web Links'" onSuccess='weblinkUpdated(data);'/>
        }
    }

    function weblinkUpdated(data){
        var imgSrc = "/assets/wonderslate/weblink.png";
        additional.innerHTML = "<div class='additional-ref-wrapper'>" +
            "<a href='"+link+"' target='_blank'>" +
            "<div class='additional-ref-item'>" +
            "<div class='additional-ref-img-wrapper'>" +
            "<img src='"+imgSrc+"' class='additional-ref-img' alt=''/>" +
            "</div>" +
            "<div class='additional-ref-info'>" +
            "<p class='additional-ref-name'>"+linkName+"</p>" +
            "</div>" +
            "</div>" +
            "</a>" +
            "</div>"+additional.innerHTML;
         $('#addWeburl').modal('hide');
        $('.loading-icon').addClass('hidden');
         $("#withnoweblinks").hide();
        $("#additional-refs").show();
        $("#addRefButton").show();
        $('#webForm')[0].reset();
        updateAllTabWithWebLink(data.resId,link,linkName)
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }

    function updateAllTabWithWebLink(id,link,linkName){
        var cStr = "<div class=\"container\">\n" +
            "<div class=\"all-container\">";
        cStr +="<div class=\"container-wrapper\">\n" +
            "<div class='d-flex justify-content-between align-items-center'>"+
            "<p class='quiz-number'></p>";
        cStr +="</div>";
        cStr += "        <div class=\"media\">\n" ;
        cStr +="            <i class=\"align-self-center material-icons blue\">\n" +
            "                link\n" +
            "            </i>\n";
        cStr += "            <div class=\"media-body\">\n" +
            "                <span class=\"date mt-0\">Added by you now</span>\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" +
            "                <div class=\"d-flex align-items-center justify-content-between\">\n" ;
        cStr += "                    <a class=\"mb-0 readnow\" href='javascript:openWebRef(" + id + ",\""+link+"\")'>Open</a>\n";

        cStr += "                </div>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n";
        cStr +=     "        </div>\n" +
            "      </div>" ;
        document.getElementById('content-data-all').innerHTML += cStr;
    }

    function googleSearch(tag){
        var searchString;
        if("School"==chapterLevel) searchString = " https://www.google.com/search?q=Class "+chapterGrade+" "+chapterSubject+" "+chapterName+" "+chapterSyllabus+" "+tag;
        else searchString = " https://www.google.com/search?q="+chapterGrade+" "+chapterSubject+" "+chapterName+" "+chapterSyllabus+" "+tag;
        if(chapterDesc!=null&&!chapterDesc=="") {
            searchString = " https://www.google.com/search?q="+chapterDesc;
        }
        window.open(searchString);
        if(loggedInUser){
            if(allTabMode)
                updateUserViewChapter(previousChapterId,"all","weblinks",tag);
            else
                updateUserViewChapter(previousChapterId,"weblinks","weblinks",tag);
        }
        else{
            if(allTabMode)
                updateViewChapter(previousChapterId,"all","weblinks",tag);
            else
                updateViewChapter(previousChapterId,"weblinks","weblinks",tag);
        }

    }

    function closeAddLink(){
        $('#addWeburl').modal('hide');
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }
</script>

