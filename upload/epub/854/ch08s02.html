<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Using Repetier-Host for analysis</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Using Repetier-Host for analysis"><div class="titlepage"><div><div><h1 class="title"><a id="ch08lvl1sec106"/>Using Repetier-Host for analysis</h1></div></div></div><p>Repetier-Host is <a id="id563" class="indexterm"/>a popular host software package which contains a lot of useful features. The<a id="id564" class="indexterm"/> object analysis tool is one of these features. Not only does Repetier-Host analyze the imported model, but it will attempt to make basic repairs.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip13"/>Tip</h3><p>More information about Repetier-Host can be found in the <span class="emphasis"><em>Preface</em></span>.</p></div></div><p>In this recipe, we'll use this program to analyze a model saved in both <code class="literal">.obj</code> and <code class="literal">.stl</code> formats.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec255"/>Getting ready</h2></div></div></div><p>You'll need the <code class="literal">.stl</code> and <code class="literal">.obj</code> files of the model that we made in <a class="link" href="ch06.html" title="Chapter&#xA0;6.&#xA0;Making the Impossible">Chapter 6</a>, <span class="emphasis"><em>Making the Impossible</em></span>. It looks similar to the one shown in the following image. From this point on, let's refer to it as the capsule.</p><div class="mediaobject"><img src="graphics/9888OS_08_07.jpg" alt="Getting ready"/></div><p>You'll also need to<a id="id565" class="indexterm"/> modify it with TopMod for a second model. Do this by selecting the pentagon face as shown in the following image on the left-hand side and extruding it with <span class="strong"><strong>Doo Sabin Extrude Mode</strong></span> with a<span class="strong"><strong> Length</strong></span> of <code class="literal">-4.000</code>:</p><div class="mediaobject"><img src="graphics/9888OS_08_08.jpg" alt="Getting ready"/></div><p>The finished result should look like the preceding image on the right-hand side. We'll refer to it as the <a id="id566" class="indexterm"/>extended capsule. Save it as a <code class="literal">.stl</code> file.</p><p>Open Repetier-Host. There's a window with a build plate on the left-hand side and a series of tabs on the right-hand side. Make sure the <span class="strong"><strong>Object Placement</strong></span> tab is selected.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec256"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Take the capsule <code class="literal">.stl</code> file and drop it on the build platform. Let's look at the compiled information displayed on the right-hand side under <span class="strong"><strong>Object Analysis</strong></span>. Take note of all the information here. It should look similar to the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_08_09.jpg" alt="How to do it..."/></div></li><li class="listitem">We'll take a look at how Repetier-Host modified the model (if any modifications were made) by exporting a <code class="literal">.stl</code> file. Do this by selecting the disc icon as shown<a id="id567" class="indexterm"/> circled in the following screenshot. After exporting the model, clear the build platform.<div class="mediaobject"><img src="graphics/9888OS_08_10.jpg" alt="How to do it..."/></div></li><li class="listitem">Left-click on the <span class="strong"><strong>Deep Analysis</strong></span> button. The result should look like the one shown in the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_08_11.jpg" alt="How to do it..."/></div></li><li class="listitem">Export the <code class="literal">.stl</code> file and clear the build platform.</li><li class="listitem">Now, we'll test the capsule <code class="literal">.obj</code> file. Drop the file on the build platform. The analysis that<a id="id568" class="indexterm"/> you see should be like the one in the following screenshot. Export the model as a <code class="literal">.obj</code> file.<div class="mediaobject"><img src="graphics/9888OS_08_12.jpg" alt="How to do it..."/></div></li><li class="listitem">Now, left-click on the <span class="strong"><strong>Deep Analysis</strong></span> button. The results should be similar to the following screenshot. Export the model as a <code class="literal">.obj</code> file.<div class="mediaobject"><img src="graphics/9888OS_08_13.jpg" alt="How to do it..."/></div></li><li class="listitem">Repeat the analysis with the extended capsule <code class="literal">.stl</code> file.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec257"/>How it works...</h2></div></div></div><p>The first test, utilizing the capsule model in <code class="literal">.stl</code> format, tested manifold and ready for printing. After a deep analysis was executed, there was an indication of intersecting triangles.</p><p>Repetier-Host gave an initial analysis that the capsule model in the <code class="literal">.obj</code> file format has many mesh errors. More issues surfaced after a deep analysis. This can be seen in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_08_14.jpg" alt="How it works..."/></div><p>The image on the left-hand side is the capsule model in the <code class="literal">.obj</code> file format as seen in MeshLab before it was imported into Repetier-Host. In the center, there is the model after loading it in Repetier-Host. Automatic <a id="id569" class="indexterm"/>repairs were performed, but the results were worsened. A deep analysis was made with the model, as seen on the right-hand side. The results are even worse. The faces have collapsed onto each other.</p><p>Repetier-Host provides a good analysis of a mesh, but it's not very competent in making repairs. We'll learn how to solve these issues in the next recipe.</p></div></div></body></html>
