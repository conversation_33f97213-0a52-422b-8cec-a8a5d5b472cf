/*common*/
@import "reboot.less";
@import "variables/ws_common.less";
@import "variables/header.less";
@import "variables/userprofile/userprofile.less";
@import "variables/aboutus.less";
@import "variables/footer-nav.less";
@import "variables/footer.less";

/*homepage*/
@import "variables/signup.less";
@import "variables/banner.less";
@import "variables/slider.less";
@import "variables/otp.less";
@import "variables/upcomingBlog.less";
//@import "variables/popularBook.less";

//@import "variables/utkarsh/utkarsh.less";

/*store*/

@import "variables/store/store.less";

/*Library*/

@import "variables/library/library.less";

///*Testgenerator*/
//
//@import "variables/testgenerator/testgenerator.less";


/*admin*/

@import "variables/admin/admin.less";

/*Testgenerators*/
@import "variables/testgenerators/testgenerators.less";

/* STORE 1 */
@import "categories/categories.less";

@import "adminws/bookcreate.less";

@import "adminws/mcqcreate.less";


/* Override Styles */
.blackspine {
  .categories h2 {
    color: #444444;
  }
  .categories1 a.nav-link {
    background-color: #db9696;
    i {
      width: 25px;
    }
  }
  .footer-nav .container a:hover,
  .footer-nav .social-icons li i:hover {
    color: #ef4545;
  }
  .videoPlays{
    display: none;
  }
  .banner_slider {
    img {
      width: 100%;
    }
    .slick-dots {
      li {
        height: 8px;
        button {
          background-color: transparent;
          height: 8px;
          padding: 3px;
          border-radius: 50px;
          border: 1px solid #CCC;
          &:before {
            display: none;
          }
        }
        &.slick-active {
          button {
            background-color: #8C1515;
            border-color: #8C1515;
          }
        }
      }
    }
    .slick-next {
      right: 0;
      z-index: 99;
      border-radius: 4px 0 0 4px;
      &:hover {
        opacity: 1;
        background: #ffffff;
      }
      &:focus {
        opacity: 1;
        background: #ffffff;
      }
    }
    .slick-prev {
      left: 0 !important;
      z-index: 99;
      border-radius: 0 4px 4px 0;
      &:hover {
        opacity: 1;
        background: #ffffff;
      }
      &:focus {
        opacity: 1;
        background: #ffffff;
      }
    }
  }

  //Purchase order success page
  .purchase-details-container {
    //min-height: calc(100vh - 165px);
    .purchase-details-wrapper {
      margin: 40px auto;
      .purchase-details {
        float: left;
        width: 50%;
        margin-right: 82px;
        @media screen and (max-width: 1024px) {
          width: 100%;
        }
        .purchase-heading {
          font-weight: 500;
          font-size: 24px;
        }
        .purchase-success-confirmation {
          font-size: 16px;
          margin-bottom: 7px;
        }
        .purchase-success-id {
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0.01em;
          margin-bottom: 16px;
        }
        .purchased-book-container {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          justify-content: start;
          align-items: start;
          .purchased-book-wrapper {
            width: 100%;
            border-radius: 6px;
            background-color: #FFFFFF;
            padding: 8px;
            border: 1px solid rgba(68, 68, 68, 0.24);
            .purchased-book-img-wrapper {
              display: inline-block;
              img {
                width: 111px;
                box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
                border-radius: 4px;
              }
            }
            .purchased-book-info {
              max-width: 361px;
              display: inline-block;
              vertical-align: top;
              padding: 16px;
              max-height: inherit;
              text-align: left;
              .purchased-book-name {
                font-style: normal;
                font-weight: 500;
                line-height: 21px;
                font-size: 16px;
                letter-spacing: 0.01em;
                margin-bottom: 7px;
              }
              .detail-book-author-name {
                text-align: left;
                margin-bottom: 7px;
              }
              .offer-price {
                display: inline-block;
                font-size: 20px;
                font-weight: 500;
                color: #AE2B24;
                letter-spacing: 0.01em;
                margin-right: 4px;
                margin-bottom: 7px;
                i {
                  font-size: 18px;
                }
              }
              .original-price {
                display: inline-block;
                font-size: 16px;
                font-weight: 300;
                color: #909090;
                letter-spacing: 0.01em;
                text-decoration: line-through;
                margin-bottom: 7px;
              }
            }
          }
        }
      }
      .browse-purchase-book {
        float: left;
        width: 40%;
        border-left: 1px solid rgba(68, 68, 68, 0.24);
        @media screen and (max-width: 1024px) {
          width: 100%;
          border: 0;
        }
        .browse-wrapper {
          padding: 24px 40px;
          margin: 0 auto;
          .waves-effect {
            position: relative;
            cursor: pointer;
            display: inline-block;
            overflow: hidden;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            vertical-align: middle;
            z-index: 1;
            transition: .3s ease-out;
          }
          .learn-btn {
            font-size: 14px;
            display: block;
            text-align: center;
            font-weight: 500;
            color: #FFFFFF;
            background: linear-gradient(270deg, @ws-lightOrange 0%, #db6a6a 100%);
            letter-spacing: 0.01em;
            padding: 11px 25px;
            border-radius: 4px;
            margin-bottom: 24px;
            margin-top: -10px;
            &:hover {
              text-decoration: none;
              color: #FFFFFF;
              box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
            }
          }
          .continue-browse {
            display: block;
            font-style: normal;
            font-weight: 300;
            line-height: normal;
            font-size: 14px;
            text-align: center;
            letter-spacing: 0.01em;
            color: @ws-lightOrange;
            padding: 24px 0;
            margin-bottom: 24px;
            border-top: 1px solid rgba(68, 68, 68, 0.24);
            border-bottom: 1px solid rgba(68, 68, 68, 0.24);
          }
          .read-on-app {
            font-weight: normal;
            line-height: normal;
            font-size: 12px;
            text-align: center;
            letter-spacing: -0.01em;
            color: rgba(68, 68, 68, 0.84);
          }
          .download-app-btn {
            display: block;
            text-align: center;
            max-width: 122px;
            height: 40px;
            margin: 0 auto;
            .download-app-btn-img {
              width: 100%;
              height: auto;
              margin: 0 auto;
            }
          }
        }
      }
    }
  }
}

.blackspine .store .quick-sortmenu {
  position: relative !important;
  z-index: 99 !important;
}
.saveCard .saveSubmit{
  background: @ws-lightOrange !important;
  color:#fff;
}