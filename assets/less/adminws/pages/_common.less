// Responsive Media Query Variables
@extraSmallDevices: ~"(max-width: 575.98px)";
@smallDevices: ~"(min-width: 576px) and (max-width: 767.98px)";
@mediumDevices: ~"(min-width: 768px) and (max-width: 991.98px)";
@largeDevices: ~"(min-width: 992px) and (max-width: 1199.98px)";
@extraLargeDevices: ~"(min-width: 1200px)";

// Colors Variables
@white: #FFFFFF;
@black: #212121;
@red: #FF4B33;
@green: #27AE60;
@blue: #2F80ED;
@teal: #20C997;
@gray: #6C757D;
@orange: #F79420;

// Button Colors
@success-btn: #27AE60;
@danger-btn: #FF4B33;
@warning-btn: #FFD602;
@secondary-btn: #8E8E8E;

// Font Weight
@thin: 100;
@extra-light: 200;
@light: 300;
@regular: 400;
@medium: 500;
@semi-bold: 600;
@bold: 700;
@extra-bold: 800;
@heavy-bold: 900;

// Modal Zoom Animation Style
.modal.fade {
  .modal-dialog.modal-dialog-zoom {
    -webkit-transform: translate(0,0)scale(.5);
    transform: translate(0,0)scale(.5);
  }
}
.modal.show {
  .modal-dialog.modal-dialog-zoom {
    -webkit-transform: translate(0,0)scale(1);
    transform: translate(0,0)scale(1);
  }
}
.modal-modifier {
  .modal-header-modifier {
    border: none;
  }
  .modal-body-modifier {
    h1,h2,h3,h4,h5 {
      color: @black;
      font-weight: @medium;
      font-size: 18px;
      line-height: normal;
      @media @extraSmallDevices, @smallDevices {
        font-size: 16px;
      }
    }
    p {
      color: @gray;
      strong {
        font-weight: @semi-bold;
      }
      @media @extraSmallDevices, @smallDevices {
        font-size: 13px;
        br {
          //display: none;
        }
      }
    }
  }
  .modal-header-modifier {
    .close {
      margin: 0;
    }
  }
  .close {
    position: absolute;
    right: 15px;
    top: 5px;
    padding: 0;
    font-weight: @thin;
    font-size: 30px;
    opacity: 1;
    z-index: 10;
    color: @black !important;
    &:focus, &:active {
      outline: 0;
      box-shadow: none;
    }
  }
  .btn, a.btn {
    font-size: 14px;
  }
  .modal-content-modifier {
    border-radius: 10px;
  }
  @media @extraSmallDevices, @smallDevices {
    .modal-dialog-modifier {
      align-items: flex-end;
      padding-bottom: 0;
      max-width: 100%;
      margin: 0;
      min-height: 100%;
    }
    .modal-content-modifier {
      border-radius: 20px 20px 0 0;
    }
  }
}

// Buttons
.modal-modifier {
  .btn-shadow {
    box-shadow: 0 2px 4px #0000001A;
    -webkit-box-shadow: 0 2px 4px #0000001A;
    -moz-box-shadow: 0 2px 4px #0000001A;
  }
  .btn-outline-secondary {
    color: @secondary-btn;
    border-color: @secondary-btn;
    &:hover {
      color: @secondary-btn;
      background-color: transparent;
      border-color: @secondary-btn;
    }
    &:not(:disabled):not(.disabled) {
      &:active, &:active:focus {
        color: @secondary-btn;
        background-color: transparent;
        border-color: @secondary-btn;
        box-shadow: none;
      }
    }
    &:focus:not(:active) {
      background-color: transparent;
    }
  }
  .btn-success {
    background-color: @success-btn;
    border-color: @success-btn;
    &:hover {
      background-color: @success-btn;
      border-color: @success-btn;
    }
    &:not(:disabled):not(.disabled) {
      &:active, &:active:focus {
        background-color: @success-btn;
        border-color: @success-btn;
        box-shadow: none;
      }
    }
    &.disabled, &:disabled {
      background-color: @success-btn !important;
      color: @white !important;
      border-color: @success-btn;
    }
    &:focus:not(:active) {
      background-color: @success-btn;
    }
  }
  .btn-danger {
    background-color: @danger-btn;
    border-color: @danger-btn;
    &:hover {
      background-color: @danger-btn;
      border-color: @danger-btn;
    }
    &:not(:disabled):not(.disabled) {
      &:active, &:active:focus {
        background-color: @danger-btn;
        border-color: @danger-btn;
        box-shadow: none;
      }
    }
    &.disabled, &:disabled {
      background-color: @danger-btn;
      border-color: @danger-btn;
    }
    &:focus:not(:active) {
      background-color: @danger-btn;
    }
  }
}

// Data Tables
.dataTables_wrapper {
  width: 100%;
  .page-item.active .page-link {
    color: @white !important;
    background-color: @blue !important;
  }
  input.form-control, select.form-control {
    height: auto !important;
  }
}
.dataTables_wrapper > .row:nth-child(2){
  overflow-x:scroll;
}
table.dataTable {
  width:100% !important;
  thead th {
    white-space: nowrap !important;
    &:first-child {
      width:10% !important;
    }
    &:nth-child(2) {
      width:50% !important;
    }
  }
  td {
    white-space: normal !important;
  }
}
table.dataTable.table-sm .sorting:before, table.dataTable.table-sm .sorting_asc:before, table.dataTable.table-sm .sorting_desc:before {
  right: 1.20em;
  font-size: 14px;
}
table.dataTable.table-sm .sorting:after, table.dataTable.table-sm .sorting_asc:after, table.dataTable.table-sm .sorting_desc:after {
  font-size: 14px;
}

// Common Styles
.loading-icon {
  z-index: 999999;
}
.modal {
  z-index: 9992;
}
input.form-control,select.form-control {
  height: 35px !important;
}
.input-error {
  border-color: @red !important;
}
.input-success {
  border-color: @green !important;
}
.bootstrap-select {
  width: 100% !important;
  .dropdown-toggle {
    font-size: 14px;
    box-shadow: 0 2px 4px #ECECFB;
    background-color: @white !important;
    color: @black !important;
    border-color: #B4CDDE !important;
    height: 35px;
    &:hover,&:focus {
      color: @black !important;
      outline: 0 !important;
    }
    &::after {
      vertical-align: 0.5em !important;
    }
  }
  .filter-option {
    font-weight: normal;
    padding-left: 4px;
  }
  .bs-searchbox {
    input.form-control {
      height: 30px !important;
      background-color: #f9f9f9;
    }
  }
  .dropdown-menu {
    &.open {
      background-color: #e6eff4;
    }
    .inner {
      background-color: #e6eff4;
      padding: 4px 8px !important;
      max-height: 300px !important;
    }
    li {
      &.no-results {
        border-radius: 3px;
        padding: 3px 10px;
        margin: 0;
        font-size: 13px;
      }
      a {
        border-radius: 3px;
        margin-bottom: 2px;
        padding: 3px 10px;
        font-size: 13px;
        &:hover {
          background-color: #20c99740;
        }
      }
      &.selected {
        a {
          color: @white;
          font-weight: 600;
          background-color: @teal;
          border-radius: 3px;
          &:hover {
            background-color: @red;
          }
        }
      }
    }
  }
}
.readonly-datepicker {
  border-color: #B4CDDE !important;
}

