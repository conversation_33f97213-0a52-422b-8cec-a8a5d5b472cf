<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<body>
<div class="container"><br>
    <div class="d-flex justify-content-between align-items-center">
        <button onclick="window.history.back();" class="btn btn-secondary">Back</button>
        <h2 class="text-center flex-grow-1">Assign Books to Batch</h2>
    </div>

    <h3>Batch: ${batch.name}</h3>
    <h4>Course: ${batch.courseName}</h4>
    <h4>Grade: ${batch.grade}</h4>
    <hr/>

    <g:form id="assignBooksForm" action="saveAssignedBooks" method="post">
        <input type="hidden" name="batchId" value="${batch.id}"/>
        <input type="hidden" name="instituteId" value="${instituteId}"/>

        <!-- Search Box -->
        <div class="form-group">
            <label for="search">Search Books:</label>
            <input type="text" id="search" name="search" value="${search}" class="form-control" placeholder="Enter book title"/>
        </div>

        <!-- Book List -->
        <div id="bookList">
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Select</th>
                    <th>Book ID</th>
                    <th>Title</th>
                </tr>
                </thead>
                <tbody>
                <g:each in="${books}" var="book">
                    <tr>
                        <td>
                            <input type="checkbox" name="bookIds" value="${book.id}"/>
                        </td>
                        <td>${book.id}</td>
                        <td>${book.title}</td>
                    </tr>
                </g:each>
                </tbody>
            </table>
            <!-- Pagination Controls -->
            <g:paginate total="${totalCount}" max="${max}" offset="${offset}" params="${params}"/>
        </div>

        <!-- Submit Button -->
        <div class="form-group text-center">
            <button type="submit" class="btn btn-success">Add Books</button>
            <g:link action="getBatch" params="[id: batch.id]" class="btn btn-default">Cancel</g:link>
        </div>
    </g:form>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script type="text/javascript">
    $(document).ready(function() {
        var selectedBookIds = [];

        // Remember selected checkboxes
        $(document).on('change', 'input[name="bookIds"]', function() {
            var bookId = $(this).val();
            if ($(this).is(':checked')) {
                if (selectedBookIds.indexOf(bookId) === -1) {
                    selectedBookIds.push(bookId);
                }
            } else {
                var index = selectedBookIds.indexOf(bookId);
                if (index !== -1) {
                    selectedBookIds.splice(index, 1);
                }
            }
        });

        // Initialize selectedBookIds
        $('input[name="bookIds"]:checked').each(function() {
            selectedBookIds.push($(this).val());
        });

        // Function to update book list
        function updateBookList() {
            var search = $('#search').val();
            $.ajax({
                url: '${createLink(action: 'searchBooksForBatch')}',
                data: {
                    batchId: '${batch.id}',
                    instituteId: '${instituteId}',
                    search: search
                },
                dataType: 'json',
                success: function(data) {
                    var html = '<table class="table table-striped"><thead><tr><th>Select</th><th>Book ID</th><th>Title</th></tr></thead><tbody>';
                    $.each(data, function(index, book) {
                        var checked = '';
                        if (selectedBookIds.indexOf(book.id.toString()) !== -1) {
                            checked = 'checked';
                        }
                        html += '<tr>';
                        html += '<td><input type="checkbox" name="bookIds" value="' + book.id + '" ' + checked + '/></td>';
                        html += '<td>' + book.id + '</td>';
                        html += '<td>' + book.title + '</td>';
                        html += '</tr>';
                    });
                    html += '</tbody></table>';
                    $('#bookList').html(html);
                }
            });
        }

        // Bind keyup event on search input
        $('#search').on('keyup', function() {
            updateBookList();
        });

        // Handle pagination links
        $(document).on('click', '.pagination a', function(e) {
            e.preventDefault();
            var url = $(this).attr('href');
            $.ajax({
                url: url,
                data: {
                    batchId: '${batch.id}',
                    instituteId: '${instituteId}',
                    search: $('#search').val()
                },
                dataType: 'json',
                success: function(data) {
                    var html = '<table class="table table-striped"><thead><tr><th>Select</th><th>Book ID</th><th>Title</th></tr></thead><tbody>';
                    $.each(data, function(index, book) {
                        var checked = '';
                        if (selectedBookIds.indexOf(book.id.toString()) !== -1) {
                            checked = 'checked';
                        }
                        html += '<tr>';
                        html += '<td><input type="checkbox" name="bookIds" value="' + book.id + '" ' + checked + '/></td>';
                        html += '<td>' + book.id + '</td>';
                        html += '<td>' + book.title + '</td>';
                        html += '</tr>';
                    });
                    html += '</tbody></table>';
                    $('#bookList').html(html);
                }
            });
        });
    });
</script>
