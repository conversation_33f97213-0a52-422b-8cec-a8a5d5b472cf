<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/assets/bookgpt/bookgpt.css">
    <script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>
    <title>BookGPT Prompt Admin</title>

    <script>
        const GPT_API_URL = "/bookgpt/api"
        let gptBookId = '${params.bookId}'
        let gptChapterId = '${params.chapterId}'
        let gptResId = '${params.resId}'
        let namespace = gptChapterId+"_"+gptResId
        function hideAppLoader(){
            const loader = document.getElementById('loader');
            loader.style.display = 'none';
        }
        function showAppLoader(sub){
            const loader = document.getElementById('loader');
            loader.style.display = 'flex';
            loader.style.flexDirection = 'column';
            if(!sub){
                document.getElementById('loaderSub').style.display='none'
            }
        }
        let chapterId = "";
        let resId=""
        let prevResId = ""

        function updateChapterDropDown(groupedData){
            let resListHTML = ""
            const dropdownButton = document.querySelector('.dropdown-button');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            const dropdownValue = document.getElementById('dropDownValue')
            const dropdownArrow = document.getElementById('dropdownArrow')

            for (const chapterId in groupedData) {
                const chapter = groupedData[chapterId];
                chapter.resources.forEach(resource=>{

                    if(resource.resType==="Notes"  && resource.link.endsWith('.pdf')){

                        if(resource.resId==gptResId){
                            resListHTML+= "<li class='dropdown-item chapterHighlight' data-chapterId='"+chapter.chapterId+"' data-resId='"+resource.resId+"'>"+resource.name+"</li>"
                        }else{
                            resListHTML+= "<li class='dropdown-item' data-chapterId='"+chapter.chapterId+"' data-resId='"+resource.resId+"'>"+resource.name+"</li>"
                        }
                    }
                })
            }
            dropdownMenu.innerHTML = resListHTML
            dropdownValue.innerHTML = document.querySelector("[data-resId='"+gptResId+"']").textContent.trim()
            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownButton.addEventListener('click', function(e) {
                e.preventDefault()
                dropdownMenu.classList.toggle('show');
                dropdownArrow.classList.toggle('rotateArrowDown')
                dropdownArrow.classList.toggle('rotateArrowUp')
            });

            dropdownItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    prevResId = gptResId
                    dropdownValue.innerHTML = this.textContent;
                    dropdownMenu.classList.remove('show');
                    dropdownArrow.classList.toggle('rotateArrowDown')
                    dropdownArrow.classList.toggle('rotateArrowUp')
                    chapterId = item.getAttribute("data-chapterId")
                    resId = item.getAttribute("data-resid")
                    dropdownItems.forEach(function(subitem) {
                        subitem.classList.remove('chapterHighlight')
                    });
                    item.classList.add('chapterHighlight')
                    if(document.getElementById('messages')){
                        document.getElementById('messages').innerHTML=""
                    }
                    if(prevResId!=resId){
                        getPDF(resId,chapterId)
                        const url = new URL(window.location.href);
                        url.searchParams.set('chapterId', chapterId);
                        url.searchParams.set('resId', resId);
                        window.history.pushState({}, '', url);
                    }
                });
            });

            window.addEventListener('click', function(e) {
                if (!dropdownButton.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                    dropdownArrow.classList.add('rotateArrowDown')
                    dropdownArrow.classList.remove('rotateArrowUp')
                }
            });
        }
    </script>
</head>
<body>
<div id="loader" class="loader">
    <dotlottie-player src="https://lottie.host/a464fcaf-6362-4ede-b647-7b48911a2375/LJJIok88LW.json" background="transparent" speed="1" style="width: 300px; height: 300px;" loop autoplay></dotlottie-player>
    <div class="introText">
        <h3 id="loaderSub" style="margin-top: 12px;">
            <span class="logo">Book<span class="logoHighlight">GPT</span>
            <img src="/assets/resource/glitter.svg" class="glitter-icon"/>
        </span>
        </h3>
    </div>
</div>
<div class="bookgpt">
    <div class="header">
        <div>
            <button class="backButton"><i class="fa-solid fa-left-long"></i></button>
        </div>
        <div class="chaptersList">
            <div class="custom-dropdown">
                <button class="dropdown-button">
                    <span id="dropDownValue">Select chapter</span>
                    <i class="fa-solid fa-angle-down rotateArrowDown" id="dropdownArrow" style="font-size: 15px;"></i>
                </button>
                <ul class="dropdown-menu">
                    <li class="dropdown-item" data-chapterId="1">Option 1</li>
                    <li class="dropdown-item" data-chapterId="2">Option 2</li>
                    <li class="dropdown-item" data-chapterId="3">Option 3</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="sections">
        <g:render template="bookgptPDFViewer"></g:render>
        <g:render template="bookgptChatViewer"></g:render>
    </div>
</div>
</body>
</html>