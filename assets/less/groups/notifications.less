@import "../wsTemplate/theme/colors.less";
@import "../wsTemplate/theme/fonts.less";
@import "../wsTemplate/theme/responsive.less";

.uimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: @red;
}
.notification-msge {
  p{
    color: lighten(@dark-gray,25%);
  }
}
.msge {
  color: @theme-primary-color;
}
.header-content  {
  h3{
    color: @theme-primary-color;
  }

}
.notification-ul {
  li{
    border-bottom: 1px solid lighten(@light-gray,25%);
  }

}
.header-wrapper {
  a{
    color: @theme-primary-color;
  }

}
.width-size{
  width: 40%;
}

@media @extraSmallDevices,@smallDevices {
  .width-size{
    width: 100%;
  }
}
