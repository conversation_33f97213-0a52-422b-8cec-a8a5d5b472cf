package com.wonderslate

import grails.test.mixin.TestFor
import spock.lang.Specification
import com.wonderslate.data.BooksMst
import grails.converters.JSON

/**
 * Test for WonderpublishController lockEditUpdate method
 */
@TestFor(WonderpublishController)
class WonderpublishControllerSpec extends Specification {

    def setup() {
    }

    def cleanup() {
    }

    void "test lockEditUpdate with valid parameters"() {
        given: "A valid book ID and lock edit parameter"
        params.bookId = "123"
        params.lockEdit = "true"
        
        and: "Mock session with required site ID and role"
        session["siteId"] = 71
        session["userdetails"] = [
            authorities: [
                [authority: "ROLE_PDF_EXTRACTOR"]
            ]
        ]
        
        and: "Mock the getSiteId method"
        controller.metaClass.getSiteId = { request -> return 71 }
        
        and: "Mock dataProviderService"
        def mockDataProviderService = Mock()
        controller.dataProviderService = mockDataProviderService
        
        and: "Mock BooksMst"
        def mockBook = new BooksMst(id: 123, title: "Test Book")
        mockDataProviderService.getBooksMst(123L) >> mockBook
        
        when: "lockEditUpdate is called"
        controller.lockEditUpdate()
        
        then: "Response should be success"
        response.contentAsString.contains('"status":"success"')
    }

    void "test lockEditUpdate with invalid site ID"() {
        given: "A valid book ID but invalid site ID"
        params.bookId = "123"
        params.lockEdit = "true"
        
        and: "Mock session with invalid site ID"
        session["siteId"] = 1
        session["userdetails"] = [
            authorities: [
                [authority: "ROLE_PDF_EXTRACTOR"]
            ]
        ]
        
        and: "Mock the getSiteId method"
        controller.metaClass.getSiteId = { request -> return 1 }
        
        when: "lockEditUpdate is called"
        controller.lockEditUpdate()
        
        then: "Response should be error"
        response.contentAsString.contains('"status":"error"')
        response.contentAsString.contains('only available for site ID 71')
    }

    void "test lockEditUpdate with invalid role"() {
        given: "A valid book ID but invalid role"
        params.bookId = "123"
        params.lockEdit = "true"
        
        and: "Mock session with valid site ID but invalid role"
        session["siteId"] = 71
        session["userdetails"] = [
            authorities: [
                [authority: "ROLE_USER"]
            ]
        ]
        
        and: "Mock the getSiteId method"
        controller.metaClass.getSiteId = { request -> return 71 }
        
        when: "lockEditUpdate is called"
        controller.lockEditUpdate()
        
        then: "Response should be error"
        response.contentAsString.contains('"status":"error"')
        response.contentAsString.contains('do not have permission')
    }

    void "test lockEditUpdate with missing book ID"() {
        given: "Missing book ID parameter"
        params.lockEdit = "true"
        
        and: "Mock session with valid site ID and role"
        session["siteId"] = 71
        session["userdetails"] = [
            authorities: [
                [authority: "ROLE_PDF_EXTRACTOR"]
            ]
        ]
        
        and: "Mock the getSiteId method"
        controller.metaClass.getSiteId = { request -> return 71 }
        
        when: "lockEditUpdate is called"
        controller.lockEditUpdate()
        
        then: "Response should be error"
        response.contentAsString.contains('"status":"error"')
        response.contentAsString.contains('Book ID is required')
    }
}
