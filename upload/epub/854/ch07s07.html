<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Making textures with MeshLab</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Making textures with MeshLab"><div class="titlepage"><div><div><h1 class="title"><a id="ch07lvl1sec96"/>Making textures with MeshLab</h1></div></div></div><p>MeshLab has a variety<a id="id527" class="indexterm"/> of options for creating random patterns and <a id="id528" class="indexterm"/>textures on a model. We're going to look briefly at a couple of these options in this recipe.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec234"/>Getting ready</h2></div></div></div><p>You'll need MeshLab open and ready.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec235"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Go to <span class="strong"><strong>Filters</strong></span> in the menu and select <span class="strong"><strong>Create New Mesh Layer</strong></span>. From the cascaded window, select <span class="strong"><strong>Sphere</strong></span>. Choose <span class="strong"><strong>Apply</strong></span> from the pop-up window and click on <span class="strong"><strong>Close</strong></span>.</li><li class="listitem">Go back to <span class="strong"><strong>Filters</strong></span> and select <span class="strong"><strong>Remeshing</strong></span>, <span class="strong"><strong>Simplification</strong></span>, and <span class="strong"><strong>Reconstruction</strong></span>. From the cascaded window, select <span class="strong"><strong>Subdivision Surfaces: LS3 Loop</strong></span>. Choose <span class="strong"><strong>Apply</strong></span> from the pop-up window and click on <span class="strong"><strong>Close</strong></span>. Save the remeshed sphere.</li><li class="listitem">Go back to <span class="strong"><strong>Filters</strong></span> and select <span class="strong"><strong>Smoothing</strong></span>, <span class="strong"><strong>Fairing</strong></span>, and <span class="strong"><strong>Deformation</strong></span>. From the cascaded window, select <span class="strong"><strong>Fractal Displacement</strong></span>. Choose <span class="strong"><strong>Apply</strong></span> from the pop-up window and click on <span class="strong"><strong>Close</strong></span>. Save the model.</li><li class="listitem">Load the remeshed sphere. Go to <span class="strong"><strong>View</strong></span> in the menu and choose <span class="strong"><strong>Show Layer Dialog</strong></span>.</li><li class="listitem">Go to <span class="strong"><strong>Filters</strong></span> and select <span class="strong"><strong>Sampling</strong></span>. From the cascaded window, choose <span class="strong"><strong>Montecarlo Sampling</strong></span>. Change the <span class="strong"><strong>Number of samples</strong></span> option to <code class="literal">10</code>. Choose <span class="strong"><strong>Apply</strong></span> and click on <span class="strong"><strong>Close</strong></span>.</li><li class="listitem">Go back to <span class="strong"><strong>Filters</strong></span> and select <span class="strong"><strong>Smoothing</strong></span>, <span class="strong"><strong>Fairing</strong></span>, and <span class="strong"><strong>Deformation</strong></span>. From the cascaded window, select <span class="strong"><strong>Craters Generation</strong></span>.</li><li class="listitem">In the pop-up window, we see <span class="strong"><strong>Montecarlo Samples</strong></span> listed in both <span class="strong"><strong>Target mesh</strong></span> and <span class="strong"><strong>Samples layer</strong></span>. Change the <span class="strong"><strong>Target mesh</strong></span> to <span class="strong"><strong>Sphere</strong></span>.</li><li class="listitem">Adjust <span class="strong"><strong>Min crater depth</strong></span> to <code class="literal">0</code> and <span class="strong"><strong>Max crater depth</strong></span> to <code class="literal">0.04</code>.</li><li class="listitem">Choose <span class="strong"><strong>Apply</strong></span> from the pop-up window and click on <span class="strong"><strong>Close</strong></span>. Save the model.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch07lvl2sec236"/>How it works...</h2></div></div></div><p>A simple method for creating a variety of textures is using fractal displacement. In the following image, we can see the results using the default settings:</p><div class="mediaobject"><img src="graphics/9888OS_07_26.jpg" alt="How it works..."/></div><p>A nice variety of effects can be made using this filter. Try experimenting with the suggested parameter values that are listed at the top of the <span class="strong"><strong>Fractal Displacement</strong></span> window.</p><p>Another method to create textures is by sampling and using the crater-generation filter. There are a variety of sampling <a id="id529" class="indexterm"/>algorithms to choose from; these sampling algorithms will create different effects. <a id="id530" class="indexterm"/>In the following image, only 10 samples were chosen in the Montecarlo sampling instead of the default 40482 samples (this value will change depending on the polygon count of the model sampled):</p><div class="mediaobject"><img src="graphics/9888OS_07_27.jpg" alt="How it works..."/></div><p>You'll gain a much better understanding of the texture possibilities if you experiment with all the parameters. There are different sampling options and many<a id="id531" class="indexterm"/> variables within the filters that<a id="id532" class="indexterm"/> will produce different effects.</p></div></div></body></html>
