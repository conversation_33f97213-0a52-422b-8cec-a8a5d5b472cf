<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn = false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class="row justify-content-center p-4">
        <div class="col-md-9 align-left main p-4">
%{--            <h4 for="publisherId">${pub.id ? "Update" : "Add"} Publisher</h4>--}%

            <form method="post" enctype="multipart/form-data" action="/publisherManagement/updatePublisher"
                  class="text-left mt-3"
                  id="publisherDetailsForm">
                <input type="text" class="form-control hidden" name="id" id="id" value="${pub.id}"/>
                <input type="text" class="form-control hidden" id="urls" value="${puburls.join('~')}"/>

                <div class="row">

                    <div class="col">
                        <label for="pubName">Name</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="name" id="pubName" value="${pub.name}"
                                   onchange="onPublisherNameChanged()"
                                   placeholder="Publisher Name"/>
                        </div>

                        <label for="pubcity">City</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="city" id="pubcity" value="${pub.city}"
                                   placeholder="City"/>
                        </div>

                        <label for="pubstate">State</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="state" id="pubstate" value="${pub.state}"
                                   placeholder="State"/>
                        </div>

                        <label for="pubcountry">Country</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="country" id="pubcountry" value="${pub.country}"
                                   placeholder="Country"/>
                        </div>

                        <label for="publogo">Logo</label>
                        <% if (pub.logo != "" && pub.logo != null) { %>
                        <div class=" input-group">
                        <img class="border rounded" style="width: 100px; height: 100px;" id="publogo"
                             src="/publisherManagement/showPublisherImage?id=${pub.id}">
                        </div>
                        <% } else { %>
                        <div class="col text-center border rounded p-4">
                            <i class="material-icons">description</i>

                            <p>Logo Not Uploaded</p>
                        </div>
                        <% } %>
                        <div class="input-group mb-3">
                            <div class="custom-file mt-1">
                                <input class="custom-file-input" id="selectFile" name="file" type="file"
                                       accept="image/png, image/jpeg, image/gif ,image/svg"
                                       onchange="updatePublisherImage()">
                                <label class="custom-file-label" id="logoFileName">Choose file</label>
                            </div>
                        </div>

                        <p id="file-error" style="font-size: 12px;
                        margin-top: 0.5rem;
                        text-align: center;">Please Upload Image below 2mb</p>

                    </div>


                    <div class="col">
                        <label for="website">Website</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="website" id="website" value="${pub.website}"
                                   placeholder="Publisher's Website URL"/>
                        </div>

                        <label for="contactPerson">Contact Person</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="contactPerson" id="contactPerson"
                                   value="${pub.contactPerson}"
                                   placeholder="Contact Person Name"/>
                        </div>

                        <label for="pubemail">Email</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="email" id="pubemail" value="${pub.email}"
                                   placeholder="Contact Person Email"/>
                        </div>

                        <label for="pubmobile">Mobile</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" oninput="numberOnly(this.id)" name="mobile" id="pubmobile" value="${pub.mobile}"
                                   placeholder="Contact Person Mobile"/>
                        </div>





                        <label for="pubtagline">Tagline</label>
                        <div class="input-group mb-3">
                            <textarea type="text" class="form-control" name="tagline" id="pubtagline" value="${pub.tagline}"
                                   placeholder="Tagline">${pub.tagline}</textarea>
                        </div>

                        <label for="pubBackgroundColor">Background Color</label>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="backgroundColor" id="pubBackgroundColor"
                                   value="${pub.backgroundColor}">
                            <div class="input-group-append">
                                <span class="input-group-text">
                                    <input type="color" id="colorPicker" onchange="selectColor()"
                                           value="${pub.backgroundColor ? pub.backgroundColor : "#ffffff"}"/>
                                </span>

                            </div>

                        </div>
                    </div>
                </div>
                <label for="urlName">Url Name</label>
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" style="">https://www.wonderslate.com/publisher/</span>
                    </div>
                    <input type="text" class="form-control" name="urlname" id="urlName" value="${pub.urlname}"
                           onchange="onUrlNameChanged()"
                           placeholder="URL Name"/>
                </div>
            <label for="urlName">Send email for eBook purchase</label>
            <div class="input-group mb-3">
                <label class="clickable-label">
                    <input type="radio" name="sendEbookEmail" value="No" <%="No".equals(pub.sendEbookEmail)?"checked":"" %>  class="radioDownload"> No
                </label>&nbsp;&nbsp;
                <label class="clickable-label">
                    <input type="radio" name="sendEbookEmail" id="pubSendEbookEmail" value="Yes" <%="Yes".equals(pub.sendEbookEmail)?"checked":"" %>   class="radioDownload"> Yes
                </label>
            </div>
            <label for="showVideoOnlyInApp">Show Video only in app</label>
            <div class="input-group mb-3">
               <select id="showVideoOnlyInApp" name="showVideoOnlyInApp"><option value="true" ${("true").equals(pub.showVideoOnlyInApp)?"selected":""}>True</option>
                   <option value="false" ${pub.showVideoOnlyInApp==null||("false").equals(pub.showVideoOnlyInApp)?"selected":""}>False</option>
               </select>
            </div>
            <label for="publisherNameForTitle">Publisher name for book title</label>
            <div class="input-group mb-3">
                <input type="text" class="form-control"  name="publisherNameForTitle" id="publisherNameForTitle" value="${pub.publisherNameForTitle}"
                       placeholder="Name for book title"/>
            </div>
            <div class="row">
                <div class="form-group col mb-3">
                    <label for="siteTitle">Site Title</label>
                    <textarea class="form-control" rows="2" name="siteTitle" id="siteTitle" autocomplete="off">${pub != null ? pub.siteTitle : ""}</textarea>
                </div>
            </div>
            <div class="row">
                <div class="form-group col mb-3">
                    <label for="siteDescription">Site Description</label>
                    <textarea class="form-control" rows="4" name="siteDescription" id="siteDescription" autocomplete="off">${pub != null ? pub.siteDescription : ""}</textarea>
                </div>
            </div>
            <div class="row">
                <div class="form-group col mb-3">
                    <label for="keywords">Site Keywords</label>
                    <textarea class="form-control" rows="4" name="keywords" id="keywords" autocomplete="off">${pub != null ? pub.keywords : ""}</textarea>
                </div>
            </div>
            <div class="row">
                <div class="form-group  col mb-3">
                    <label for="printBookShare">Print book share</label>
                    <input type="number" name="printBookShare" class="form-control" id="printBookShare" value="${pub!=null?pub.printBookShare:""}">
                </div>
                <div class="form-group  col mb-3">
                    <label for="ebookShareOnWS">eBook share WS</label>
                    <input type="number" name="ebookShareOnWS" class="form-control" id="ebookShareOnWS" value="${pub!=null?pub.ebookShareOnWS:""}">
                </div>
                <div class="form-group  col mb-3">
                    <label for="ebookShareOnWhitelabel">eBook share Whitelabel</label>
                    <input type="number" name="ebookShareOnWhitelabel" class="form-control" id="ebookShareOnWhitelabel" value="${pub!=null?pub.ebookShareOnWhitelabel:""}">
                </div>
            </div>

            </form>

            <button id="updatePub" class="btn btn-primary"
                    onclick="updatePublisherr()">${pub.id ? "Update" : "Add"}</button>

            <% if (pub.id) { %>
            <div>
                <h3 class="mt-4">Banner Management</h3>
            <p>Please add all the banners of same size. (Recommended size: 2050px * 780px)</p>
                <% for (int i = 0; i < 6; i++) { %>

                <form class="form-horizontal mt-4" enctype="multipart/form-data" role="form"
                      name="uploadbanner${(i + 1)}"
                      id="uploadbanner${(i + 1)}" action="/pubdesk/addBanners"
                      onsubmit="return addBanner(${(i+1)})"
                      method="post">
                    <h5><strong>Banner ${(i + 1)}</strong></h5>
                    <input type="hidden" name="bannerId" value="${bannersMstList[i] ? bannersMstList[i].id : ""}">
                    <input type="hidden" name="pubId" value="${pub.id}">
                    <div class="row add_banners">

                        <div class="form-group col mb-3">
                            <label for="name${(i + 1)}">Name</label>
                            <input type="text" class="form-control" name="name" id="name${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].imageName : ""}"
                                   autocomplete="off">
                        </div>
                        <div class="form-group col mb-3">
                            <label for="bookId${(i + 1)}">Book Id</label>
                            <input type="text" class="form-control" name="bookId" id="bookId${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].bookId : ""}"
                                   autocomplete="off" onblur="checkBookId(this.value, ${(i+1)})">
                        </div>
                        <div class="form-group col mb-3">
                            <label for="imageDesc${(i + 1)}">Desktop</label>
                            <input type="file" name="file" class="form-control"
                                   id="imageDesc${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" accept="image/*">
                        </div>
                        <div class="form-group col mb-3">
                            <label for="imageMob${(i + 1)}">Mobile</label>
                            <input type="file" name="filesss" class="form-control"
                                   id="imageMob${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}" accept="image/*">
                        </div>

                    </div>

                    <div class="row align-items-center banner_details">
                        <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                            <input type="text" class="form-control form-control-sm" name="namename" id="namename${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" readonly="true" autocomplete="off">
                        </div>
                        <% if (bannersMstList[i] != null && bannersMstList[i].imagePath) { %>
                        <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                            <a class="p-0" href="/wonderpublish/showImage?id=${bannersMstList[i].id}&fromPub=true&fileName=${bannersMstList[i] ? bannersMstList[i].imagePath : ""}">View Desktop Image</a>
                        </div>
                        <% } %>
                    </div>

                    <div class="row align-items-center banner_details">
                        <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                            <input type="text" class="form-control form-control-sm" name="namenamemobile" id="namenamemobile${(i + 1)}"
                                   value="${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}" readonly="true" autocomplete="off">
                        </div>
                        <% if (bannersMstList[i] != null && bannersMstList[i].imagePathMobile) { %>
                        <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                            <a class="p-0" href="/wonderpublish/showImage?id=${bannersMstList[i].id}&fromPub=true&fileName=${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}">View Mobile Image</a>
                        </div>
                        <% }%>
                    </div>

                    <% if (bannersMstList[i] != null) { %>
                    <div class="d-flex">
                        <button class="btn btn-success btn-success-modifier mb-2 mr-2 px-4" type="submit">Update</button>
                        <button class="btn btn-outline-secondary btn-outline-secondary-modifier mb-2 px-3" type="button"
                                onclick="deleteId(${bannersMstList[i].id});">Delete</button>
                    </div>
                    <% } else { %>
                    <button type="submit" class="btn btn-primary btn-primary-modifier mb-2 px-5">Add</button>
                    <% } %>
                </form>

                <% } %>
            </div>
            <% } %>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var urls = document.getElementById('urls').value.split('~')
    if (document.getElementById("id").value) {
        var index = urls.indexOf(document.getElementById("urlName").value)
        urls.splice(index, 1);
        document.getElementById("urls").value = urls.join('~')

    }
    if (!document.getElementById('urlName').value) {
        document.getElementById("urlName").value =
            document
                .getElementById("pubName").value
                .toString()
                .toLowerCase()
                .replace(/ /g, "-")
                .replace(/\./g, "-")
    }

    function updatePublisherr() {
        onUrlNameChanged()
        var id = document.getElementById("id").value.toString().trim()
        var name = document.getElementById("pubName").value.toString().trim()
        var website = document.getElementById("website").value.toString().trim()
        var contactPerson = document.getElementById("contactPerson").value.toString().trim()
        var email = document.getElementById("pubemail").value.toString().trim()
        var mobile = document.getElementById("pubmobile").value.toString().trim()
        var urlname = document.getElementById("urlName").value.toString().trim()
        var city = document.getElementById("pubcity").value.toString().trim()
        var state = document.getElementById("pubstate").value.toString().trim()
        var country = document.getElementById("pubcountry").value.toString().trim()
        var tagline = document.getElementById("pubtagline").value.toString().trim()
        var backgroundColor = document.getElementById("pubBackgroundColor").value.toString().trim()
        if (name == "") {
            alert("Publisher Name cannot be empty.")
            return
        }
        if (urlname == "" || urlname.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("Publisher Url must be all small characters and must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            return
        }

        if (mobile.length > 0 && mobile.length != 10 ) {
            alert('Enter Valid mobile')
            document.getElementById("pubmobile").value = ""
            return
        }
        var webreg = /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/ig
        if (website.length > 0 && !webreg.test(website)) {
            alert('Enter Valid Website')
            document.getElementById("website").value = ""
            return
        }
        $('.loading-icon').removeClass('hidden');
        document.getElementById('publisherDetailsForm').submit();
    }

    const onPublisherNameChanged = () => {
        changed = true
        // document.getElementById("urlName").value==""
        if (true) {
            document.getElementById("urlName").value =
                document
                    .getElementById("pubName").value
                    .toString()
                    .toLowerCase()
                    .replace(/ /g, "-")
                    .replace(/\./g, "-")
            onUrlNameChanged()
        }
    }
    const onUrlNameChanged = () => {
        document.getElementById('urlName').value = document.getElementById('urlName').value.toLowerCase().replace(/ /g, "-").replace(/\./g, "-")
        document.getElementById('urls').value.split("~").forEach((u, i) => {
            if (u == document.getElementById('urlName').value) {
                alert("Url Not Available")
                document.getElementById('urlName').value = ""
            }
        })
    }

    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }

    function updatePublisherImage() {
        var oFile = document.getElementById("selectFile").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if (oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            document.getElementById("selectFile").value = ""
            return
        }

        if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color', 'red');
            return;
        }
        document.getElementById("logoFileName").innerText = oFile.name
    }

    function addBanner(index) {
        var selectedDescFile = document.getElementById("imageDesc"+index).files[0];
        var selectedMobFile = document.getElementById("imageMob"+index).files[0];
        if (document.getElementById("name" + index).value == "") {
            alert("Please enter the name.");
            return false;
        } else if(document.getElementById("imageDesc"+index).value=="" && document.getElementById("namename"+index).value=="" && document.getElementById("imageMob"+index).value!="") {
            alert("Desktop images mandatory for mobile images.");
            return false;
        } else if(document.getElementById("imageDesc"+index).value=="" && document.getElementById("namename"+index).value=="") {
            alert("Please choose images.");
            return false;
        } else if((selectedDescFile !=undefined && selectedDescFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) || (selectedMobFile !=undefined && selectedMobFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1)){
            alert("File name must not contain any space and special characters.");
            return false;
        } else if((selectedDescFile !=undefined && selectedDescFile.size>=153600) || (selectedMobFile !=undefined && selectedMobFile.size>=153600)){
            alert("File size should be below 150kb.");
            return false;
        }else {
            $('.loading-icon').removeClass('hidden');
            alert("Saved successfully!");
        }
    }

    function deleteId(id) {
        let confirmAction = confirm("Are you sure to delete this banner?");
        if(confirmAction) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="pubdesk" action="deleteBannerById" params="'id='+id" onSuccess = "bannerDeleted(data);"/>
        }
    }

    function bannerDeleted(data) {
        if (data.status == "OK") {
            alert("Deleted successfully!");
            location.reload();
        }
    }

    function openColorPicker() {
        // document.getElementById('colorPicker').removeClass('hidden')
        $('#colorPicker').trigger('click')
    }

    function selectColor() {
        document.getElementById('pubBackgroundColor').value = document.getElementById('colorPicker').value
    }

    var bookIdIndex = null
    const checkBookId = (bookId, indx) => {
        if(bookId.length<1) return
        if(!parseInt(bookId)){
            alert("Please enter a valid bookId")
            document.getElementById("bookId"+indx).value = ""
            return
        }
        if(true){
            bookIdIndex=indx
            <g:remoteFunction controller="publisherManagement" action="checkBookId" params="'bookId='+bookId" onSuccess="alertBook(data)"/>
        }
    }
    const alertBook=(data)=>{
        if(data.status!="OK"){
            alert(data.status)
            document.getElementById("bookId"+bookIdIndex).value = ""
            return
        }
    }
    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }
</script>
