.main {
  background-color: white;
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}
.firstPage {
  background-color: #D3D3D3;
  padding-left: 50px;
}
.float-label-control {
  display: block;
  width: 100%;
  padding: 0.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
  //border-bottom: 1px solid #aaa;
  outline: none;
  margin-right: 20px;
  background: none;
  display: flex;
}
.cktext{
  width: 100%;
}
.quiz{
  display: -webkit-box;
  width: 80%;
  //padding-bottom: 20px;
}
.float-label-control input {
  display: block;
  width: 100%;
  //padding: 7.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #aaa;
  outline: none;
  margin-right: 20px;
  background: none;

}
.quiz1{
    display: -webkit-box;
    width: 80%;
  padding:0.1em 3em 5px 4em;
}
.quiz2{
  display: -webkit-box;
  width: 80%;
  padding:0.1em 3em 5px 4em;
}
.quiz3{
  display: -webkit-box;
  width: 80%;
  padding:1.1em 3em 5px 4em;
}
.quiz4{
  display: -webkit-box;
  width: 80%;
  padding:1.1em 3em 5px 4em;
  #subject{
    width: 33.33%;
  }
}
.quiz5{
  display: -webkit-box;
  //width: 80%;
  padding:1.1em 3em 5px 4em;
}
.quiz6{
  display: -webkit-box;
  //width: 80%;
  padding:1.1em 3em 5px 16em;
  .text-center {
    text-align: center;
    //margin-left: -195px;
    //.btn-primary {
    //  color: #ffffff;
    //  background-color: #f15b2a;
    //  border-color: #ef4912;
    //  margin:0px;
    //}
    @media (min-width: 768px) {
      .col-sm-12 {
        width: 100%;
        float: left;
      }
    }
    .btn-primary {
      color: #ffffff;
      background-color:@ws-darkOrange ;
      border-color: @ws-darkOrange;
      margin: 0px;
    }
    .btn:focus,.btn:active {
      background-color: @ws-darkOrange!important;
    }
  }
}
.quiz7{
  display: -webkit-box;
  //width: 80%;
  padding:1.1em 3em 5px 4em;
}
#static-content{
  h4{
    margin-bottom: 9px;
    //margin-top: 11px;
    margin-left: 0px;
    font-size: 18px;
    font-weight: 500;
  }

}
.pagenumber-green {
  height: 21px;
  width: 35px;
  text-align: center;
  border-width: 1px;
  border-color: green;
  border-radius: 3px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
  background-color: green;
  color: white;
}
.quiz8 {
 width: 100%;
  .alert-warning {
    background-color: #f8d9ac;
    border-color: #f6bd95;
    color: #f0ad4e;
  }

  .alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    margin-left: 14px;

    }
}
#sidebar{
  a:focus{
    background-color: green;
    color: black;
    text-decoration: none;
  }
  a:active{
    background-color: green;
    color: black;
    text-decoration: none;
  }
  a:hover{
    text-decoration: none;
  }
}