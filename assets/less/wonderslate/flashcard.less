@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";
@import "../wonderslate/wscomponents/customInput.less";
@import "../wonderslate/wscomponents/toggleswitch.less";

::-webkit-scrollbar {
  width: 0;  /* Remove scrollbar space */
  background: transparent;  /* Optional: just make scrollbar invisible */
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
@card-shadow:0 0 10px rgba(0, 0, 0, 0.08);
@card-border-radius:5px;
@card-width:500px;
@card-height:300px;
#htmlreadingcontent{
  margin-top: 0;
}

.btn{
  font-size: 12px;
  font-weight: @regular;
  &:hover{
    transition:all 0.5s;
  }
}
.flashcards{
  position: relative;
  width: @card-width;
  height: @card-height;
  margin-top: 2rem;
  @media @extraSmallDevices,@smallDevices,@mediumDevices{
    height: @card-width - 100;
    width: @card-height - 50;
  }
  .flash-card{
    position: absolute;
    width: @card-width;
    height: @card-height;
    box-shadow: @card-shadow;
    -webkit-box-shadow: @card-shadow;
    -moz-box-shadow: @card-shadow;
    border-radius: @card-border-radius;
    @media @extraSmallDevices,@smallDevices,@mediumDevices{
      height: @card-width - 100;
      width: @card-height - 50;
    }
    &:last-child{
      margin-bottom: 4rem;
    }

  }
  .slide-left{
    z-index: 99999;
    animation-name: slideLeft;
    animation-duration: 0.2s;
    transform-origin: bottom left;
    transform-style: preserve-3D;
  }
  .slide-right{
    z-index: 99999;
    animation-name: slideRight;
    animation-duration: 0.2s;
    transform-origin: bottom left;
    transform-style: preserve-3D;
  }
  @keyframes slideLeft {
    0%   {opacity: 0.9;left:0; top:0;transform:rotate(-2deg);}
    100%  {opacity: 0.5;left:-100px; top:0;transform:rotate(-3deg);}
  }
  @keyframes slideRight {
    0%   {opacity: 0.5;left:-100px; top:0;transform:rotate(-3deg);}
    100%  {opacity: 0.9;left:0; top:0;transform:rotate(-2deg);}
  }
.color-1{
  background: @blue;
}
  .color-2{
    background: @yellow;
  }
  .color-3{
    background: @teal;
  }

}
.card-previous{
  position: absolute;
  z-index: 98;
  left: 0;
  top:50%;
  background: @white;
  box-shadow: 0 0 4px @gray-light-shadow;
  -webkit-box-shadow: 0 0 4px @gray-light-shadow;
  -moz-box-shadow: 0 0 4px @gray-light-shadow;
  border-radius: 28px 0 0 28px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0;
  border:none;
  &:hover{
    cursor: pointer;
  }
  i{
    color:@blue;
    transform: matrix(-1, 0, 0, 1, 0, 0);
    font-size: 18px;

  }
}
.card-next{
  position: absolute;
  z-index: 98;
  right: 0;
  top:50%;
  background: @white;
  box-shadow: 0 0 4px @gray-light-shadow;
  -webkit-box-shadow: 0 0 4px @gray-light-shadow;
  -moz-box-shadow: 0 0 4px @gray-light-shadow;
  border-radius: 28px 0 0 28px;
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0;
  border:none;
  &:hover{
    cursor: pointer;
  }
  i{
    color:@blue;
    font-size: 18px;

  }
}
.slide1{
  z-index: 92;
}
.slide2{
  z-index:91;
  transform: rotate(1.94deg);
  display: block !important;
}
.slide3{
  z-index: 90;
  transform: rotate(3.49deg);
  display: block !important;
}
button:focus{
  outline:0;
}
.flip-box-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flash-card:hover .flip-box-inner {

}
.flipCard{
  transform: rotateY(180deg);
}
.rotate-icon{
  transform: rotate(90deg);
  transition:transform 0.5s ease-in;
  -webkit-transition: -webkit-transform 0.5s ease-in;
}

.flip-box-front, .flip-box-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  flex-direction: column;
  overflow: hidden;

  p{
    font-style: normal;
    font-weight: @regular;
    font-size: 14px;
    color:@white;
    padding: 1.7rem;

  }
}
.flip-box-front {
  p {
    padding: 0;
  }
}
.flip-box-front,.flip-box-back{
  &.removeFlex {
    display: table-cell;
    overflow: hidden;
  }
  p {
    font-size: 12px;
  }
  >p {
    margin-top: 1rem;
  }
  td {
    font-size:12px;
    color:@white;
  }
  table{
    margin: 1rem auto 0;
  }
}
  .flip-box-back {
    background:radial-gradient(94.76% 94.76% at 7.57% 6.19%, #E9E9E9 0%, #FFFFFF 89.06%);
    p {
      color: @black;
    }
  }
  .flip-box-back td p{
    padding: 0;
  }
  .flip-box-front td p {
    padding: 0;
  }
  .flip-box-front td{
    padding: 0;
  }

  .flip-box-back {
    transform: rotateY(180deg);
  }

  .flashcard-wrapper{
    margin: 0 auto;
    .setname{
      border: 1px solid lighten(@dark-gray,25%);
      width: 300px;
      border-top: none;
      border-left: none;
      border-right: none;
      border-radius: 0;
      padding: 5px;
    }
  }

  .dots{
    &::before{
      content:'';
      background: url('../../images/ws/dots.svg') center center no-repeat;
      width: 20px;
      position: absolute;
      top:0;
      left: -15px;
      height: 100%;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        left: -10px;
      }
    }
  }
  #empty {
    &.card-box {
      border: none;
      &:hover{
        .delete-card{
          display: none;
        }
      }
    }
    .card-box {
      border: none;
      &:hover{
        .delete-card{
          display: none;
        }
      }
    }
  }
  .card-box{
    background: @white;
    box-shadow: 0 0 10px @gray-light-shadow;
    -webkit-box-shadow: 0 0 10px @gray-light-shadow;
    -moz-box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 5px;
    padding: 10px 10px 15px;
    width: 95%;
    border:1px solid #27AE60;
    @media @extraSmallDevices,@smallDevices,@mediumDevices{
      width: 100%;
    }
    &:hover{
      .delete-card{
        display: block;
      }
    }

    .card-count{
      color: lighten(@dark-gray,25%);
      font-size: 10px;
      margin-bottom: 5px;
      display: block;
    }
    .cardHeader{
      display: flex;
      align-items: center;
      justify-content: space-between;
      p{
        color: lighten(@dark-gray,25%);
        font-size: 10px;
        margin-bottom: 10px;
      }
      form{
        p{
          display: flex;
          align-items: center;
        }
      }
    }
    input{
      border:none;
      box-shadow: inset 0 0 5px @gray-light-shadow;
      -webkit-box-shadow: inset 0 0 5px @gray-light-shadow;
      -moz-box-shadow: inset 0 0 5px @gray-light-shadow;
      border-radius: 5px;
    }
    textarea{
      background: @white;
      box-shadow: inset 0 0 5px @gray-light-shadow;
      -webkit-box-shadow: inset 0 0 5px @gray-light-shadow;
      -moz-box-shadow: inset 0 0 5px @gray-light-shadow;
      border-radius: 5px;
      font-size: 14px;
      font-style: italic;
      color: @dark-gray;
      border:none;
      resize: none;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        font-size: 16px;
      }
    }
  }
  .revision-answer{
    p{
      font-size: 12px;
      color:@cyan;
    }
    textarea{
      background: @white;
      box-shadow: inset 0 0 5px @gray-light-shadow;
      -webkit-box-shadow: inset 0 0 5px @gray-light-shadow;
      -moz-box-shadow: inset 0 0 5px @gray-light-shadow;
      border-radius: 5px;
      font-size: 14px;
      font-style: italic;
      color: @dark-gray;
      border:none;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        font-size: 16px;
      }
    }
  }
  .revision-question{
    textarea{
      background: @white;
      box-shadow: inset 0 0 5px @gray-light-shadow;
      -webkit-box-shadow: inset 0 0 5px @gray-light-shadow;
      -moz-box-shadow: inset 0 0 5px @gray-light-shadow;
      border-radius: 5px;
      font-size: 14px;
      font-style: italic;
      color: @dark-gray;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        font-size: 16px;
      }
    }
  }
  .add-text{
    background:transparent;
    border:none;
    color: lighten(@dark-gray,25%);
    font-size: 10px;
    &:focus{
      outline:0;
    }
  }
  .btn-flashcard{
    border: 1px solid @cyan;
    box-sizing: border-box;
    box-shadow:0 0 10px @gray-light-shadow;
    -webkit-box-shadow:0 0 10px @gray-light-shadow;
    -moz-box-shadow:0 0 10px @gray-light-shadow;
    border-radius: 5px;
    font-size: 12px;
    color:@cyan;
    display: flex;
    align-items: center;
    background: transparent;
    i{
      font-size: 16px;
      margin-right: 5px;
    }
    &.print{
      color:@theme-primary-color;
      border: 1px solid @theme-primary-color;
      i{
        margin-left: 5px;
        color:@theme-primary-color;
        font-size: 16px;
      }
    }
  }

  .back-flashCard{
    border: none;
    background: none;
    i{
      font-size: 18px;
      color:lighten(@dark-gray,25%);
    }
    &:focus{
      outline: 0;
    }
  }
  .submit-button {
    display: flex;
    justify-content: center;
    .btn {
      border: 1px solid @theme-primary-color;
      box-sizing: border-box;
      box-shadow: 0 0 10px @gray-light-shadow;
      -webkit-box-shadow:0 0 10px @gray-light-shadow;
      -moz-box-shadow:0 0 10px @gray-light-shadow;
      border-radius: 5px;
      color:@theme-primary-color ;
      margin-right: 10px;
      display: flex;
      align-items: center;

      &:hover {
        background: @theme-primary-color;
        color: @white;
      }
      &.cancel{
        border:1px solid @red;
        color:@red;
        &:hover{
          background: @white;
        }
      }
    }
  }
  .submit-buttons{
    display: flex;
    justify-content: center;
    margin: 0 auto;
    .btn{
      border: 1px solid @theme-primary-color;
      box-sizing: border-box;
      box-shadow: 0 0 10px @gray-light-shadow;
      -webkit-box-shadow:0 0 10px @gray-light-shadow;
      -moz-box-shadow:0 0 10px @gray-light-shadow;
      border-radius: 5px;
      color:@theme-primary-color;
      margin-right: 10px;
      display: flex;
      align-items: center;
      &:hover{
        background:@theme-primary-color;
        color:@white;
      }
      &.cancel{
        border:1px solid @red;
        color:@red;
        &:hover{
          background: @white;
        }
      }
      &.revise{
        box-shadow: 0 0 10px @gray-light-shadow;
        -webkit-box-shadow:0 0 10px @gray-light-shadow;
        -moz-box-shadow:0 0 10px @gray-light-shadow;
        border-radius: 5px;
      }
      &.save-card{

      }
    }
  }
  .add-card{
    border: 1px solid @cyan;
    box-sizing: border-box;
    box-shadow: 0 0 10px @gray-light-shadow;
    -webkit-box-shadow:0 0 10px @gray-light-shadow;
    -moz-box-shadow:0 0 10px @gray-light-shadow;
    border-radius: 5px;
    color:@cyan;
    width: 85px;
    &:hover{
      background:@cyan;
      color:@white;
    }
  }

  .setname-wrapper{

    p{
      display: flex;
      color: lighten(@dark-gray,25%);
      font-weight: @regular;
      margin: 0;
      padding: 0;
      align-items: center;
      position: absolute;
      right: 0;
      top: 15px;
      font-size: 12px;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        right: 15px;
      }
    }


  }
  .delete-card{
    background: none;
    border:none;
    display: none;
    cursor: pointer;
    i{
      color:darkred;
      font-size: 18px;
      line-height: 0;
    }
  }
  #my-cards{
    counter-reset: section;
  }
  .col-md-6 .card-count::after{
    counter-increment: section;
    content: "Set " counter(section) ": ";
  }
  .slideshow-container{

  }
  .flashcards{
    margin: 0 auto;
  }
  .flashcards .flash-card.active{

    z-index: 92;
    //transform: rotate(-80deg);
  }

  .turn-slide{
    border:none;
    color:@blue;
    border-radius: 50px;
    width: 50px;
    height: 50px;
    position: absolute;
    bottom:25px;
    z-index: 98;
    background: @white;
    box-shadow: 0 2px 10px @gray-light-shadow;
    -webkit-box-shadow:0 2px 10px @gray-light-shadow;
    -moz-box-shadow:0 2px 10px @gray-light-shadow;
    left: 45%;
    right: 45%;
    display: flex;
    align-items: center;
    justify-content: center;
    i:not(.rotate-icon){
      transform: rotate(-90deg);
      transition:transform 0.5s ease-in;
      -webkit-transition: -webkit-transform 0.5s ease-in;
    }
    @media @extraSmallDevices,@smallDevices,@mediumDevices{
      left: 42%;
      right: 42%;
      bottom:20px;
    }
  }

  #back-slider{
    background: none;
    border:none;
    i{
      color: lighten(@dark-gray,25%);
    }
    &:focus{
      outline:0;
    }
  }
  .edit{
    background:@blue;
    padding: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    border: none;
    color:@white;
    i{
      color: @white;
      font-size: 15px;
      margin-right: 4px;
    }
  }
  .saveCard {
    margin: 0 auto;
    .saveSubmit {
      background: @orange;
      box-shadow: 0 2px 4px @gray-light-shadow;
      -webkit-box-shadow:0 2px 4px @gray-light-shadow;
      -moz-box-shadow:0 2px 4px @gray-light-shadow;
      border-radius: 4px;
      //width: 100%;
      color: @white;
      font-size: 14px;
      height: 45px;
      padding: 10px 25px;
      font-weight: @medium;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  #successModal{
    .modal-header{
      border: none;
    }
    .modal-footer {
      border: none;
      .close-btn{
        background: @green;
        border-radius: 5px;
        color:@white;
      }
    }
    .modal-body{
      text-align: center;
      i{
        color:@green;
        font-size: 38px;
      }
      p{
        color:@green;
        font-size: 14px;
        margin-top: 10px;
        span{
          font-weight: @bold;
        }
      }

    }
  }
  #publicModal{
    #successModal;
    .modal-body{
      p{
        color:@theme-primary-color;
      }
    }
    .modal-footer {
      border: none;
      .close-btn{
        background: @theme-primary-color;
        border-radius: 5px;
        color:@white;
      }
      .cancel-btn{
        border:1px solid @theme-primary-color;
        color:@theme-primary-color;
        background: @white;
      }
    }
  }

  #deleteModal{
    #publicModal;
  }
  #deleteSetModal{
    #deleteModal;
  }
  .no-padding{
    @media @extraSmallDevices,@smallDevices,@mediumDevices{
      padding: 0;
    }
  }


  .non-ws {
    #revisionTitle {
      border: none;
      border-bottom: 1px solid lighten(@dark-gray,25%);
      border-radius: 0;
    }
    &.submit-buttons {
      .btn {
        border-color: @cyan;
        color: @cyan;
        &:hover {
          background: @cyan;
          color: @white;
        }
      }
    }
    .flip-box-back p {
      color: @white;
    }
  }
  .expand{
    position: absolute;
    z-index: 999;
    right: 5px;
    bottom: 5px;
    margin: 0 auto;
  }
#updateDefinition{
  p,td{
    font-weight: @bold;
  }
}
#updateDefinition,#updateTerms{
  table,image{
    margin: 0 auto;
  }
}
.modal{
  td,p,h4{
    color:@theme-primary-color;
  }
}