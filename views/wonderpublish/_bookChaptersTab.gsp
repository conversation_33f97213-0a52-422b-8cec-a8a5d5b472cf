    <link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<style>
table td a {
    color: #007bff;
}
table td a.btn {
    color: #fff;
}

.extract-mcqs-btn a{
    color: blue !important;
}
.list-chapters .chap-list-wrap{
    border-bottom: 1px solid #ddd !important;
}
.list-chapters .chap-list-wrap:last-child{
    border-bottom: none !important;
}
.list-chapters div{
    border-bottom: none;
}
</style>
<div class="row">
    <div class="col col-12 col-md-3 col-lg-3 ">
        <a id="back-btns" class="d-flex align-items-center goback"><i class="material-icons">arrow_left</i> Go Back</a>
        <%if(gptManager){%>
        <!--one text area and one submit button. Label for text area is "Copy Chapter" and button text is "Copy". Place holder for text area is "Comma separated book ids"-->
        <div class="form-group col-12">
            <label>Copy Chapters</label>
            <textarea class="form-control" type='text' name='number' id='copyBookIds' placeholder="Comma separated book ids"></textarea>
            <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" id="submitCopyChaptersBtn" onclick="submitCopyChapters()">Copy</button>
        </div>
        <%}%>
        <h4 class="bookchapterHeader"><strong>Book Chapters</strong></h4>

        <div class="chapter-wrapper mt-3">
            <div class="d-flex justify-content-between align-items-center p-1">
                <h4>Chapters</h4>
                <button class="btn btn-sm btn-default add-new ml-1" onclick="javascript:addNewChapter()">Add New</button>
                <%if(!institutePublisher){%>
                <button class="btn btn-sm btn-default add-new ml-1" id="copyChapterBtn" style="display: none;">Copy</button>
                <%}%>
            </div>
            <div class="list-chapters" id="chaptersList">

                <% if(chaptersMst!=null){
                    for(int i=0;i<chaptersMst.size();i++){
                        if("toc".equals(chaptersMst[i].chapterDesc)) continue;%>
                <div id="${chaptersMst[i].id}" class="chap-list-wrap p-2">
                    <div class="d-flex justify-content-between">
                        <a href="javascript:getChapterDetails('${chaptersMst[i].id}')" class="greytext" id="chapter${chaptersMst[i].id}">  ${i+1}. ${chaptersMst[i].name} (${chaptersMst[i].id})</a>
                        <%-- Conditional delete button based on lockEdit status --%>
                        <%if("71".equals(""+session["siteId"])){%>
                            <% if(showDeleteButton) { %>
                            <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR">
                                <a href="javascript:deleteChapter(${chaptersMst[i].id});"><span><i class="material-icons">delete_outline</i> </span>
                                </a>
                            </sec:ifAnyGranted>
                            <% } %>
                        <%}else{%>
                            <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                                <a href="javascript:deleteChapter(${chaptersMst[i].id});"><span><i class="material-icons">delete_outline</i> </span>
                                </a>
                            </sec:ifAnyGranted>
                        <%}%>
                    </div>
                    <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR">
                        <%if(chaptersMst[i].mcqsExtracted=="true"){%>
                        <div>
                            <span>Extracted</span>
                        </div>
                        <%}else{%>
                        <div class="extract-mcqs-btn">
                            <a href="javascript:extractMCQs(${chaptersMst[i].id});"><span>Extract</span></a>
                        </div>
                        <%}%>

                    </sec:ifAnyGranted>
                </div>
                <%}
                }%>

            </div>

            <div class="d-flex my-2 justify-content-center">
                <% if(chaptersMst!=null){ if(chaptersMst.size()>1) {%>
                <button class="btn btn-sm btn-primary" onclick="updateSortOrder();">Update Sort Order</button>
                <%}
                }%>
            </div>
        </div>
    </div>
    <div class="col col-12 col-md-9 col-lg-9">
        <div class="add-contents" id="chapterdetails" style="display: none;">
            <div class="form-group col-12 col-md-8 col-lg-8 px-0">
                <label>Chapter name</label>
                <input name="name" id="chaptername" class="form-control" type="text" placeholder="Enter chapter name here." onblur="javascript:chapterDtlUpdate(this);">
                <div class="valid-feedback">Valid.</div>
                <div class="invalid-feedback">Please fill out this field.</div>
            </div>

            <div class="form-group col-12 col-md-8 col-lg-8 px-0">
                <label for="chapterDesc">Tags</label>
                <small>** to be used in Youtube and Web search</small>
                <textarea class="form-control" rows="3" name='chapterDesc' id="chapterDesc"  placeholder="Tags" onblur="javascript:chapterDtlUpdate(this);" maxlength="2000"></textarea>
                <div class="valid-feedback">Valid.</div>
                <div class="invalid-feedback">Please fill out this field.</div>
            </div>

        </div>
        <div class="resources" style="display: none;" id="chapterdetailsdetails">
            <h2 class="border-bottom">Resources</h2>
            <div class="row">
                <div class="col-6"><h4 class="pt-3 pb-2">Upload Resources</h4>
                    <a href="" data-toggle="modal" data-target="#readingMaterials">Reading Materials</a>
                    <a href="" data-toggle="modal" data-target="#referenceVideoLinks">Reference Video link</a>
                    <a href="" data-toggle="modal" data-target="#referenceWebLinks">Reference Web link</a>
                    <% if(allowAudioVideo){%>
                    <a href="" data-toggle="modal" data-target="#paidVideos">Audio / Video files</a>
                    <%}%>
                    <% if(siteMst.sageOnly!="true") {%>
                    <a href="javascript:openRelatedVideos()">Related videos admin</a>
                    <%}%>
                </div>
                <div class="col-6 border-left">
                    <h4 class="pt-3 pb-2">Create Resources</h4>
                    <a href="javascript:createFlashCards()">Flashcards</a>
                    <a href="javascript:createMCQ()">Multiple Choice Questions</a>
                    <a href="javascript:createMCQBulk()">Multiple Choice Questions - Bulk</a>
                    <a href="javascript:createMCQFile()">Multiple Choice Questions - Excel File</a>
                    <a href="javascript:createMCQWordFile()">Multiple Choice Questions - Word File</a>
                    <a href="javascript:showQuizCopy('Multiple Choice Questions')">Copy Quiz</a>
                    <a href="javascript:showReadCopy('Notes')">Copy Reading material</a>
                    <a href="javascript:createHTML('')">Notes</a>
                    <a href="javascript:createQandA('QA')">Questions and Answers - Long</a>
                    <a href="javascript:createQandA('Short QA')">Questions and Answers - Short</a>
                    <a href="javascript:createQAFile()">Questions and Answers - Excel File</a>
                    <% if("books".equals(session["entryController"])){%>
                    <a href="javascript:createVideoHTML('')">Video Explanation</a>
                    <%}%>
                </div>
            </div>

            <div class="mt-3" id="newResourceTable" style="display: none;">
                <table class="table chapter-table table-bordered" id="addedContents">
                    <table id="chapter-resources" class="table table-sm table-bordered table-hover chapter-table">
                        <%if(!("sage".equals(session["entryController"]))) {%>
                        <thead class="bg-primary text-white text-center">
                        <tr>
                            <th>Resource Id</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Action</th>
                            <th></th>
                            <th>Set Zoom</th>
                            <th>Added by</th>
                        </tr>
                        </thead>
                        <%}%>
                    </table>

                </table>
            </div>

        </div>


    </div>

</div>

<div class="modal fade createBook-modal modal-modifier" id="copyChapter" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-left">
                <div class="form-group col-12">
                    <h5 class="mt-3 mb-4 pb-2 border-bottom">Copy chapter</h5>
                    <label>Chapter Id</label>
                    <input class="form-control" type='number' name='number' id='copyChapterId' placeholder="Enter the chapter id to copy" onkeypress="return onlyNumberKey(event)">
                    <div class="invalid-feedback" id="copyChapterError"></div>
                </div>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" id="submitCopyChapterBtn" onclick="submitCopy()">Copy</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="successCopyChapterModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3 text-success">Success!</h5>
                <p>The chapter is successfully copied. <br>Page will reload now.</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="chapterTabReload()">Ok</button>
                </div>
            </div>

        </div>
    </div>
</div>
<script>
    var newBookId='';
    var siteId = "${session['siteId']}";
    var canDeleteResources = false;
    var canEditResources = false;

    <%-- Check permissions for delete and edit based on lockEdit status --%>
    <%if("71".equals(""+session["siteId"])){%>
        <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR">
            <%-- For site ID 71, check if lockEdit is true --%>
            <% if(booksMst != null && "true".equals(booksMst.lockEdit)) { %>
                <%-- If lockEdit is true, only ROLE_PDF_EXTRACTOR can edit/delete --%>
                canDeleteResources = true;
                canEditResources = true;
            <% } else { %>
                <%-- If lockEdit is false or null, ROLE_PDF_EXTRACTOR can edit/delete --%>
                canDeleteResources = true;
                canEditResources = true;
            <% } %>
        </sec:ifAnyGranted>
    <%}else{%>
        <%-- For other sites, use existing logic --%>
        <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
            canDeleteResources = true;
            canEditResources = true;
        </sec:ifAnyGranted>
    <%}%>
    function copyChapter(){
        $("#copyChapter").modal('show').on('shown.bs.modal', function () {
            $('#copyChapterId').focus().on('keyup', function () {
                var checkNumber = $(this).val();

                if(jQuery.isNumeric(checkNumber) == false){
                    this.value = this.value.replace(/[^0-9\.]/g,'');
                    $('#copyChapterError').show().html('Please enter numbers only.');
                    $(this).addClass('input-error');
                    return;
                } else {
                    $('#copyChapterError').hide().html('');
                    $(this).removeClass('input-error');
                }
            });
        }).on('hidden.bs.modal', function () {
            $('#copyChapterError').hide().html('');
            $('#copyChapterId').removeClass('input-error');
        });
    }

    function submitCopy(bookId){
        newBookId = bookId;
        if(document.getElementById("copyChapterId").value==""){
            $("#copyChapterError").show().html('Please enter the chapter id.');
            $('#copyChapterId').addClass('input-error');
         }else{
            $('.loading-icon').removeClass('hidden');
            var chapterId = document.getElementById("copyChapterId").value;
            <g:remoteFunction controller="resourceCreator" action="copyChapter" onSuccess='chapterCopied(data);' params="'destBookId='+bookId+'&sourceChapterId='+chapterId"></g:remoteFunction>
        }
    }

    function chapterCopied(data){
        if(data.status=="Success"){
            $('.loading-icon').addClass('hidden');
            $("#copyChapter").modal('hide');
            $("#successCopyChapterModal").modal('show');
        }else{
            $('.loading-icon').addClass('hidden');
            $("#copyChapterError").show().html(data.status);
            $('#copyChapterId').addClass('input-error');
        }
    }

    function chapterTabReload() {
        $('.loading-icon').removeClass('hidden');
        if(mybookId) {
            window.location.href="/book-create-new?bookId="+mybookId;
        } else {
            window.location.href="/book-create-new?bookId="+newBookId;
        }
    }

   function myResources(allItems){
       var resourceData=formattedTopicData;
       var test= $('#chapter-resources').DataTable({
           destroy: true,
           'data':allItems,
            columns:[{
                "data": "id",
            },
                {
                    "data": "resName"
                },
                {
                    "data": "resType"
                },
                {
                    "data": resourceData,
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        if(data.resType==='Notes') {
                            <%  if(siteMst.mainResourcesPage != null && siteMst.mainResourcesPage == "true"){ %>
                                return "<div class='d-flex align-items-center justify-content-center'><a href='javascript:openBook(" + data.id + "," + data.bookId + ","+data.topicId+")' class='btn btn-primary'>" +
                                "&nbsp;<span class='light10text'>View</span> </a>" + (canEditResources ? "<a href='javascript:editHTML(\"" + "test" + "\"," + data.id + ")' class='btn btn-secondary'><span class='light10text'>Edit</span></a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>";
                            <%}else{%>
                                return "<div class='d-flex align-items-center justify-content-center'><a class='btn btn-primary' href=\"library/"+encodeURIComponent(data.resName).replace(/'/g,"&#39;")+"?checkurl=true&siteName=${session['entryController']}&bookId="+data.bookId+"&chapterId="+data.topicId+"\" target='_blank'>View</a>" + (canEditResources ? "<a href='javascript:editHTML(\"" + "test" + "\"," + data.id + ")' class='btn btn-secondary'><span class='light10text'>Edit</span></a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                            <%}%>
                        }
                        else if(data.resType==='KeyValues'){
                            return "<div class='d-flex align-items-center justify-content-center'>" +
                                "<a href='javascript:openFlashCard("+ data.topicId + ","+ data.id + "," + data.bookId +",\"" + data.resName.replace(/'/g,"&#39;") + "\")' class='btn btn-primary'>View</a>" + (canEditResources ? "<a href='javascript:editFlashCards(" + data.id + ")' class='btn btn-secondary'><span class='light10text'>Edit</span></a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") +
                                "</div>"
                        }
                        else if(data.resType==='Reference Videos'){
                            return  "<div class='d-flex align-items-center justify-content-center'><a href='javascript:playVideo(\"" + data.resLink + "\"," + data.id + ")' class='btn btn-primary'>Watch</a>" + (canEditResources ? "<a href='javascript:editResource(\"" + data.resLink + "\",\"" + data.id + "\",\"" + data.resName.replace(/'/g,"&#39;")+ "\",\"" + data.videoPlayer + "\",\"" + data.allowComments + "\",\"" + data.displayComments + "\",\"" + data.testStartDate + "\",\"" + data.testEndDate +"\",\"" + data.downloadlink1 + "\",\"" + data.downloadlink2 + "\",\"" + data.downloadlink3 + "\")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                        }
                        else if(data.resType==='Multiple Choice Questions'){
                            if(siteId==1||siteId==27) {
                             var formatQuiz = "<a href='/funlearn/renderMCQContent?quizId=" + data.resLink + "&resId=" + data.id + "' style='padding-right: 7px' target='_blank' class='text-primary mt-2'>Format MCQs</a>"
                            }else {
                                var formatQuiz =""
                            }
                            if (data.testStartDate || data.testResultDate){
                                return "<div class='d-flex align-items-center justify-content-center'><a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' style='padding-right: 7px' target='_blank' class='btn btn-primary'>Learn</a>" +
                                    "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.resLink + "&resId=" + data.id + "' target='_blank' class='btn btn-primary'>Practice</a>" +
                                    (canEditResources ? "<a href='javascript:editMCQ(" + data.id + ")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + (canEditResources ? "<a href='javascript:createMCQRead(" + data.id +"," + data.resLink +"\,\"" + (data.resName).replace(/'/g,"&#39;") +"\")' class='btn btn-secondary'>Create MCQ Read</a>" : "") + "</div>" +
                                    (canEditResources ? "<a href='/admin/videoExplanationUpdate?quizId=" + data.id + "' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Support information upload</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/mergeQuizzes?resId=" + data.id + "&chapterId="+data.topicId+"&bookId="+data.bookId+"&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2'>Merge MCQs</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/mcqSorter?resId=" + data.id + "&chapterId="+data.topicId+"&bookId="+data.bookId+"&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Sort MCQs</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/sectionModifier?resId=" + data.id + "&chapterId="+data.topicId+"&bookId="+data.bookId+"&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Add / Modify Sections</a>" : "") +
                                    formatQuiz+
                                    (canEditResources ? "<a href='javascript:quickEditMCQ(" + data.id + ")' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2'>Quick Edit</a>" : "")


                            }else{
                                return "<div class='d-flex align-items-center justify-content-center'><a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "' style='padding-right: 7px' target='_blank' class='btn btn-primary'>Learn</a>" +
                                    "<a href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=practice" + "&source=web" + "&pubDesk=true" + "&name=" + data.resName + "&siteName=" + "${session['siteName']}" + "&learn=false'  target='_blank' class='btn btn-primary'>Practice</a>" +
                                    (canEditResources ? "<a href='javascript:editMCQ(" + data.id + ")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + (canEditResources ? "<a href='javascript:createMCQRead(" + data.id + "," + data.resLink + "\,\"" + (data.resName).replace(/'/g, "&#39;") + "\")' class='btn btn-secondary'>Create MCQ Read</a>" : "") + "</div>" +
                                    (canEditResources ? "<a href='/admin/videoExplanationUpdate?quizId=" + data.id + "' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Support information upload</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/mergeQuizzes?resId=" + data.id + "&chapterId=" + data.topicId + "&bookId=" + data.bookId + "&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2'>Merge MCQs</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/mcqSorter?resId=" + data.id + "&chapterId=" + data.topicId + "&bookId=" + data.bookId + "&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Sort MCQs</a>" : "") +
                                    (canEditResources ? "<a href='/resourceCreator/sectionModifier?resId=" + data.id + "&chapterId="+data.topicId+"&bookId="+data.bookId+"&mode=view' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2' target='_blank'>Add / Modify Sections</a>" : "") +
                                    formatQuiz+
                                    (canEditResources ? "<a href='javascript:quickEditMCQ(" + data.id + ")' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2'>Quick Edit</a>" : "")


                            }
                        }
                        else if(data.resType==='Short QA'){
                            return "<div class='d-flex align-items-center justify-content-center'>" + (canEditResources ? "<a href='javascript:editQA(" + data.id + ")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                        }
                        else if(data.resType==='QA'){
                            return "<div class='d-flex align-items-center justify-content-center'>" + (canEditResources ? "<a href='javascript:editQA(" + data.id + ")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                        }
                        else if(data.resType==='Reference Web Links'){
                            return "<div class='d-flex align-items-center justify-content-center'><a href='javascript:openWebLink(\"" + data.resLink.replace(/#/g,":") + "\")' class='btn btn-primary'>Open</a>" + (canEditResources ? "<a href='javascript:editWeblinks(\"" + data.id + "\",\"" + (data.resName).replace(/'/g,"&#39;") + "\",\"" + data.resLink.replace(/#/g,":") + "\")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                        }
                        else if(data.resType==='videoexplanation'){
                            return "<div class='d-flex align-items-center justify-content-center'>" + (canEditResources ? "<a href='javascript:editVideoHTML(" + data.id + ")' class='btn btn-secondary'>Edit</a>" : "") + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                        }
                        else if(data.resType==='Uploaded Media'){
                            if(data.fileType==='mp3'){
                            return "<div class='d-flex align-items-center justify-content-center'><a href='javascript:playAudioFile("+ data.bookId +"," + data.id + ")' class='btn btn-primary'>Listen</a>" + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>";
                            }
                            else {
                                return "<div class='d-flex align-items-center justify-content-center'><a href='javascript:playVideoFile("+ data.bookId +"," + data.id + ")' class='btn btn-primary'>Watch</a>" + (canDeleteResources ? "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" : "") + "</div>"
                            }
                        }
                    }
                },
                {
                    "data": resourceData,
                    "render": function (data, type, row) {
                        return "<a href='javascript:getDeepLink(" + data.topicId + "," + data.id + ",\"" + data.resType + "\",\"resource\")' class='text-primary'>Deep Link</a>"
                    }
                },
                {
                    "data":resourceData,
                    "render":function (data,type, row){
                        if(data.fileType =='pdf' || data.fileType =='zip') {
                            return "<select name=\"zoomLevel-" + data.id + "\" id=\"zoomLevel-" + data.id + "\" class=\"form-control\" onchange='javascript:addZoom(" + data.id + ")' style='font-size:12px;width:90px;'>" +
                                "<option value=\"\">Default</option>" +
                                "<option value=\"1\">130%</option>" +
                                " <option value=\"2\">160%</option>" +
                                " <option value=\"3\">190%</option>" +
                                "</select>";
                        }else{
                            return ""
                        }
                    }
                },
                {
                    "data": "creatorname"
                }
            ]
        });

       //On load holding zoom value
       for (var i=0;i<allItems.length;i++){
           if (allItems[i].fileType =='pdf' || allItems[i].fileType =='zip'){
               document.querySelector("#zoomLevel-"+allItems[i].id+" option[value='"+allItems[i].zoomLevel+"']").setAttribute('selected',true);
           }
       }
    }

    //Zoom editing option
    function addZoom(resId){
        var resourceDtlId = resId;
        var type = document.getElementById("zoomLevel-"+resourceDtlId);
        var selectedValue=type[type.selectedIndex].value;
        var mode = 'edit';
       <g:remoteFunction controller="resourceCreator" action="editPdfZoomLevel" params="'resourceDtlId='+resourceDtlId+'&zoomLevel='+selectedValue+'&mode='+mode" onSuccess="success(data)"/>
    }

    function success(data){
       if (data.status == 'OK'){
           alert('Zoom level has been updated')
       }
    }

    function submitCopyChapters(){
        var bookIds = document.getElementById("copyBookIds").value;
        if(bookIds==""){
            alert("Please enter the book id to copy the chapter");
        }else{
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="autogpt" action="copyChapters" params="'bookIds='+bookIds+'&destBookId='+bookId" onSuccess="chaptersCopied(data)"/>
        }
    }

    function chaptersCopied(data){
        $('.loading-icon').addClass('hidden');
        alert("Chapters copied successfully");
        location.reload();
    }

    function extractMCQs(chapterId){
        const redirectionURL = "/pdfExtractor/mcqExtractor?bookId="+"${params.bookId}"+"&chapterId="+chapterId
        window.open(redirectionURL, "_blank")
    }
</script>

