<asset:stylesheet href="wonderslate/readSection.css"></asset:stylesheet>

<sec:ifLoggedIn>
    <script>
        var loginUser=true;
    </script>
</sec:ifLoggedIn>
<sec:ifNotLoggedIn>
    <script>
        var loginUser=false;

    </script>
</sec:ifNotLoggedIn>

<%if("1".equals(""+session["siteId"])){%>
<style type="text/css" media="print">
    #all { display: none; }
</style>
<%}%>
<style>
@media print {
    iframe {
        display:none;
    }
}

.navBtns:disabled{
    cursor: not-allowed;
}
.epub_footer{
    justify-content: space-evenly !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
    .epub_footer{
        justify-content: space-between !important;
    }
    .epubHighlightsBtn, .epubDownloadBtn {
        width: auto !important;
        font-size: 10px;
        padding: 5px;
    }
}
</style>
<link rel="stylesheet" href="/assets/viewer.css">

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<asset:javascript src="wonderslate/epub-full.min.js"/>
<script>
    var readId;
    var resSubFileNm="";
    var defaultZoom;
    var zoomLevel;
    var prepjoySite = "${session['prepjoySite']}";
    var mobileView=false;
    var bookTitle = "";
    var previewMode=${previewMode};

    function displayReadingMaterial(id){

        readId = id;
       getHtmlsData(id);
        if ('${params.pubDesk}' == 'true' || '${params.pubDesk}' == true){
            masterChapterID = '${params.chapterId}'
        }
        localStorage.setItem('lastReadPDF',JSON.stringify({resId:id,pdfOpen:true,chapterId:masterChapterID,bookId:${params.bookId}}))
    }

    var epubContainerWidth = 0;
    if($(window).width > 767) {
        epubContainerWidth = $("#htmlreadingcontent").width();
    }else {
        epubContainerWidth = $(window).width();
    }
    var epubScreenWidth = (99/100) * (epubContainerWidth);
    var keyListener;
    var encryptedKey = "${encryptEpubKey}";
    var book = ePub();
    var epubPageNumber = 0;
    var epubChapterHref = [];
    var rendition;
    var firstTimeLoadingSingleEpubFile = false;
    var ebupChapterLink = "";
    var currentSpineItem = "";
    var epubPreviewChapterLink = "";
    var epubChapterIndex=[];
    var totalBookChapter;
    var totalChaptersList;
    var showPdf;
    var encodedStringPartsObj = {};
    var epubPagePercentage=0;
    var epubCurPage=1;
    var epubTotalPages=0;
    var tocObj;
    var currChap = null;
    var genericReader = ${genericReader};
    var openingFirst=true;
    function displayEpub(data,resId,bookID,chapterIdForPDF,resIdVal){

        epubPageNumber=0;
        currChap = chapterIdForPDF;
        var epubLastRead = localStorage.getItem('lastReadDetails');

        if (epubLastRead !="" && epubLastRead !=null && epubLastRead !=undefined){
            epubLastRead = JSON.parse(epubLastRead);
        }

        //checking no of total chapters splitted at left side column.
         totalChaptersList= $('.read-book-chapters-wrapper').children('li').length;
        if(data.ebupChapterLink == undefined || data.ebupChapterLink == "" || data.ebupChapterLink == null ) book = ePub();
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        document.getElementById('reader').innerHTML = "";
        $("#htmlreadingcontent").show();
        $("#htmlreadingcontent").html("");
        $('#chapter-actions').removeClass('d-lg-flex');
        $('.epub-action').css('display','flex');
        if($(window).width()<767) {
            $('#chapter-actions').removeClass('d-flex').addClass('d-none');
            $('.generateTest').addClass('d-none');
        }
        $('.chapter-notes,#print-all-action,#chapter-all-action,#content-data-all').hide();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
        }


        $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').hide();
        $('.add-tabs').removeClass('d-lg-flex').addClass('d-none');
        document.getElementById('reader').classList.add('readerAnimation');
        document.getElementById('reader').style.zIndex='9999';
        var item = document.getElementById('reader');
        var chapterNameStr = "";
        var navElement = "<div class='epub_nav'>" +
                            "<div class='d-flex align-items-center wrap-1'>"+
                                "<div class='d-flex align-items-center'>"+
                                    "<button class='epub_nav-backBtn' title='Back to resources'>" +
                                        "<i class='material-icons-round'>keyboard_backspace</i>"+
                                    "</button>";
                                    if (!previewMode && genericReader) {
                                        navElement+= "<div id='chaptersListDisplayEpub'>" +
                                            " <select name='chaptersList' id='chaptersList' onchange='getDropDownValue(this.value)'>";
                                        <g:each in="${topicMst}" var="chapter" status="i">
                                        <%
                                            // Replace line breaks with spaces using a regular expression
                                            String chapterName = chapter.name.replaceAll("\\r?\\n", " ");
                                            %>

                                        if (data.chapterId=='${chapter.id}'){
                                            navElement+=  "<option value='${chapter.id}' selected><%= chapterName %></option>";
                                        }else{
                                            navElement+=  "<option value='${chapter.id}'><%= chapterName %></option>";
                                        }
                                        </g:each>
                                        navElement+="</select>"+
                                        "</div>";
                                    }
                    navElement+="</div>"+
                            "</div>"+
                            "<div class='d-flex align-items-center wrap-2'>"+

                                    "<button class='btn epubHighlightsBtn d-flex align-items-center' id='epubHighlights'>" +
                                        "<i class='material-icons-round'>description</i>"+
                                        "Highlights"+
                                    "</button>" ;
                    if (!previewMode){
                            navElement+=
                                "<div class='d-flex align-items-center'>"+
                                    "<button class='btn navBtns' id='prevBtnEpub' title='Previous Page'>" +
                                        "<i class='material-icons-round'>west</i>"+
                                    "</button>" +
                                    "<button class='btn navBtns' id='nextBtnEpub' title='Next Page'>" +
                                        "<i class='material-icons-round'>east</i>"+
                                    "</button>" +
                                "</div>"+
                            "</div>";
                    }


        var footerElement = "<div class='epub_footer'>" ;
                            if (!previewMode || genericReader) {
                                footerElement+="<div id='previousChapter' style='visibility: hidden'></div>";
                            }
                footerElement+="<div class='ePubFooterWrap' style='width:auto!important; '>"+
                                    "<div class='d-flex justify-content-center align-items-center'>"+
                                        "<p>" +
                                            "Page " +
                                            "<span class='epub_footer-pageNum'></span> of  " +
                                            "<span class='epub_footer-totalPages'></span> " +
                                        "</p>"+
                                        "<p>" +
                                            "<span class='epub_footer-percentage' style='margin-left: 5px;margin-right:5px;'></span>"+
                                        "</p>"+
                                    "</div>";
                if (!previewMode) {
                    footerElement+= "<div class='' style='display: none'>" +
                                        "<input type='range' id='epubProgressSlider' max='100' min='1' step='1' class='epubProgressSlider'>" +
                                    "</div>" +
                                "</div>";
                    if (!previewMode || genericReader) {
                        footerElement+="<div id='nextChapter' style='visibility: hidden'></div>";
                    }
                    footerElement+="</div>";
                }

        var epubNotesElement = "<section class='notesWrapper displayOpenClose' style='z-index: 4'>"+
                                    "<div class='notesDisplay'>"+
                                        "<div class='notesDisplay_header'>"+
                                            "<h3 style='width: 100%;text-align: center'>Highlights</h3>"+
                                            "<button class='close'><i class='fa-solid fa-xmark'></i></button>"+
                                        "</div>"+
                                        "<div class='notesDisplay_body'>"+
                                            "<ul class='notesList'></ul>"+
                                        "</div>"+
                                    "</div>"+
                                "</section>";
        var epubLoaderElement = "<div class='lazyLoader' style='top: 75px !important;'>"+
                                   "<div class='anim'>"+
                                        "<div class='slider-anim'>"+
                                           "<div class='lottieLoader'>"+
                                            "</div>"+
                                    "<div style='margin-top: 12px;'>"+
                                       " <p style='font-size: 16px;'>Please wait while loading your book...</p>"+
                                    "</div>"+
                                        "</div>"+
                                   "</div>"+
                                 "</div>";

        item.insertAdjacentHTML('afterbegin',navElement);
        item.insertAdjacentHTML('beforeend',footerElement);
        item.insertAdjacentHTML('beforeend',epubNotesElement);
        item.insertAdjacentHTML('beforeend',epubLoaderElement);
        item.setAttribute('style','height:100%;');

        if(!previewMode && genericReader){
            const mySelectElement = document.getElementById("chaptersList");
            displayNextAndPreviousValues(mySelectElement);
        }
        document.querySelector('.epub_nav-backBtn').addEventListener('click',function (){
            saveEpubDetails(resIdVal,epubPageNumber,data.chapterId);
            if(genericReader){
                history.back();
            }else {
                if ($(window).width() < 767) {
                    $('header,.related-book-wrapper').show();
                } else {
                    $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').show();
                }

                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
                document.getElementById('reader').classList.remove('readerAnimation');
                item.innerHTML = '';
                keyListener = undefined;
                if ($(window).width() < 768) {
                    if (showResourceList) {
                        document.querySelector('.side-chapters').classList.add('d-none');
                    }
                }
                closeResourceScreen();

                if (data.isSingleEpubFile == true) {
                    getChapterDetails(currChap)
                }
            }
        });

        document.getElementById('epubHighlights').addEventListener('click',function (){
            if (loggedInUser){
                viewChapterNotesEpub(resIdVal,bookID,chapterIdForPDF);
            }else{
                loginOpen()
            }
        });

        var footerProgressSlider  = document.getElementById('epubProgressSlider');

        if(data.isSingleEpubFile == true && firstTimeLoadingSingleEpubFile == true){
            if($(window).width()<767){
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,});
            }else {
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,
                    // script:"../assets/annotator-template.js"
                });
            }
            if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) {
                ebupChapterLink = data.ebupChapterLink;
                currentSpineItem = ebupChapterLink;
                if(data.previewChapterLink != undefined && data.previewChapterLink != null && data.previewChapterLink != "") epubPreviewChapterLink = data.previewChapterLink.split("#")[0];
                rendition.display(data.ebupChapterLink.split("#")[0]);
            }
            else rendition.display();
            rendition.on('rendered', function(section, view) {
                for(var b=0;b<book.spine.spineItems.length;b++){
                    if(book.spine.spineItems[b].href == currentSpineItem.split("#")[0]) epubPageNumber = book.spine.spineItems[b].index;
                }
                $('.loading-icon').addClass('hidden');
                firstTimeLoadingSingleEpubFile = true;
                var contents = rendition.getContents();
                for(var c=0;c<contents.length;c++){
                    // contents[c].css('color','red',true);
                    contents[c].document.oncontextmenu = document.body.oncontextmenu = function() {return false;}
                    var bookId = "${params.bookId}";
                    var resId = 0;
                    if(data.isSingleEpubFile == true ) resId = "${params.bookId}";
                    else resId = readId;
                    $(contents[c].document).find("body").attr("annotator-data",""+resId+"__"+bookLang+"__"+serverPath+"__"+bookId+"__"+ebupChapterLink);
                    contents[c].addStylesheet(serverPath+"/assets/wonderslate/annotator.min.css");
                    contents[c].addStylesheet(serverPath+"/assets/wonderslate/annotator-template.css");
                    contents[c].addStylesheet(serverPath+"/assets/landingpage/printFix.css");
                    <sec:ifLoggedIn>
                    contents[c].addScript(serverPath+'/assets/login.js');
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                    contents[c].addScript(serverPath+'/assets/notlogin.js');
                    </sec:ifNotLoggedIn>
                    contents[c].addScript(serverPath+'/assets/wonderslate/annotator-template.js');
                    contents[c].contentWidth(epubScreenWidth);
                }

                setTimeout(function (){
                    var iframeBodyElement = document.querySelector('.epub-container .epub-view iframe').contentWindow.document.querySelector('body');
                    var readerElm = document.querySelector('.notesWrapper');
                    iframeBodyElement.addEventListener('copy',function (e){
                        e.preventDefault();
                        return false;
                    })
                    iframeBodyElement.addEventListener('cut',function (e){
                        e.preventDefault();
                        return false;
                    })

                    readerElm.addEventListener('copy',function (e){
                        e.preventDefault();
                        return false;
                    })
                    readerElm.addEventListener('cut',function (e){
                        e.preventDefault();
                        return false;
                    })

                    document.addEventListener('keydown',function (e){
                        if (e.ctrlKey || e.metaKey){
                            if (e.keyCode==65){
                                e.preventDefault();
                                return false;
                            }
                        }
                    });

                    if (document.querySelector('.epub-container') !=null){
                        document.querySelector('.epub-container').setAttribute('style','height:100% !important;overflow:scroll !important;padding-bottom:8rem')
                    }
                    if (book.spine.spineItems.length<=1){
                        $('#nextBtnEpub').attr('disabled','disabled');
                    }
                    if($(window).width()<767){
                        iframeBodyElement.style.overflow="scroll"
                    }

                },500)
                document.querySelector('.lazyLoader').classList.add('loaderHidden');setTimeout(function (){
                    var iframeBodyElement = document.querySelector('.epub-container .epub-view iframe').contentWindow.document.querySelector('body');
                    var readerElm = document.querySelector('.notesWrapper');
                    iframeBodyElement.addEventListener('copy',function (e){
                        e.preventDefault();
                        return false;
                    })
                    iframeBodyElement.addEventListener('cut',function (e){
                        e.preventDefault();
                        return false;
                    })

                    readerElm.addEventListener('copy',function (e){
                        e.preventDefault();
                        return false;
                    })
                    readerElm.addEventListener('cut',function (e){
                        e.preventDefault();
                        return false;
                    })

                    document.addEventListener('keydown',function (e){
                        if (e.ctrlKey || e.metaKey){
                            if (e.keyCode==65){
                                e.preventDefault();
                                return false;
                            }
                        }
                    });

                    if (document.querySelector('.epub-container') !=null){
                        document.querySelector('.epub-container').setAttribute('style','height:100% !important;overflow:scroll !important;padding-bottom:8rem')
                    }
                    if (book.spine.spineItems.length<=1){
                        $('#nextBtnEpub').attr('disabled','disabled');
                    }
                    if($(window).width()<767){
                        iframeBodyElement.style.overflow="scroll"
                    }

                },500)
                document.querySelector('.lazyLoader').classList.add('loaderHidden');
            });
        } else{
            book.open(serverPath+"/funlearn/getEpubFile?resId="+resIdVal+"&encryptedKey="+encryptedKey,"epub").then(function(toc){
                    if (!prepjoySite){
                        $('.loading-icon').addClass('hidden');
                    }else{
                        $('#loading').hide();
                    }
                },
                function (error) {
                    //console.log("Status : "+ error.status);
                    //console.log("Stack : " + error.stack);
                });
            if($(window).width()<767){
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,});
            }else {
                rendition = book.renderTo("reader", {
                    flow: "scrolled-doc",
                    width: epubScreenWidth,
                    // script:"../assets/annotator-template.js"
                });
            }
            if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) {
                ebupChapterLink = data.ebupChapterLink;
                currentSpineItem = ebupChapterLink;
                if(data.previewChapterLink != undefined && data.previewChapterLink != null && data.previewChapterLink != "") epubPreviewChapterLink = data.previewChapterLink.split("#")[0];
                rendition.display(data.ebupChapterLink.split("#")[0]);
            }
            else {
                rendition.display();
            }
        }

        rendition.on('rendered', function(section, view) {
            for(var b=0;b<book.spine.spineItems.length;b++){
                if(book.spine.spineItems[b].href == currentSpineItem.split("#")[0]) {
                    epubPageNumber = book.spine.spineItems[b].index;
                }
            }

            var totalPageNos = book.spine.spineItems.length;
            var epubPercentage = Math.round((100 * (epubPageNumber+1) / totalPageNos));
            document.querySelector('.epub_footer-totalPages').innerHTML = totalPageNos;
            document.querySelector('.epub_footer-pageNum').innerHTML = epubPageNumber+1;
            document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';

            if (genericReader){
                if (epubLastRead !=null){
                    epubLastRead.map(item=>{
                        if (item.bookId=='${params.bookId}' && openingFirst && !previewMode){
                            openSpecificEPubPage(data,book,item.pageNo,item.chapterid)
                            openingFirst = false;
                        }
                    });
                }
            }

            if (data.isSingleEpubFile !=true && data.isSingleEpubFile!="true" && !previewMode){
                openSpecificEPubPage(data,book,epubPageNumber)
            }

            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
            // //console.log(book);
            firstTimeLoadingSingleEpubFile = true;
            var contents = rendition.getContents();
            var epubContentBody;
            for(var c=0;c<contents.length;c++){
                // contents[c].css('color','red',true);
                contents[c].document.oncontextmenu = document.body.oncontextmenu = function() {return false;}
                var bookId = 0;
                <% if(params.bookId == null || params.bookId == '') { %>
                bookId=urlBookId;
                <% } else {%>
                bookId="${params.bookId}";
                <%}%>
                var resId = 0;
                if(data.isSingleEpubFile == true ) {
                    resId = bookId;
                }
                else resId = readId;
                epubContentBody = $(contents[c].document).find("body");
                $(contents[c].document).find("body").attr("annotator-data",""+resId+"__"+bookLang+"__"+serverPath+"__"+bookId+"__"+ebupChapterLink);
                contents[c].addStylesheet(serverPath+"/assets/wonderslate/annotator.min.css");
                contents[c].addStylesheet(serverPath+"/assets/wonderslate/annotator-template.css");
                contents[c].addStylesheet(serverPath+"/assets/landingpage/printFix.css");
                <sec:ifLoggedIn>
                contents[c].addScript(serverPath+'/assets/login.js');
                </sec:ifLoggedIn>
                <sec:ifNotLoggedIn>
                contents[c].addScript(serverPath+'/assets/notlogin.js');
                </sec:ifNotLoggedIn>
                contents[c].addScript(serverPath+'/assets/wonderslate/annotator-template.js');
                contents[c].contentWidth(epubScreenWidth);
                $(contents[c].document).find("body").bind('cut copy paste', function (e) {
                    e.preventDefault();
                });

                //Setting no of Chapters in book.
                totalBookChapter=book.spine.spineItems.length;
                //disabeling and enabling next button based on selected chapter
                if(epubPageNumber===(totalBookChapter - 1)){
                    $('#nextBtnEpub').attr('disabled','disabled');
                }
                //disabeling and enabling prev button based on selected chapter
                if(epubPageNumber===0){
                    $('#prevBtnEpub').attr('disabled','disabled');
                    $('#nextBtnEpub').removeAttr('disabled','disabled');
                }
                else{
                    $('#prevBtnEpub').removeAttr('disabled','disabled');
                }
                //onclick of chapters
                //for section base epub file where left side chapterlist is less than total chapters in book
                if((totalBookChapter!=totalChaptersList)){
                    var bookChapterList = epubChapterIndex[0].toc;
                    for (var e = 0; e < bookChapterList.length; e++) {
                        if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                            $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                var bookLabel=bookChapterList[e].label.split('\n').join('');
                                bookLabel=bookLabel.split('\t').join('');
                                bookLabel=bookLabel.split(':').join('');
                                if (elem.innerText.toUpperCase() == bookLabel.toUpperCase()) {
                                    $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                    $(elem).addClass('orangeText');
                                }
                            });
                        }
                    }
                }

            }
            setTimeout(function (){
                var iframeBodyElement = document.querySelector('.epub-container .epub-view iframe').contentWindow.document.querySelector('body');
                var readerElm = document.querySelector('.notesWrapper');
                iframeBodyElement.addEventListener('copy',function (e){
                    e.preventDefault();
                    return false;
                })
                iframeBodyElement.addEventListener('cut',function (e){
                    e.preventDefault();
                    return false;
                })

                readerElm.addEventListener('copy',function (e){
                    e.preventDefault();
                    return false;
                })
                readerElm.addEventListener('cut',function (e){
                    e.preventDefault();
                    return false;
                })

                document.addEventListener('keydown',function (e){
                    if (e.ctrlKey || e.metaKey){
                        if (e.keyCode==65){
                            e.preventDefault();
                            return false;
                        }
                    }
                });

                if (document.querySelector('.epub-container') !=null){
                    document.querySelector('.epub-container').setAttribute('style','height:100% !important;overflow:scroll !important;padding-bottom:8rem')
                }
                if (book.spine.spineItems.length<=1){
                    $('#nextBtnEpub').attr('disabled','disabled');
                }

                if($(window).width()<767){
                    iframeBodyElement.style.overflow="scroll"
                }
                if (genericReader){
                    enableOnswipe();
                }
            },500)
            document.querySelector('.lazyLoader').classList.add('loaderHidden');
        });

        // Navigation loaded
        book.loaded.navigation.then(function(toc){
            //Pushing left side column chapter details in epubChapterIndex array
            epubChapterIndex.push(toc);
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
            //console.log(toc);
            if(epubChapterHref.length < toc.length) toc.forEach(function(chapter) {
                if(epubChapterHref.indexOf(chapter.href) == -1 ) epubChapterHref.push(chapter.href);
            });

            tocObj = toc;
            var loaderSlogans = ["Expand your mind, not your shelves: switch to ebooks.",
                "Ebooks: countless stories at your fingertips.",
                "Ebooks: the future of reading, today.",
                "Take your library with you: read ebooks!",
                "Carry a library in your pocket with ebooks.",
                "Get lost in a good book, anytime, anywhere with ebooks.",
            ];

            sloganInterval = setInterval(function (){
                if (document.getElementById('sloganTexts')!=null){
                    document.getElementById('sloganTexts').innerHTML = loaderSlogans[Math.round(Math.random()*5)];
                }
            },2500);

            setTimeout(function (){
                $('#loadAnim-2').show();
                $('#loadAnim-1').hide();
            },2500);
            document.querySelector('.lazyLoader').classList.add('loaderHidden');
        });

        if(keyListener == undefined) {
            $('#nextBtnEpub').click(function () {
                openingFirst=false;
                if (!genericReader){
                    epubPageNumber = epubPageNumber + 1;
                    totalBookChapter=book.spine.spineItems.length;
                    if(epubPageNumber > 0){
                        $('#prevBtnEpub').removeAttr('disabled','disabled');
                    }
                    for(var b=0;b<book.spine.spineItems.length;b++){
                        if(book.spine.spineItems[b].index == epubPageNumber) {
                            if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                            }
                            currentSpineItem = book.spine.spineItems[b].href;
                            if(epubPreviewChapterLink == "" || epubPreviewChapterLink == undefined || epubPreviewChapterLink == null) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                            else if(epubPreviewChapterLink == ebupChapterLink) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                            else {
                                epubPageNumber = epubPageNumber - 1;
                                break;
                            }
                        }
                    }


                    if(totalBookChapter==totalChaptersList){   //For epub file where left side chapterlist and total chapters in book are same
                        //Add active state and remove active state
                        var active = $('.read-book-chapters-wrapper li.orangeText');
                        if (active.is(':last-child')) {
                            $('.read-book-chapters-wrapper li:first-child').addClass('orangeText');
                            active.removeClass('orangeText')
                        }
                        active.next().addClass('orangeText');
                        active.removeClass('orangeText');
                        currChap =  active.next().children()[1].id.match(/\d+/g)[0];
                        localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                    }
                    else { //for section base epub file where left side chapterlist is less than total chapters in book
                        var bookChapterList = epubChapterIndex[0].toc;//get chapterlist
                        for (var e = 0; e < bookChapterList.length; e++) {
                            //check chapterlist's chapterLink equal to right side chapterLink
                            if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                                $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                    //check if chaptername at left side equal to Right side chapter name
                                    if (elem.innerText.toUpperCase() == bookChapterList[e].label.toUpperCase()) {
                                        $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                        $(elem).addClass('orangeText');
                                        currChap = $(elem).attr('id').match(/\d+/g)[0];
                                        localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                                    }
                                });
                            }
                        }
                    }
                    var epubPercentage = Math.round((100 * (epubPageNumber+1) / book.spine.spineItems.length));
                    document.querySelector('.epub_footer-pageNum').textContent = epubPageNumber+1;
                    document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
                    footerProgressSlider.value = epubPercentage
                }else{
                    const mySelectElement = document.getElementById("chaptersList");
                    const selectedIndex = mySelectElement.selectedIndex;
                    const nextValue = getNextElementValue(mySelectElement);
                    const previousValue = getPreviousElementValue(mySelectElement);
                    if (nextValue!=null){
                        getHtmlsDataByChapter(nextValue);
                    }
                }

            });
            $('#prevBtnEpub').click(function () {
                openingFirst=false;
                if (!genericReader){
                    if(epubPageNumber > 0){
                        $('#nextBtnEpub').removeAttr('disabled','disabled');

                        var epubPercentage = Math.round((100 * (epubPageNumber) / book.spine.spineItems.length));
                        document.querySelector('.epub_footer-pageNum').textContent = epubPageNumber;
                        document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
                        footerProgressSlider.value = epubPercentage;

                        epubPageNumber = epubPageNumber - 1;
                        for(var b=0;b<book.spine.spineItems.length;b++){
                            if(book.spine.spineItems[b].index == epubPageNumber) {
                                if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                    if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                                }
                                currentSpineItem = book.spine.spineItems[b].href;
                                if(epubPreviewChapterLink == "" || epubPreviewChapterLink == undefined || epubPreviewChapterLink == null) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                                else if(epubPreviewChapterLink == ebupChapterLink) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                                else rendition.prev();
                            }
                        }
                        if(totalBookChapter==totalChaptersList){  //Previous state when prev button clicked
                            var active = $('.read-book-chapters-wrapper li.orangeText');
                            if (active.is(':last-child')) {
                                $('.read-book-chapters-wrapper li:last-child').addClass('orangeText');
                                active.removeClass('orangeText')
                            }
                            active.prev().addClass('orangeText');
                            active.removeClass('orangeText');
                            currChap =  active.prev().children()[1].id.match(/\d+/g)[0];
                            localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                        }else { //for section base epub file where left side chapterlist is less than total chapters in book
                            var bookChapterList = epubChapterIndex[0].toc;//get chapterlist
                            for (var e = 0; e < bookChapterList.length; e++) {
                                //check chapterlist's chapterLink equal to right side chapterLink
                                if (bookChapterList[e].href.split("#")[0] === currentSpineItem) {
                                    $(".read-book-chapters-wrapper li").each(function (id, elem) {
                                        //check if chaptername at left side equal to Right side chapter name
                                        if (elem.innerText.toUpperCase() == bookChapterList[e].label.toUpperCase()) {
                                            $(".read-book-chapters-wrapper li").removeClass('orangeText');
                                            $(elem).addClass('orangeText');
                                            currChap = $(elem).attr('id').match(/\d+/g)[0];
                                            localStorage.setItem('lastReadPDF',JSON.stringify({resId:resId,pdfOpen:true,chapterId:currChap,bookId:${params.bookId}}))
                                        }
                                    });
                                }
                            }
                        }
                    }
                }else{
                    const mySelectElement = document.getElementById("chaptersList");
                    const selectedIndex = mySelectElement.selectedIndex;
                    const nextValue = getNextElementValue(mySelectElement);
                    const previousValue = getPreviousElementValue(mySelectElement);
                    if (previousValue!=null){
                        getHtmlsDataByChapter(previousValue);
                    }
                }
            });

            keyListener = function(e){
                // Left Key
                if ((e.keyCode || e.which) == 37) {
                    if(epubPageNumber > 0){
                        epubPageNumber = epubPageNumber - 1;
                        for(var b=0;b<book.spine.spineItems.length;b++){
                            if(book.spine.spineItems[b].index == epubPageNumber) {
                                if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                    if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                                }
                                currentSpineItem = book.spine.spineItems[b].href;
                                if(epubPreviewChapterLink == "" || epubPreviewChapterLink == undefined || epubPreviewChapterLink == null) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                                else if(epubPreviewChapterLink == ebupChapterLink) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                                else rendition.prev();
                            }
                        }
                    }
                }
                // Right Key
                if ((e.keyCode || e.which) == 39) {
                    epubPageNumber = epubPageNumber + 1;
                    for(var b=0;b<book.spine.spineItems.length;b++){
                        if(book.spine.spineItems[b].index == epubPageNumber) {
                            if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                                if(data.ebupChapterLink != undefined && data.ebupChapterLink != "" && data.ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                            }
                            currentSpineItem = book.spine.spineItems[b].href;
                            if(epubPreviewChapterLink == "" || epubPreviewChapterLink == undefined || epubPreviewChapterLink == null) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                            else if(epubPreviewChapterLink == ebupChapterLink) rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                            else {
                                epubPageNumber = epubPageNumber - 1;
                                break;
                            }
                        }
                    }
                }

            };
            rendition.on("keyup", keyListener);
        }else{
            if (genericReader){
                const mySelectElement = document.getElementById("chaptersList");
                const selectedIndex = mySelectElement.selectedIndex;
                const nextValue = getNextElementValue(mySelectElement);
                const previousValue = getPreviousElementValue(mySelectElement);

                $('#nextBtnEpub').click(function () {
                    if (nextValue!=null){
                        getHtmlsDataByChapter(nextValue);
                    }

                })
                $('#prevBtnEpub').click(function () {
                    if (previousValue!=null){
                        getHtmlsDataByChapter(previousValue);
                    }
                })
            }
        }


        document.addEventListener("keyup", keyListener, false);

        rendition.on("relocated", function(location){
            // //console.log(location);
        });
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
        }
    }

    function displayHtmls(data){
        $("#htmlreadingcontent").show();
        document.getElementById("htmlreadingcontent").addEventListener('copy',function (event){
            event.preventDefault();
        })
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
        }
        $('#chapter-actions').removeClass('d-lg-flex');
        if($(window).width()<767) {
            $('#chapter-actions').removeClass('d-flex').addClass('d-none');
            $('.generateTest').addClass('d-none');
        }
        $('.chapter-notes,#print-all-action,#chapter-all-action,#content-data-all').hide();
        var filename,resourceName,resLink;
        var cData;

        for(i=0;i<chapterDetailsData.length;i++){

            if(readId==chapterDetailsData[i].id){
                filename=chapterDetailsData[i].filename;
                resourceName=chapterDetailsData[i].resName;
                resLink=chapterDetailsData[i].resLink;
            }
        }

        var extraPath = "/OEBPS";
        var isZipBook = filename.toLowerCase().endsWith(".zip") || filename.toLowerCase().endsWith(".pdf");

        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract"+
            (isZipBook?"":extraPath)+"/"+resSubFileNm.substring(0,resSubFileNm.lastIndexOf("/") +1);


        if("CreatedOnline"==resourceName) resourceName="Chapter Reading Content";
        var htmls ="";
        if (siteId == 1){
            htmls += "<div class=\"row pb-4 mt-4 d-none d-lg-flex justify-content-between mx-0\">";
        }else {
            htmls += "<div class=\"row pb-4 mt-4 d-lg-flex justify-content-between mx-0\">";
        }
        htmls +="<i class='material-icons' onclick='javascript:closeResourceScreen();' style='cursor: pointer;'>keyboard_backspace</i>";
        htmls += "<h4>" + resourceName + "</h4>";

        htmls +=  "        <div class=\"text-right\"></div>\n" +
            "    </div>"+data;


        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }

        var $iframe = $('#htmlreadingcontent');

        $iframe.ready(function() {

            if(isZipBook) {

                htmls = replaceAll(htmls,"src=\"", "src=\""+replaceStr);
            }
            else if(htmls.search('id="parent"') == -1){
                htmls = replaceAll(htmls,"src=\"", "src=\""+replaceStr);
             }
            if(resLink.indexOf(".")!=-1) {
                htmls = replaceAll(htmls, "href=\"", "href=\"" + replaceStr);
            }
            htmls =  replaceAll(htmls,"\"../Styles/", "\""+replaceStr+"../Styles/");
            if(isZipBook) htmls = replaceAll(htmls,"data=\"", "data=\""+replaceStr );
            htmls = replaceAll(htmls,"src: url[(]\"", "src: url[(]\""+replaceStr);
            htmls = replaceAll(htmls,"background-image: url[(]'", "background-image: url[(]'"+replaceStr);
            // htmls = htmls.replace(/\\\\/g , '\\');
            // htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');

            if(isZipBook) {

                //move the content to left side
                $('#htmlreadingcontent').css({
                    'max-width' : '100%',
                    'padding' : '16px 48px'
                });
                $('.text-format').hide();
            } else {
                $('.text-format').show();
            }

            var __replaceHref = "";

            document.getElementById('htmlreadingcontent').innerHTML = '<div id="htmlContent" style="margin: 0 auto;"><div class="ptyyy">'+htmls+'</div></div>';
            const ytEmbed = document.querySelector('#htmlContent .ytEmbed_helpIframe')
            if(ytEmbed){
                const embedurl = ytEmbed.getAttribute("src")
                if(embedurl.includes("https://www.youtube.com/embed/")){
                    const updatedeURL = embedurl.split("OEBPS/")[1]
                    ytEmbed.setAttribute("src",updatedeURL)
                }
            }

            if(siteId==12||siteId==23||siteId==24) {

                $("#htmlreadingcontent").find("a").each(function() {

                    __replaceHref = $(this).attr('href');

                    if(__replaceHref){
                        if(__replaceHref.split('#').length > 1){
                            $(this).attr('href',"#"+__replaceHref.split('#')[1]);

                        }
                        else{
                            $(this).attr('href',"#");
                        }
                    }
                });
                if($('#htmlreadingcontent').hasClass('zaza')){
                    $('#htmlContent').find('.Basic-Paragraph.ParaOverride-1').addClass('fontInherit');
                    $('#htmlContent').find('div._idGenObjectLayout-1A').addClass('moveleft');
                    $('#htmlContent').find('#_idContainer010 > .Basic-Paragraph.ParaOverride-1.fontInherit').addClass('headerOverride');
                    $('#htmlContent').find('#_idContainer006 > .Basic-Paragraph.ParaOverride-1.fontInherit').addClass('handbookoverride');
                    $('#htmlContent').find('#_idContainer001 > .Basic-Paragraph.ParaOverride-1.fontInherit > .CharOverride-6').addClass('bulksales');
                    $('#htmlContent').find('.table-responsive').addClass('red');
                    $('#htmlContent').find('table').each(function () {
                        $('table').wrap('<div class="table-responsives">');
                    });
                }


            }


            if(!isZipBook) {
                renderMathInElement(document.body);
            }
            $('#htmlreadingcontent').find('table').each(function () {
                $('table').wrap('<div class="table-responsive">');
            });

        });


        loadAnnotator(readId);
        $("#htmlreadingcontent").show();

        $(".contentEdit #formatMenu, .contentEdit #notesMenu").show();
    }

    function displaySplittedEpubs(data,resId){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        const chapterIdsArr = []
        const chaptersListItems = document.querySelectorAll('.read-book-chapters-wrapper li')
        chaptersListItems.forEach(li=>{
            const id = li.id.split('chapterName')[1]
            chapterIdsArr.push(Number(id))
        })
        const lazyLoaderHTMl = "<div class='lazyLoader' style='top: 75px !important;'><div class='anim'><div class='slider-anim'><div class='lottieLoader'></div><div style='margin-top: 12px;'> <p style='font-size: 16px;'>Please wait while loading your book...</p></div></div></div></div>"
        let navToolHTML = '<div class=\'epub_nav\'>' +
            '<div class=\'d-flex align-items-center wrap-1\'>' +
                '<div class=\'d-flex align-items-center\' >' +
                    '<button class=\'epub_nav-backBtn\' title=\'Back to resources\'>' +
                        '<i class=\'fa-solid fa-arrow-left-long\'></i>' +
                    '</button>';
                if(!previewMode && genericReader){
                    navToolHTML+='<div id="navLeftItem"></div>';
                }
        navToolHTML+='</div>' +
            '</div>' +
            '<div class=\'d-flex align-items-center wrap-2\'>' +
                '<button class=\'btn epubHighlightsBtn d-flex align-items-center\' id=\'epubHighlights\'>' +
                    '<i class=\'fa-solid fa-file-pen\'></i>' +
                '</button>';
            if (!previewMode){
                navToolHTML+= '<div class=\'d-flex align-items-center\'>' +
                                '<button class=\'btn navBtns\' id=\'prevBtnEpub\' title=\'Previous Page\'>' +
                                    '<i class=\'fa-solid fa-left-long\'></i></button>' +
                                '<button class=\'btn navBtns\' id=\'nextBtnEpub\' title=\'Next Page\'>' +
                                    '<i class=\'fa-solid fa-right-long\'></i>' +
                                '</button>' +
                            '</div>';
            }
        navToolHTML+='</div>' +
            '</div>';
        const iframeHTML = "<iframe id='readerFrameTwo' src=\""+serverPath+"/resources/epubReader?bookId=${params.bookId}&resId="+resId+"&chapterId="+masterChapterID+"\" width='100%' style='height: 100vh;border:none'></iframe>"

        const footerToolHTMl = '<div class="epub_footer">' +
                '<div id="previousChapter" style="visibility: hidden">' +
                '</div>' +
            '<div class="ePubFooterWrap" style="width:auto!important;">' +
                '<div class="d-flex justify-content-center align-items-center">' +
                    '<p>Page <span class="epub_footer-pageNum" id="footerPageno"></span>' +
                    ' of <span class="epub_footer-totalPages" id="epubFooterTotalPages"></span>' +
                    '</p>' +
                '</div>' +
            '<div class="" style="display: none">' +
                '<input type="range" id="epubProgressSlider" max="100" min="1" step="1" class="epubProgressSlider">' +
            '</div>' +
        '</div>' +
            '<div id="nextChapter" style="visibility: hidden"></div>' +
            '</div>'
        var epubNotesElement = "<section class='notesWrapper displayOpenClose' style='z-index: 4'>"+
            "<div class='notesDisplay'>"+
            "<div class='notesDisplay_header'>"+
            "<h3 style='width: 100%;text-align: center'>Highlights</h3>"+
            "<button class='close'><i class='fa-solid fa-xmark'></i></button>"+
            "</div>"+
            "<div class='notesDisplay_body'>"+
            "<ul class='notesList'></ul>"+
            "</div>"+
            "</div>"+
            "</section>";

        const readerHTMl = lazyLoaderHTMl+navToolHTML+epubNotesElement+iframeHTML+footerToolHTMl
        $("#reader").html(readerHTMl);

        document.getElementById('reader').classList.add('readerAnimation');
        document.getElementById('reader').classList.remove('readerAnimationClose');
        document.getElementById('reader').style.height='100%'
        $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').hide();
        $('.add-tabs').removeClass('d-lg-flex').addClass('d-none');
        document.querySelector('body').removeAttribute('style');
        document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: hidden;');

        document.querySelector('.epub_nav-backBtn').addEventListener('click',function (){
            saveEpubDetails(resIdVal,1,masterChapterID);
            if(genericReader){
                history.back();
            }else {
                if ($(window).width() < 767) {
                    $('header,.related-book-wrapper').show();
                } else {
                    $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').show();
                }

                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
                document.getElementById('reader').classList.remove('readerAnimation');
                if ($(window).width() < 768) {
                    if (showResourceList) {
                        document.querySelector('.side-chapters').classList.add('d-none');
                    }
                }
            }
        });


        const nextBtnEpub = document.getElementById('nextBtnEpub')
        const prevBtnEpub = document.getElementById('prevBtnEpub')
        const footerPageno = document.getElementById('footerPageno')
        const epubHighlights = document.getElementById('epubHighlights')
        const navLeftItem = document.getElementById('navLeftItem')
        const epubFooterTotalPages = document.getElementById('epubFooterTotalPages')
    }
    function hideLazyLoader(){
        document.querySelector('.lazyLoader').classList.add('loaderHidden');
    }

    function showLazyLoader(){
        document.querySelector('.lazyLoader').classList.remove('loaderHidden');
    }
    //showing pdf documents
    function displayPdfReadingMaterial(pdfLink,allowDownload,id,zoomLevelTwo,resName){
        $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').hide();
        $('.add-tabs').removeClass('d-lg-flex').addClass('d-none');
        localStorage.setItem('lastReadPDF',JSON.stringify({resId:id,pdfOpen:true,chapterId:masterChapterID,bookId:${params.bookId}}))
        if(loggedInUser) {
            updateUserView(id,"all","read");
        }else {
            updateView(id,"all","read");
        }
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
            mobileView=true;
        }
        $('#allAddButton').hide();
        $('.subMenu').hide();
        $('.generateTest').hide();
        hideMainAndDisplayResource(id);
        if(allowDownload != "yes") {
            var pdfKey = "${encryptPdfKey}";
            var bookId=0;
            <% if(params.bookId == null || params.bookId == '') { %>
            bookId=urlBookId;
            <% } else {%>
            bookId="${params.bookId}";
            <%}%>

            //Zoom Level for yes no pdf
            if (zoomLevelTwo !=null || zoomLevelTwo !=""){
                if (zoomLevelTwo == "1"){
                    defaultZoom = 130;
                }else if (zoomLevelTwo == "2"){
                    defaultZoom = 160;
                }else if (zoomLevelTwo == "3"){
                    defaultZoom = 190;
                }

                if($(window).width()<768){
                    defaultZoom = 48;
                }
            }else {
                defaultZoom = 100;
            }
            var chapterId=chapterIdForPDF;
            bookTitle = resName;
            var knimbusInstUser = false
            <%if (session["userdetails"]!=null){%>
            var checkInstUseName = '${session["userdetails"].username}'
            if(checkInstUseName !="" && checkInstUseName.indexOf("_institute")>0){
                knimbusInstUser = true
            }
            <%}%>

            $("#reader").html("<iframe id='readerFrameTwo' src=\""+serverPath+"/resources/pdfReader?bookLang="+bookLang+"&resId="+id+"&bookId="+bookId+"&loggedInUser="+loggedInUser+"&bookUrl="+serverPath+"/funlearn/getPdfFile?resId="+id+"__encryptedKey="+pdfKey+"&chapterId="+masterChapterID+"&title="+bookTitle+"&knimbusInstUser="+knimbusInstUser+"&mobileView="+mobileView+"&bookGPT=false#zoom="+defaultZoom+"\" width='100%' style='height: 100vh'></iframe>");
            document.getElementById('reader').classList.add('readerAnimation');
            document.querySelector('body').removeAttribute('style');
            document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: hidden;');
            $("#notesnavigationsection").show();
            $("#notesnavigation").show();
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
        }else if(allowDownload == "yes") {
            displayDownloadablePdf(id);
        }
        if (bookLang != "English") {
            $('#htmlreadingcontent iframe').on('load', function () {
                var getIframe = $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .google-search-btn");
                getIframe.css("display", "none");
                $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("margin-right", "0px");
                $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("padding-right", "30px");
            });
        }
    }
    function getEncodedPdfDataUsingAjax(url,pdfLink,id){
        $.ajax({
            url : url,
            type : 'GET',
            tryCount : 0,
            retryLimit : 3,
            success : function(data) {
                joinEncodedStringPartsAndDisplay(data,pdfLink,id);
            },
            error : function(xhr, textStatus, errorThrown ) {
                this.tryCount++;
                if (this.tryCount <= this.retryLimit) {
                    //try again
                    $.ajax(this);
                    return;
                }else{
                    <g:remoteFunction controller="funlearn" action="getEncodedPdf" onSuccess="displayEncodedPdf(data)" params="'link='+pdfLink+'&id='+id" />
                }
            }
        });
    }

    function displayDownloadablePdf(id){
        var readerViewElement = document.getElementById('reader');
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
        }
        readerViewElement.style.height = '100vh';
        readerViewElement.classList.add('readerAnimation');
        var backButtonHtml = "<div class='mt-4'><button class='backBtn ml-3 dPdfBack' title='Back to Chapter' style='background: transparent;border: none;'><i class='material-icons-round'>keyboard_backspace</i></button></div>";
        document.getElementById('reader').innerHTML = "<div class='w-100 h-100'>"+backButtonHtml+"<embed src=\"/wonderpublish/downloadEncodedPdf?id="+id+"\" type=\"application/pdf\" width=\"100%\" class='mt-4 d-none d-lg-block h-100'>" +
            "<div class='text-center d-lg-none'><a target='_blank' href=\"/wonderpublish/downloadEncodedPdf?id="+id+"\" width=\"100%\" height=\"100%\" style=\"width: 100%; height: 100%;\" frameborder=\"0\" scrolling=\"no\" class='mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect download'>Download</a>"+
            "</div>"+
            "</div>";
        $("#htmlreadingcontent").show();
        // '<iframe id="fraDisabled" src="data:application/pdf;base64,'+resData.encodedString+'"+></iframe>' ;
        // '<object  style="position:relative" data="data:application/pdf;base64,'+resData.encodedString+'" type="application/pdf" ></object>' ;
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
        }
        window.componentHandler.upgradeDom();

        document.querySelector('.dPdfBack').addEventListener('click',function (){
            if ($(window).width() < 768){
                $('header,.related-book-wrapper').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
                $('.mobile-back-button').attr('onclick','mobileBackToChapters()');
            }else{
                $('header,.prepjoy_cta,.footer-menus,.related-book-wrapper').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
            }
            document.getElementById('reader').classList.remove('readerAnimation');
            $("#htmlreadingcontent").hide();
        })
    }
    function displayEncodedPdf(resData) {
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
        }
        var encodedPdfHtmlStr = "<div class='pdfbutton'>\n" +
            "    <button id=\"prev\">Previous</button>\n" +
            "    <span>Page: <span id=\"page_num\"></span> / <span id=\"page_count\"></span></span>\n"+
            "    <button id=\"next\">Next</button>\n" ;


        if(resData.allowDownload == "yes") encodedPdfHtmlStr = encodedPdfHtmlStr+"    <button onclick=\"downLoadEncodedPdf('"+resData.id+"')\">Download PDF</button>\n"
        encodedPdfHtmlStr = encodedPdfHtmlStr+  "</div>" +
            "<canvas id=\"the-canvas\" style='margin: 0 auto;'></canvas>";
        document.getElementById('htmlreadingcontent').innerHTML = setBackButton() + encodedPdfHtmlStr;
        $("#htmlreadingcontent").show();
        // '<iframe id="fraDisabled" src="data:application/pdf;base64,'+resData.encodedString+'"+></iframe>' ;
        // '<object  style="position:relative" data="data:application/pdf;base64,'+resData.encodedString+'" type="application/pdf" ></object>' ;
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
        }
        window.componentHandler.upgradeDom();
        // If absolute URL from the remote server is provided, configure the CORS
        // header on that server.

        var pdfData = atob(resData.encodedString);

        // var url = '/assets/wonderslate/Percentage1.pdf';

        // Loaded via <script> tag, create shortcut to access PDF.js exports.
        // var pdfjsLib = window['/assets'];

        // The workerSrc property shall be specified.
        // pdfjsLib.GlobalWorkerOptions.workerSrc = 'http://localhost:8080/assets/pdf.worker.js';

        var pdfDoc = null,
            pageNum = 1,
            pageRendering = false,
            pageNumPending = null,
            scale = 2,
            canvas = document.getElementById('the-canvas'),
            ctx = canvas.getContext('2d');

        /**
         * Get page info from document, resize canvas accordingly, and render page.
         * @param num Page number.
         */
        function renderPage(num) {
            pageRendering = true;
            // Using promise to fetch the page
            pdfDoc.getPage(num).then(function(page) {
                var viewport = page.getViewport({scale:scale});
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render PDF page into canvas context
                var renderContext = {
                    canvasContext: ctx,
                    viewport: viewport
                };
                var renderTask = page.render(renderContext);

                var containerWidth = $(".tab-content").width();

                // Wait for rendering to finish
                renderTask.promise.then(function() {
                    pageRendering = false;
                    if (pageNumPending !== null) {
                        // New page rendering is pending
                        renderPage(pageNumPending);
                        pageNumPending = null;
                    }
                });
            });

            // Update page counters
            document.getElementById('page_num').textContent = num;
        }

        /**
         * If another page rendering in progress, waits until the rendering is
         * finised. Otherwise, executes rendering immediately.
         */
        function queueRenderPage(num) {
            if (pageRendering) {
                pageNumPending = num;
            } else {
                renderPage(num);
            }
        }

        /**
         * Displays previous page.
         */
        function onPrevPage() {
            if (pageNum <= 1) {
                return;
            }
            pageNum--;
            queueRenderPage(pageNum);
        }
        document.getElementById('prev').addEventListener('click', onPrevPage);

        /**
         * Displays next page.
         */
        function onNextPage() {
            if (pageNum >= pdfDoc.numPages) {
                return;
            }
            pageNum++;
            queueRenderPage(pageNum);
        }
        document.getElementById('next').addEventListener('click', onNextPage);

        /**
         * Asynchronously downloads PDF.
         */
        pdfjsLib.getDocument({data: pdfData}).promise.then(function(pdfDoc_) {
            pdfDoc = pdfDoc_;
            document.getElementById('page_count').textContent = pdfDoc.numPages;

            // Initial/first page rendering
            renderPage(pageNum);
        }).catch(function(e) {
            //console.log(e);
        });
        $(document).on({
            "contextmenu": function(e) {
                //console.log("ctx menu button:", e.which);

                // Stop the context menu
                e.preventDefault();
            },
            "mousedown": function(e) {
                //console.log("normal mouse down:", e.which);
            },
            "mouseup": function(e) {
                //console.log("normal mouse up:");
                //console.log(e)
            }
        });

    }

    function joinEncodedStringPartsAndDisplay(resData,pdfLink,id){
        encodedStringPartsObj["'"+resData.requestNo+"'"] = resData.shortMessage;
        var checkAllParts = 1;
        for(var i=1;i<=10;i++){
            if(encodedStringPartsObj["'"+i+"'"] == undefined) checkAllParts = 0;
        }
        if(checkAllParts == 1) {
            var msg = "";
            for(var i=1;i<=10;i++){
                msg = msg + encodedStringPartsObj["'"+i+"'"];
            }
            displayEncodedPdf({
                "encodedString":msg,
                "id":resData.id,
                "pdfLink":pdfLink
            });
        }
    }

    function downLoadEncodedPdf(id){
        <g:remoteFunction controller="wonderpublish" action="downloadEncodedPdf"  params="'id='+id" />
    }

    //support functions
    function hideMainAndDisplayResource(id){
        showLoadingShimmer("htmlreadingcontent");
        readId = id;
    }
    function showLoadingShimmer(divId){
        var shimmerStr="<div class=\"container\">\n" +
            "                    <div class=\"content-wrapper mt-20\">\n" +
            "                        <div class=\"content row flexAlign\">\n" +
            "                            <photo class=\"shine col-md-3 col-sm-6 col-xs-12\" ></photo>\n" +
            "                        </div>\n" +
            "                    </div>\n" +
            "                    <br>";
        for(var i=0;i<9;i++){
            shimmerStr +="<div class=\"content-wrapper\">\n" +
                "                        <div class=\"content row flexAlign\">\n" +
                "                            <div class=\"line-wrapper col-md-6 col-sm-12 col-xs-12\">\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                                <lines class=\"shine\"></lines>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                    <br>";
        }
        shimmerStr +="</div>\n" +
            "            </div>";

        document.getElementById(divId).innerHTML = shimmerStr;

    }

    function changeColor(field) {
        $(field).siblings().removeClass('active');
        $(field).addClass('active');
        $('#book-read-material').removeClass().addClass('book-read-material'+' '+($(field).attr('id')));
    }

    function hideTextFormatter() {
        $('.color-mode-list-item').removeClass('active');
        $('#formatMenu').hide();
        $('#notesMenu').hide();
        $('.section-btns').hide();
        $('#book-read-material').removeClass('white-bg black-bg sepia-bg grey-bg').addClass('book-read-material'+' ');
    }

    function showTextFormatter() {
        $('.color-mode-list-item:first').addClass('active');
        $('#formatMenu').show();
        $('#notesMenu').show();
        $('.section-btns').show();
        if($(window).width()<767){
            $('#chapter-details-tabs').width('80%');
            $('.contentEdit').width('100px');
        }
    }

    function decreaseLineHeight() {
        $('#htmlreadingcontent').css({
            'line-height' : 'normal'
        });
    }

    function increaseLineHeight() {
        $('#htmlreadingcontent').css({
            'line-height' : '24px'
        });
    }

    function decreaseLetterSpacing() {
        $('#htmlreadingcontent').css({
            'letter-spacing' : 'normal'
        });
    }

    function increaseLetterSpacing() {
        $('#htmlreadingcontent').css({
            'letter-spacing' : '0.04em'
        });
    }

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    $('#notesMenu').on('click',function () {
        $('#overlay').removeClass('d-none');
        $('.export-notes').removeClass('d-none');
        $('#notesMenu').addClass('active');
        $('.subMenu').hide();

    });

    $('.close-notes').on('click',function () {
        $('#overlay').addClass('d-none');
        $('.export-notes').addClass('d-none');
        $('#notesMenu').removeClass('active');
        $('.subMenu').show();
    });

    <%if("1".equals(""+session["siteId"])){%>
    if ('matchMedia' in window) {
        // Chrome, Firefox, and IE 10 support mediaMatch listeners
        window.matchMedia('print').addListener(function(media) {
            console.log("pringgg",media);
            if (media.matches) {
                beforeBookPrint();
            } else {
                // Fires immediately, so wait for the first mouse movement
                $(document).one('mouseover', afterBookPrint);
            }
        });
    } else {
        // IE and Firefox fire before/after events
        $(window).on('beforeprint', beforeBookPrint);
        $(window).on('afterprint', afterBookPrint);
    }
    function beforeBookPrint() {
        $("#all").hide();
    }
    function afterBookPrint() {
        $("#all").show();
    }
    <%}%>

    function viewChapterNotesEpub(resId,bookID,chapterIdForPDF) {
        var paramsList  = ""

        paramsList  = 'limit=100&all_fields=1&uri='+resId+'&bookId='+bookID

        <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderChapterNotesEpub(data);' params="paramsList" />
    }

    function renderChapterNotesEpub(data){
        var notesData = data.rows;
        var notesHTML = "";

        if (notesData.length>0){
            for (var i=0;i<notesData.length;i++){
                var borderCol = "black";
                if (notesData[i].annotateColor!=undefined&&notesData[i].annotateColor!=null&&notesData[i].annotateColor!=""){
                    if (notesData[i].annotateColor == 'annotateColorYellow'){
                        borderCol = 'yellowBorder'
                    }else if(notesData[i].annotateColor=='annotateColorGreen'){
                        borderCol = 'greenBorder'
                    }else if(notesData[i].annotateColor == 'annotateColorBlue'){
                        borderCol = 'blueBorder'
                    }
                }
                if (notesData[i].text==null){
                    notesHTML+= "<li class='"+borderCol+"'>" +
                        "<p>Highlight</p>"+
                        "<a style='font-size: 16px'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                        "</li>";
                }else{
                    notesHTML+= "<li class='"+borderCol+"'>" +
                        "<p>Note</p>"+
                        "<a style='font-size: 16px'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                        "<p style='margin-left: 15px;margin-top: 10px;word-break: break-all;font-size: 15px'>"+notesData[i].text+"</p>"+
                        "</li>";
                }
            }
        } else{
            notesHTML+="No Highlights found."
        }
        document.querySelector('.notesList').innerHTML = notesHTML;
        epubOpenCloseNotesWindow();
    }

    function epubOpenCloseNotesWindow(){
        var notesWrapper = document.querySelector('.notesWrapper');
        notesWrapper.classList.toggle('openPopupWindow');
        notesWrapper.classList.toggle('displayOpenClose');
        if (notesWrapper.classList.contains('openPopupWindow')){
            notesWrapper.addEventListener('click',function (){
                epubOpenCloseNotesWindow()
            })
        }
    }

    function saveEpubDetails(resIdVal,epubPageNumber,chapteridVal){
        var lastReadDetailsArr = [];
        var oldLastReadDetails = localStorage.getItem('lastReadDetails');
        var lastReadDetailsObj = {
            resId:resIdVal,
            pageNo:epubPageNumber,
            chapterid:chapteridVal,
            bookId:'${params.bookId}'
        }

        //localStorage.setItem('lastReadPDF',JSON.stringify({resId:resIdVal,pdfOpen:false,chapterId:"${params.chapterId}"}))
        if (oldLastReadDetails){
            oldLastReadDetails =  JSON.parse(oldLastReadDetails);
            oldLastReadDetails.map(item=>{
                if (item.bookId == '${params.bookId}'){
                    item.pageNo = epubPageNumber;
                    item.chapterid = chapteridVal;
                    item.bookId = '${params.bookId}';
                    item.resId= resIdVal
                }else{
                    oldLastReadDetails.push(lastReadDetailsObj)
                }
            });
            oldLastReadDetails = Array.from(new Set(oldLastReadDetails.map(JSON.stringify))).map(JSON.parse);
            localStorage.setItem('lastReadDetails',JSON.stringify(oldLastReadDetails));
        }else{
            lastReadDetailsArr.push(lastReadDetailsObj)
            localStorage.setItem('lastReadDetails',JSON.stringify(lastReadDetailsArr));
        }
    }

    function openSpecificEPubPage(data,book,pageNo,chapterid){
        if (!genericReader){
            for(var b=0;b<book.spine.spineItems.length;b++){
                if(book.spine.spineItems[b].index == pageNo) {
                    if(epubChapterHref.indexOf(book.spine.spineItems[b].href) > -1) {
                        if(ebupChapterLink != undefined && ebupChapterLink != "" && ebupChapterLink != null ) ebupChapterLink = book.spine.spineItems[b].href;
                    }
                    currentSpineItem = book.spine.spineItems[b].href;
                    rendition.display(book.spine.spineItems[b].href.split("#")[0]);
                }
            }
        }else{
            getHtmlsDataByChapter(chapterid)
        }


        var epubPercentage = Math.round((100 * (pageNo+1) / book.spine.spineItems.length));
        document.querySelector('.epub_footer-pageNum').innerHTML = pageNo+1;
        document.querySelector('.epub_footer-percentage').innerHTML = '<span> &#x2022; </span>' + epubPercentage + ' %';
        document.getElementById('epubProgressSlider').value = epubPercentage;
    }

    function closePDFReaderView(){
        if ($(window).width() < 768){
            if (showResourceList){
                $('header,.related-book-wrapper,.prepjoy-footer').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
            }else{
                $('header,.related-book-wrapper,.prepjoy-footer').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
                $('.resource-contents > .tab-content,.add-tabs').removeClass('d-none');
                $('.side-chapters').addClass('d-none');
                $('#content-data-all').show().removeClass('d-none');
                $('.epub-action').hide();
            }
            $('.mobile-back-button').attr('onclick','mobileBackToChapters()');
        }else{
            $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').show();
            $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
        }
        document.getElementById('reader').classList.remove('readerAnimation');
        $("#htmlreadingcontent").hide();
        document.querySelector('body').removeAttribute('style');
        document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: auto;');
    }

    function openNextPDFResource(item){
        closePDFReaderView();
        if(item.sharing==null) {
            if (item.resLink.includes(".pdf")) {
               displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName,item.topicId);
            } else {
                displayReadingMaterial(item.id,item.resName);
            }
        }else{
            if (item.quizMode == "file") {
                if (item.resLink.includes(".pdf")) {
                    displayPDFNotes(item.id,item.resName);
                }else {
                    downloadFile(item.id);
                }
            } else {
                displayReadingMaterial(item.id,item.resName);
            }
        }
    }

    function getDropDownValue(value){
        getHtmlsDataByChapter(value);
    }

    function getNextElementValue(selectElement) {
        const selectedIndex = selectElement.selectedIndex;
        const nextIndex = selectedIndex + 1;

        if (nextIndex < selectElement.options.length) {
            return selectElement.options[nextIndex].value;
        }

        return null; // Next element does not exist
    }

    function getPreviousElementValue(selectElement) {
        const selectedIndex = selectElement.selectedIndex;
        const previousIndex = selectedIndex - 1;

        if (previousIndex >= 0) {
            return selectElement.options[previousIndex].value;
        }

        return null; // Previous element does not exist
    }

    function displayNextAndPreviousValues(selectElement) {
        const selectedIndex = selectElement.selectedIndex;

        // Get the value of the next element
        const nextValue = getNextElementValue(selectElement);
        if (nextValue !== null) {
            document.getElementById("nextChapter").style.visibility = 'visible';
            document.getElementById("nextChapter").innerHTML="<a href='javascript:getHtmlsDataByChapter("+nextValue+");' title='Next Chapter'>Next Chapter</a>";
        } else {
            document.getElementById("nextChapter").innerHTML="";
        }

        // Get the value of the previous element
        const previousValue = getPreviousElementValue(selectElement);
        if (previousValue !== null) {
            document.getElementById("previousChapter").style.visibility = 'visible';
            if (previewMode){
                document.getElementById("previousChapter").style.display = 'none';
            }
            document.getElementById("previousChapter").innerHTML="<a href='javascript:getHtmlsDataByChapter("+previousValue+");' title='Previous Chapter'>Previous Chapter</a>";
        } else {
            if (previewMode){
                document.getElementById("previousChapter").style.display = 'none';
            }
            document.getElementById("previousChapter").innerHTML="";
        }

    }

    function enableOnswipe(){
        var iframe = document.querySelector('iframe');
        var innerDoc = iframe.contentDocument || iframe.contentWindow.document;
        const pdfViewerContainer = innerDoc.body;
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;

        // Add touch event listeners to detect swipes
        pdfViewerContainer.addEventListener('touchstart', (event) => {
            touchStartX = event.touches[0].clientX;
            touchStartY = event.touches[0].clientY;
        });

        pdfViewerContainer.addEventListener('touchmove', (event) => {
            touchEndX = event.touches[0].clientX;
            touchEndY = event.touches[0].clientY;
        });

        pdfViewerContainer.addEventListener('touchend', handleSwipe);

        // Function to handle the swipe
        function handleSwipe() {
            const horizontalSwipeDistance = touchEndX - touchStartX;
            const verticalSwipeDistance = touchEndY - touchStartY;

            const swipeThreshold = 80;

            if (Math.abs(horizontalSwipeDistance) > Math.abs(verticalSwipeDistance)) {
                if (horizontalSwipeDistance > swipeThreshold) {
                    leftSwipeHandler();
                } else if (horizontalSwipeDistance < -swipeThreshold) {
                    rightSwipeHandler();
                }
            }
        }

        // Your swipe left handler function
        function leftSwipeHandler() {
            const selectElement = document.getElementById("chaptersList");
            const previousValue = getPreviousElementValue(selectElement);
            if (previousValue!=null && previousValue!=undefined){
                window.parent.getHtmlsDataByChapter(previousValue);
            }

        }

        // Your swipe right handler function
        function rightSwipeHandler() {
            const selectElement = document.getElementById("chaptersList");
            const nextValue = getNextElementValue(selectElement);
            if (nextValue!=null && nextValue!=undefined){
                window.parent.getHtmlsDataByChapter(nextValue);
            }

        }
    }
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && (event.key === 's' || event.key === '3' || event.key === '#')) {
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === '3') {
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        if((event.ctrlKey || event.metaKey && event.shiftKey)){
            document.body.style.background = "#000"
            document.body.innerHTML = "<div style='display: flex;flex-direction: column;justify-content: center;align-items: center'>" +
                "<h1 style='color: #fff'>Oops! Saving this webpage is not allowed.</h1> " +
                "<p style='color: #fff'>Reload the page if you're facing any issues</p>" +
                "</div>";
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
</script>

