package com.wonderslate
import com.wonderslate.usermanagement.MailManagementService



class TaskActivitiesUpdateJob {
	def mailManagementService
    static triggers = {
		cron name: 'cronTrigger', cronExpression: "0 30 01 * * ?" // every day at 6pm
		//simple repeatInterval: 5000l // execute job once in 5 seconds
    }

    def execute() {
        // execute job
       mailManagementService.activitiesUpdate()
       log.info("Activity update email sent on ${new Date()}")		
    }
}
