@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

.store {
  .tab-content {
    .tab-pane {
      >.d-flex.justify-content-between.align-items-center{
        position: absolute;
        top:0;
        width: 100%;
        left: 0;
        padding-left: 20px;
      }
      > div {
        .custom-select-ws {
          width: 160px;
          @media @iPhone{
            width:200px;
            margin-right: 20px;
          }
          .select-items {
            z-index: 9999;
          }
          .select-selected {
            border: 1px solid @ws-border;
            border-radius: 4px;
            font-family: @ws-header-font;
            font-size: @ws-menu-fontSize - 2;
            &:before {
              content: 'Sort by :';
              color: fade(@ws-darkBlack, 50%);
              font-weight: normal;
              margin-right: 5px;
              padding-left: 10px;
            }
          }
        }
        > h3 {
          font-size: @ws-header-fontSize + 2;
          font-weight: @ws-header-fontWeight;
          color: @ws-darkBlack;
          font-family: @ws-header-font;
          span {
            color: fade(@ws-darkBlack, 50%);
            font-weight: normal;
          }
        }
      }
    }
  }
  .topSchoolBooks {
   &:hover{
     background: @ws-white;
     border-radius: 6px;
     box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
     transition: all 0.5s;
     @media @iPad-landscape{
     width: 152px;
   }
     @media @iPad-portrait{
       width: 152px;
     }
   }
    >a{
      &:hover{
        text-decoration: none;
      }
    }
    padding:10px;
    padding-bottom: 0;
    .image-wrapper {
      >a{
        &:hover{
          text-decoration: none;
        }
      }
      width: 132px;
      height: 165px;
      position: relative;
      z-index: 99;
      img {
        width: 132px;
        height: 165px;
        position: relative;
        border-radius: 4px;
        box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      }
      h3 {
        position: absolute;
        font-size: @ws-menu-fontSize - 4;
        font-weight: @ws-header-fontWeight;
        color: @ws-white;
        background-color: @ws-lightOrange;
        padding: 7px 14px;
        bottom: 32px;
        left: -6px;
        margin-bottom: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        &:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0;
          top: 100%;
          border-width: 2px 3px;
          border-style: solid;
          border-color: @ws-darkOrange @ws-darkOrange transparent transparent;
        }

      }
    }
    .content-wrapper {
      height: 113px;
      margin-top: 8px;
      a:hover{
        text-decoration: none;
      }
      h3 {
        height: 43px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3; /* number of lines to show */
        -webkit-box-orient: vertical;
      }
      h3, p {
        font-size: @ws-menu-fontSize - 2;
        font-family: @ws-header-font;
        color: @ws-darkBlack;
        font-weight: normal;
        margin-bottom: 0;
        width: 132px;
      }
      p {
        &.sub-name {
          font-weight: @ws-header-fontWeight;
          font-size: @ws-menu-fontSize - 4;
          margin-bottom: 0.5rem;
          span {
            color: fade(@ws-darkBlack, 40%);
            font-size: @ws-menu-fontSize - 6;
            margin-right: 4px;
          }
        }
        &.complete {
          color: fade(@ws-darkBlack, 40%);
          font-size: @ws-menu-fontSize - 6;
        }
        &.price {
          color: @ws-red;
          font-size: @ws-menu-fontSize;
          font-family: @ws-banner-font;
          span {
            font-size: @ws-menu-fontSize - 4;
            text-decoration: line-through;
            color: fade(@ws-darkBlack, 60%);
            margin-left: 5px;
          }
        }
      }
      @media @iPhone {
        width: 132px;
      }
      .price-tag{
        p{
          width: 100%;
        }
      }
    }
  }
}
.test-wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover{
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    transition: all 0.5s;
    text-decoration: none;
  }
  .content-wrapper{
    padding: 0.8rem 5px;
    h3{
      font-size: @ws-menu-fontSize - 6;
      font-family: @ws-header-font;
      font-weight: @ws-header-fontWeight;
      color:fade(@ws-darkBlack,40%);
      text-transform: uppercase;
    }
    p{
      font-size: @ws-menu-fontSize - 2;
      color:@ws-red;
      &.sub-name{
        font-size: 10px;
        color:@ws-darkBlack;
        font-weight: @ws-header-fontWeight;
        span{
          color:fade(@ws-darkBlack,72%);
          margin-right:0.3rem;
          font-weight: normal;
        }
      }

    }
  }
  .card{
    padding: 0.8rem 0.8rem;
    border: 0.5px solid rgba(0, 0, 0, 0.4);
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    background: @ws-white;
    width: 132px;
    min-height: 165px;
    margin-top: 0.8rem;

    h4{
      font-size: @ws-menu-fontSize - 2;
      color:@ws-darkBlack;
      font-family: @ws-header-font;
      font-weight: @ws-header-fontWeight;
      text-align: center;
    }
    >p{
      color:fade(@ws-darkBlack,40%);
      font-size: @ws-menu-fontSize - 6;
      font-family: @ws-header-font;
      font-weight: @ws-header-fontWeight;
      text-align: center;
      margin: 0;
    }
    h2{
      font-size: @ws-menu-fontSize;
      color:@ws-darkBlack;
      font-family: @ws-banner-font;
      font-weight: @ws-header-fontWeight;
      text-align: center;
    }
    .test-content{
      >div{
        width: 50%;
      }
      p{
        margin: 0;
        font-size: 12px;
        font-family: @ws-banner-font;
        color:@ws-darkBlack;
        text-align: center;
        &.language{
          font-family: @ws-header-font;
        }
      }
      span{
        color:fade(@ws-darkBlack,40%);
        font-size: @ws-menu-fontSize - 6;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        display: block;
        text-align: center;
        text-transform: uppercase;
      }
    }
  }
}
.butn-wrappers{
  .btn-rank{
    border:1px solid #F79420;
    font-size: 14px;
    color:#F79420;
    display: block;
    margin-bottom: 0.5rem;
  }
  .btn-attempt{
    background: linear-gradient(94.13deg, #3AE878 13.47%, #30C465 73.92%);
    color:@ws-white;
    display: block;
  }
}