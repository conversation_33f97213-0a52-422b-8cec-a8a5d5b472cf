<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <meta name="keywords" content="${keywords}">
    <meta name="description" content="Wonderslate is an effort to bring syllabus specific educational content to all.Engaging parents, teachers and students to create and share content. Be it mind maps, videos , quiz, solved question paper etc. CBSE,ICSE,state boards etc.">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />

    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto Slab:300,400,700' rel='stylesheet' type='text/css'>

    <asset:stylesheet href="hopscotch-0.1.1.css"/>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="flat-ui.css"/>
    <asset:stylesheet href="demo.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>
    <asset:stylesheet href="flipbook.style.css"/>
    <asset:stylesheet href="font-awesome.css"/>
    <ckeditor:resources />
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));

%>

<style>
.modal.modal-wide .modal-dialog {
    width: 90%;

}
.show-on-hover:hover > div.dropdown-menu {
    display: block;
    width: 500px !important;
}
.wrapper {
    min-height: 100%;
    height: auto !important;
    height: 100%;
    margin: 0 auto -3em;
}
.footer, .push {
    height: 3em;
}
</style>
<body>

<nav class="navbar navbar-default navbar-fixed-top" style="background-color: white">
    <div class="container-fluid navbarfirstline">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <sec:ifNotLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}" id="addingcontent2">WONDERPUBLISH</a></span>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}">Visvesvaraya Technological University</a></span>



            </sec:ifLoggedIn>

        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <div class="row row-right">
                <ul class="nav navbar-nav navbar-right top-nav">
                    <sec:ifNotLoggedIn>
                        <li><a href="javascript:showregister('login');">&nbsp;Sign in&nbsp;</a></li>
                        <li><a href="javascript:showregister('signup');">&nbsp;Register&nbsp;</a></li>
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>

                        <li><a href="/books/books">My Library</a></li>
                        <li id="notification" class="dropdown"><a href="#"><i class="fa fa-bell-o fa-x"></i></a></li>
                        <li><g:link uri="/logoff">&nbsp;Logout&nbsp;&nbsp;&nbsp;&nbsp;</g:link></li>
                    </sec:ifLoggedIn>
                </ul>

            </div>
        </div>
    </div>
    <div class="popoverhome" style="display:none"></div>

    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->


</nav>
<body>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>

<!--<div>-->
<div >
    <div class="container-fluid" >
        <div class="row wpbluebackground"><br><br>
            <div class="col-md-2"><h4 ><br><a href="/books/vtulibrary" class="whitetext"><i class="fa fa-arrow-left fa-x"></i> MY LIBRARY</a></h4></div>
            <div class="col-md-6 col-md-offset-3"><h3 class="whitetext">${bookTitle}</h3></div>
        </div>



        <br>
    </div>
    <div class="container-fluid" >
        <div  class='row maincontent' id="bookdtl">
            <div class="col-md-1 ">


            </div>

            <div class='col-md-10 '>
                <div id="content-books">
                    <div class="row"><br>
                        <div class="col-md-5 col-md-offset-1"><div class="whitebackground boxifynobordernoheight greytext"><br>&nbsp; <i class="fa fa-hand-o-right fa-x"></i> Click on the chapter box to open the chapter. <br><br></div></div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 col-md-offset-1">
                            <div id="chapters">

                            </div>
                        </div>
                    </div><BR>

                    <div class="row main">
                        <div class="col-md-1 text-center"></div>
                        <div class="col-md-10">
                            <div id="content-data-qanda" class="row">
                                <div class='col-md-10 col-md-offset-1'><div class='text-center'><br><h4><i class="fa fa-group fa-x"></i>&nbsp; DISCUSSION FORUM</h4><br></div></div>
                            </div>
                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><input type="text" class="form-control" placeholder="Ask Question/Doubt?"> </div>
                            </div><br>
                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><p class="fontsize16"><b>Questions</b></p>
                                </div>

                            </div>

                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><p class="fontsize16">Is velocity and speed are the same? Thanks.</p>
                                </div>

                            </div>
                            <div class="row  smallerText greytext">
                                <div class='col-md-5 col-md-offset-1'><i class="fa fa-question-circle fa-x green"></i>&nbsp;141 votes&nbsp;<i class="fa fa-chevron-up fa-x"></i>&nbsp;<i class="fa fa-chevron-down fa-x"></i> </p>
                                </div>
                                <div class='col-md-5 text-right'> 2 months ago by <span class="dark1text">Soumitra Bose</span></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class='col-md-9 col-md-offset-2 greytext'><hr>Velocity is speed in a given direction. Speed is just how fast something is going.</p>
                                </div>
                            </div>
                            <div class="row smallerText greytext">
                                <div class='col-md-4 col-md-offset-2'><i class="fa fa-check-circle fa-x green"></i>&nbsp;344 votes&nbsp;<i class="fa fa-chevron-up fa-x"></i>&nbsp;<i class="fa fa-chevron-down fa-x"></i> </p>
                                </div>
                                <div class='col-md-5 text-right'> 2 months ago by <span class="dark1text">Chandan</span></p>
                                </div>
                            </div>
                            <div class="row  dark5text">
                                <div class='col-md-9 col-md-offset-2'><b>Show all 12 answers&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Answer this question</b>
                                </div>

                            </div>
                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><hr>
                                </div>
                            </div>


                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><p class="fontsize16">What makes acceleration a vector quantity in this example?</p>
                                </div>

                            </div>
                            <div class="row  smallerText greytext">
                                <div class='col-md-5 col-md-offset-1'><i class="fa fa-question-circle fa-x green"></i>&nbsp;11 votes&nbsp;<i class="fa fa-chevron-up fa-x"></i>&nbsp;<i class="fa fa-chevron-down fa-x"></i> </p>
                                </div>
                                <div class='col-md-5 text-right'> 3 months ago by <span class="dark1text">Gagan</span></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class='col-md-9 col-md-offset-2 greytext'><hr>If i gave you a number for acceleration, what does that tell you?
                                I'm accelerating at 13 m/s^2. Which direction am i traveling in the first place? Will my acceleration totally oppose my motion, help increase, the motion, or neither?
                                You need some information on the direction of the acceleration to understand where in space the motion is changing. However, In a 1 dimensional example you do not need acceleration to be a vector. You can only go in 2 directions, + and -. so if you are used to doin 1-d problems, like you do the first week of the physics course, you do not need vector valued functions to analyze motion. </p>
                                </div>
                            </div>
                            <div class="row smallerText greytext">
                                <div class='col-md-4 col-md-offset-2'><i class="fa fa-check-circle fa-x green"></i>&nbsp;125 votes&nbsp;<i class="fa fa-chevron-up fa-x"></i>&nbsp;<i class="fa fa-chevron-down fa-x"></i> </p>
                                </div>
                                <div class='col-md-5 text-right'> 2 months ago by <span class="dark1text">Aditi Anand</span></p>
                                </div>
                            </div>
                            <div class="row  dark5text">
                                <div class='col-md-9 col-md-offset-2'><b>Show all 4 answers&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Answer this question</b>
                                </div>

                            </div>
                            <div class="row">
                                <div class='col-md-10 col-md-offset-1'><hr>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>

        </div>

    </div>
    <div class="push"></div>

</div>
<div id="lightbook">

</div>


<g:render template="/funlearn/wpfooter"></g:render>
<!--</div>-->
<script>
    var pageType='book';
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>

<asset:javascript src="pageflip/three.js"/>
<asset:javascript src="pageflip/flipbook.webgl.js"/>
<asset:javascript src="pageflip/pdf.js"/>
<asset:javascript src="pageflip/compatibility.js"/>
<asset:javascript src="pageflip/pdf.worker.js"/>
<asset:javascript src="pageflip/jquery.touchSwipe.js"/>
<asset:javascript src="pageflip/share.js"/>
<asset:javascript src="pageflip/flipbook.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>



<g:render template="/funlearn/topicscripts"></g:render>
<script>

    function changeSem(){

        var htmlStr = "<div class='row'><div class='col-md-12 text-center'><span class='numberCircle'>1</span><span class='numberCircle'>2</span><span class='numberCircle'>3</span>" +
                "<span class='numberCircle selected'><a href=''>4</a></span></div></div> ";

        document.getElementById("semchange").innerHTML = htmlStr;
        $('#semchange').toggle(1000);
    }
    function getBooksList(getBooksList){

        <g:remoteFunction controller="books" action="getChaptersList"  onSuccess='displayBooks(data);'
                params="'bookId=${params.bookId}'" />
    }


    function displayBooks(data){
        var chapters = data.results;
        var randomColor,randomViews,randomHours,randomMinutes;
        var htmlStr="<br><br> <div class='row'>";
        var rowclosed=false;
        var columnCount=0;
        for(var i = 0; i <chapters.length; ++i){


                randomColor = columnCount % 10;
                randomViews = Math.floor(Math.random() * 32) + 1;
                randomHours = Math.floor(Math.random() * 50) + 1;
                randomMinutes = Math.floor(Math.random() * 59) + 1;
                columnCount++;
                htmlStr += " <div class='col-md-3 text-center'>" +
                        "           <a href='javascript:showBook(${params.bookId},"+chapters[i].id+","+chapters[i].noOfPages+")'>  <div class='boxifychapter'><div class='row'><div class='col-md-11 text-right  greytext'>Chapter&nbsp;"+(i+1)+" </div></div>                        <div class='boxifychapterbody dark" + randomColor + " whitetext'>" +
                        "                                            <div class='boxifytop light" + randomColor + " wpbooktitle'><br><br><b>" + chapters[i].topicName + "</b></div>" +
                        "                                        <br><div class='row smallText'><div class='col-md-11 col-md-offset-1 text-left'><i class='fa fa-eye fa-x'></i>&nbsp;" + randomViews + "&nbsp;&nbsp;" +
                        " <i class='fa fa-clock-o fa-x'></i>&nbsp;" + randomHours + "h&nbsp;" + randomMinutes + "m </div></div></div></div>" +
                        "                                 </a>  </div>";
                // logic to close the row and create a new one
                if ((columnCount) % 4 == 0 && i < chapters.length) htmlStr += "</div><br><br> <div class='row'>";


        }
        htmlStr+="</div>";
        document.getElementById("chapters").innerHTML=htmlStr;
        //$("#content-books").css("display","block");
    }

    getBooksList();
    //getTopicsMap('topic');
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>


</body>
</html>