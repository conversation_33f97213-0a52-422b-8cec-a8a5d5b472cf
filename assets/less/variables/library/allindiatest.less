@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

#allindiatest {
.testFilter {
  background: @ws-white;
  h3 {
    font-size: @ws-menu-fontSize;
    color: fade(@ws-darkBlack, 50%);
    border-bottom: 0.5px solid fade(@ws-darkBlack, 50%);
    font-weight: normal;
    font-family: @ws-header-font;
    padding: 0.7rem 0.5rem;
  }
  ul {
    padding: 0.5rem 1.5rem;
    list-style: none; counter-reset: li;
    li {
      padding: 0rem 2px;

      a {
        color: @ws-darkBlack;
        counter-increment: li;
        &.active {
          color: @ws-lightOrange;
          &:before{
            color:@ws-lightOrange;
          }
        }
        font-size: @ws-header-fontSize;
        font-family: @ws-header-font;
        &:before{
          content: counter(li);
          width: 1em;
          margin-left: -1em;
          font-size: @ws-header-fontSize;
          font-family: @ws-header-font;
          color: @ws-darkBlack;
          position: relative;
          left: -1rem;
        }
      }

    }
  }
}
  .test-wrapper {
    [data-state="live"]{
         .card{
           .livelabel{
             display: block;
           }
           .btn-wrappers{
             &.startTest{
               display: block;
             }
             &.showRank{
               display: block;
               .btn-test{
                 display: none;
               }
             }
           }
         }
    }
    [data-state="showrankonly"]{
      .card{
        .btn-wrappers{
          &.showRank{
            display: block;
            .btn-test{
              display: none;
            }
          }

        }
      }
    }
    [data-state="showrank"]{
      .card{
        .btn-wrappers{
          &.showRank{
            display: block;
          }

        }
      }
    }

    [data-state="registered"]{
      .card{
        .btn-wrappers{
          &.registered{
            display: block;
          }
        }
      }
    }
    &:hover {
      box-shadow: none;
      background: none;
    }
    .card {
      background: @ws-white;
      .test-content{
        >div{
          width:auto;
        }
        p,span{
          text-align: left;
        }
      }
      padding: 1rem 0;
      width: 152px;
      .livelabel{
        width: 27px;
        height:10px;
        text-align: center;
        background: @ws-red;
        border-radius: 2px;
        display: none;
        font-size: 6px;
        color:@ws-white;
        float: right;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        text-transform: uppercase;
        position: absolute;
        top:5px;
        right:10px;
      }
      > h4 {
        font-size: @ws-menu-fontSize;
        padding: 0;
      }
      min-height: 192px;
      height: auto;
      .btn-wrappers {
        text-align: center;
        &.startTest,&.showRank,&.registered{
          display: none;
        }
        .btn-test {
          background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
          color: @ws-white;
          width: 130px;
          font-size: @ws-menu-fontSize - 2;
          font-family: @ws-header-font;
        }
        .btn-ranks{
          .btn-test;
          background: none;
          border:1px solid @ws-lightOrange;
          color:@ws-lightOrange;
          margin-bottom: 0.5rem;

        }
        .btn-registers{
          .btn-test;
          background: rgba(94, 199, 215, 0.6);
          display: flex;
          justify-content: center;
          margin: 0 auto;
          i{
            font-size: 16px;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

.ml-8{
  margin-left: -8px;
}