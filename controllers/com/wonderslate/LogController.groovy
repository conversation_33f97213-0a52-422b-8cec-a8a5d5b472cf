package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BannersMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.groups.GroupsService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.log.AffiliationLog
import com.wonderslate.log.AffliationLinkLog
import com.wonderslate.log.AppVersion
import com.wonderslate.log.BooksViewDtl
import com.wonderslate.log.ContactFormDtl
import com.wonderslate.log.DeviceInformation
import com.wonderslate.log.FeedbackDtl
import com.wonderslate.log.FeedbackMst
import com.wonderslate.log.FlashCardTimeLog
import com.wonderslate.log.NotificationDtl
import com.wonderslate.log.QuizIssues
import com.wonderslate.log.ResourceView
import com.wonderslate.log.SmartbannerDtl
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.LogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.UserLog
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.MailManagementService
import com.wonderslate.usermanagement.UserRole
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.rest.client.RestBuilder
import org.apache.commons.io.FileUtils
import org.springframework.security.core.context.SecurityContextHolder
import pl.touk.excel.export.WebXlsxExporter

//import pl.touk.excel.export.WebXlsxExporter
import pl.touk.excel.export.XlsxExporter
import groovy.sql.Sql
import grails.transaction.Transactional

import javax.imageio.ImageIO
import java.awt.image.BufferedImage
import java.text.SimpleDateFormat

class LogController {
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    MailManagementService mailManagementService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    AsyncLogsService asyncLogsService
    UtilService utilService
    def redisService
    def rememberMeServices
    LogsService logsService
    GroupsService groupsService


    def index() {}

    @Transactional
    def updateCount(){
        //Do not use, till more clarity on how to do is emerged - AA
        /**    String sql = "update ResourceDtl set "+params.columnName+"="+params.columnName+"+1 where id="+params.id;
         ResourceDtl.executeUpdate(sql);

         if(springSecurityService.currentUser!=null){
         if("noOfViews".equals(params.columnName)){
         userManagementService.addOncePoints(springSecurityService.currentUser.username,"LP","VIEW",params.id);

         ResourceView resourceView =  new ResourceView(resourceDtlId: new Integer(params.id),username:springSecurityService.currentUser.username,action:"view");
         resourceView.save(flush: true, failOnError: true);
         } else if ("noOfLikes".equals(params.columnName)){
         ResourceView resourceView = ResourceView.findByResourceDtlIdAndUsernameAndAction(new Integer(params.id),springSecurityService.currentUser.username,"like")
         if(resourceView==null){
         resourceView =  new ResourceView(resourceDtlId: new Integer(params.id),username:springSecurityService.currentUser.username,action:"like");
         resourceView.save(flush: true, failOnError: true);

         def rd = ResourceDtl.findById(new Integer(params.id));
         if(rd!=null) {
         userManagementService.addPoints(rd.createdBy,"SP","LIKE",params.id);
         }
         }
         } else if ("noOfFavourites".equals(params.columnName)){
         ResourceView resourceView = ResourceView.findByResourceDtlIdAndUsernameAndAction(new Integer(params.id),springSecurityService.currentUser.username,"favourite")
         if(resourceView==null){
         resourceView =  new ResourceView(resourceDtlId: new Integer(params.id),username:springSecurityService.currentUser.username,action:"favourite");
         resourceView.save(flush: true, failOnError: true);
         }
         }
         }*/
        def json = ["status":"OK"]
        render json as JSON

    }
    @Transactional
    def updateView(){
        String logEnabled = dataProviderService.isResourceLoggingEnabled()
        if("true".equals(logEnabled)) {
            if (params.chapterId != null) {
                asyncLogsService.addResViewChapter(new Long(params.chapterId),null,"view",params.source,params.fromTab,params.viewedFrom)

            } else {
                Integer duration = null
                if(params.duration!=null) duration  = new Integer(params.duration)
                asyncLogsService.addResView(new Long(params.id),null,"view",params.source,params.fromTab,params.viewedFrom,utilService.getSiteId(request,session),duration)
            }
        }
        def json = ["status":"OK"]
        render json as JSON
    }
    @Transactional @Secured(['ROLE_USER'])
    def updateUserView(){

        String logEnabled = dataProviderService.isResourceLoggingEnabled()
       if("true".equals(logEnabled)) {
            if (params.chapterId != null) {
                asyncLogsService.addResViewChapter(new Long(params.chapterId),springSecurityService.currentUser.username,"view",params.source,params.fromTab,params.viewedFrom)
            } else {
                Integer duration = null
                if(params.duration!=null) duration  = new Integer(params.duration)
                asyncLogsService.addResView(new Long(params.id),springSecurityService.currentUser.username,"view",params.source,params.fromTab,params.viewedFrom,
                        utilService.getSiteId(request,session),duration)
                //this shouldn't be here.. should be move to quizrecorder place
                asyncLogsService.updateUsageList(new Long(params.id),springSecurityService.currentUser.username)
            }
        }
        def json = ["status":"OK"]
        render json as JSON
    }

  @Secured(['ROLE_USER']) @Transactional
    def getResViewByUser(){

        def json = ["userLog":logsService.getResViewByUser(springSecurityService.currentUser.username,Integer.parseInt(params.batchIndex))]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def addQuizIssue(){
        QuizIssues quizIssues = new QuizIssues(objectiveMstId: new Long(params.id),issuesList: params.issuesList,
                issue: params.issue,username: springSecurityService.currentUser.username)
        quizIssues.save(failOnError: true, flush:true)

        def json = ["status":"OK"]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeBookFromLibrary(){
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        def status = "Deleted"
        if(params.bookId!=null){
            BooksViewDtl.executeUpdate("delete from BooksViewDtl where bookId=" + params.bookId+" and username='"+springSecurityService.currentUser.username+"'")
            BooksPermission.executeUpdate("delete BooksPermission where bookId=" + params.bookId+" and username='"+springSecurityService.currentUser.username+"'and batch_id is null")
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            new Sql(dataSource).execute("DELETE from chapter_access WHERE book_id IN ("+params.bookId+") and username='"+springSecurityService.currentUser.username+"'")
            if(siteMst.allBooksLibrary=="true" || getSiteId(request)==25) dataProviderService.getBooksListForUser(springSecurityService.currentUser.username)
            else redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
            dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
        }else{
            status="Not deleted"
        }
        def json = ['status':status]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeResource(){
        def status = "Deleted"
        if(params.resId!=null){
            ResourceDtl resourceDtl = ResourceDtl.findByIdAndCreatedBy(new Long(params.resId),springSecurityService.currentUser.username)
            if(resourceDtl!=null){
                resourceDtl.chapterId = new Long(resourceDtl.chapterId.intValue()*-1)
                resourceDtl.save(flush:true,failOnError: true)
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }else{
                status="Not deleted"
            }
        }else{
            status="Not deleted"
        }
        def json = ['status':status]
        render json as JSON
    }

    @Transactional
    def getLatestAppVersion(){
        AppVersion appVersion = dataProviderService.getLatestAppVersion(params.appType, new Long(params.siteId))
        if(redisService.("latestNotificationId_"+params.siteId)==null) dataProviderService.getLatestNotifications(params.siteId)
        def json = ['appVersion':appVersion, latestNotificationId: redisService.("latestNotificationId_"+params.siteId)]
        render json as JSON
    }

    @Transactional
    def insertDeviceId(){
        DeviceInformation deviceInformation = DeviceInformation.findByDeviceId(params.deviceId)
        if(deviceInformation==null&&springSecurityService.currentUser!=null){
            deviceInformation =  new DeviceInformation(deviceId:params.deviceId,
                    username:springSecurityService.currentUser.username)
        }else if(springSecurityService.currentUser!=null){
            //updating username
            deviceInformation.username = springSecurityService.currentUser.username
        }
        def status
        if(deviceInformation!=null) {
            deviceInformation.save(failOnError: true, flush:true)
            status = "updated"
        }else{
            status = "Device information not added"
        }
        def json =  [
                'status':  status
        ]

        render json as JSON
    }

    @Transactional
    def loggedOutFromDevice(){
        DeviceInformation deviceInformation = DeviceInformation.findByDeviceId(params.deviceId)
        if(deviceInformation!=null){
            deviceInformation.delete(flush:true)
        }
        def json =  [
                'status':  "loggedout"
        ]

        render json as JSON

    }

    @Transactional
    def deleteNotification(){
        NotificationDtl notificationDtl = NotificationDtl.findById(new Long(params.id))
        if(notificationDtl!=null){
            notificationDtl.status="deleted"
            notificationDtl.deletedBy = springSecurityService.currentUser.username
            notificationDtl.dateDeleted = new Date()
            notificationDtl.save(flush: true, failOnError: true)
            dataNotificationService.sendDeleteNotification(notificationDtl)
        }
        def json = ["result":"success"]
        render json as JSON
    }

    @Transactional
    def sendNotification(){
        def file = request.getFile('file')
        String messageTo=""
        def siteId = utilService.getSiteId(request,session)
        if("singleuser".equals(params.userMode)) {
            messageTo = siteId+"_"+params.username
        }
        else if("batch".equals(params.userMode)) messageTo = String.join(",", params.batches);
        else if("book".equals(params.userMode)) messageTo = params.bookId

        NotificationDtl notificationDtl = new NotificationDtl(title: (""+params.title).replaceAll('"',""),body:(""+params.body).replaceAll('"',""),createdBy: springSecurityService.currentUser.username,
                siteId: siteId, link:params.link,sendTo: messageTo,messageType:params.userMode)
        notificationDtl.save(flush: true, failOnError: true);



        if (file&&!file.empty) {
            String filename = file.originalFilename;
            filename = filename.replaceAll("\\s+", "")
            File uploadDir = new File("upload/notifications/" + notificationDtl.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
            notificationDtl.imageUrl = filename
            notificationDtl.save(flush: true, failOnError: true);
        }
        if("singleuser".equals(params.userMode)){
            dataNotificationService.sendNotificationToUser(notificationDtl,messageTo)
        }
        else if("batch".equals(params.userMode)) {
            String batchIds
            if("java.lang.String".equals((params.batches).getClass().getName())) {

                batchIds = params.batches
            }
            else {
                String[] arr = params.batches
                batchIds = String.join(",", arr)
            }
            dataNotificationService.sendNotificationToBatch(notificationDtl,batchIds)
        }
        else if("book".equals(params.userMode)) {

            dataNotificationService.sendNotificationToBook(notificationDtl,params.bookId)
        }
        else if("all".equals(params.userMode)) {
            dataNotificationService.sendNotificationToAll(notificationDtl)
        }

        dataProviderService.getLatestNotifications(getSiteId(request))
        redirect(action: 'notification', params: ['notificationSent': "true"])
    }

    @Secured(['ROLE_NOTIFICATION']) @Transactional
    def notification(){
        if(session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }

        def instituteIds = []

        List instituteUserDtl = InstituteUserDtl.findAllByUsername(springSecurityService.currentUser.username)
        instituteUserDtl.each{ institute ->
            instituteIds << institute.instituteId

        }
        if(instituteIds.size()>0) {
            List batchesList = CourseBatchesDtl.findAllByStatusAndConductedForInList("active", instituteIds,[sort:"name" ])
            List institutes = InstituteMst.findAllByIdInList(instituteIds,[sort:"name" ])


            [batchesList: batchesList, institutes: institutes]
        }else [batchesList: [], institutes: []]
    }

    @Transactional
    def liveVideoAutoNotifications(){

        String sql ="SELECT rd.id,date_add(rd.test_start_date,INTERVAL '5:30' HOUR_MINUTE) test_start_date,rd.chapter_id,cm.name,bm.id book_id,bm.site_id,rd.resource_name,bm.status,rd.res_type,bm.title,rd.res_deep_link" +
                " FROM wscontent.resource_dtl rd, wscontent.chapters_mst cm, wscontent.books_mst bm\n" +
                " where rd.res_type='Reference Videos' and rd.test_start_date is not null and rd.test_start_date > sysdate() \n" +
                " and rd.test_start_date < date_add(sysdate(),INTERVAL '0:09' HOUR_MINUTE) and cm.id=rd.chapter_id and bm.id=cm.book_id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String notificationsSent = ""
        String notificationTitle
        String notificationBody
        String link=""
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy h:mm a");
        results.each{ liveClasses ->

            if(NotificationDtl.findByResId(liveClasses.id)==null){
                link = liveClasses.res_deep_link ? liveClasses.res_deep_link : getDeepLink(liveClasses.chapter_id, liveClasses.id, liveClasses.res_type, liveClasses.book_id, liveClasses.site_id)
                notificationsSent += liveClasses.id + ", "
                //first notification is at book level
                notificationTitle = "Attend live session at " + dateFormat.format(liveClasses.test_start_date)
                notificationBody = liveClasses.title + "\n" + liveClasses.name + " \n" + liveClasses.resource_name + "\n Click the link below to attend the live session \n " + link

                NotificationDtl notificationDtl = new NotificationDtl(title: notificationTitle, body: notificationBody, createdBy: "System",
                        siteId: liveClasses.site_id, link: link, sendTo: "" + liveClasses.book_id, messageType: "book", resId: liveClasses.id)
                notificationDtl.save(flush: true, failOnError: true);

                dataNotificationService.sendNotificationToBook(notificationDtl, "" + liveClasses.book_id)

                //now get the batches
                sql = "SELECT GROUP_CONCAT(bbd.batch_id) batch_ids  FROM wsuser.books_batch_dtl bbd,wsuser.course_batches_dtl cbd " +
                        "where bbd.book_id=" + liveClasses.book_id + " and cbd.id=bbd.batch_id and cbd.status='active'"
                dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                sql1 = new Sql(dataSource)
                def batches = sql1.rows(sql)

                if (batches.size() > 0) {
                    notificationBody = liveClasses.title + " \n" + liveClasses.name + " \n" + liveClasses.resource_name + "\n ."

                    notificationDtl = new NotificationDtl(title: notificationTitle, body: notificationBody, createdBy: "System",
                            siteId: liveClasses.site_id, link: link, sendTo: "" + batches[0][0], messageType: "batch", resId: liveClasses.id)
                    notificationDtl.save(flush: true, failOnError: true);
                    dataNotificationService.sendNotificationToBatch(notificationDtl, "" + batches[0][0])
                }

            }
            dataProviderService.getLatestNotifications(getSiteId(request))
            render "Notifications sent to following resIds " + notificationsSent
        }
    }



    @Secured(['ROLE_NOTIFICATION']) @Transactional
    def deleteLiveVideoAutoNotifications(){
        String sql ="SELECT rd.id,date_add(rd.test_start_date,INTERVAL '5:30' HOUR_MINUTE) test_start_date,rd.chapter_id,cm.name,bm.id book_id,bm.site_id,rd.resource_name,bm.status,rd.res_type,bm.title" +
                " FROM wscontent.resource_dtl rd, wscontent.chapters_mst cm, wscontent.books_mst bm\n" +
                " where rd.id="+params.resId+" and cm.id=rd.chapter_id and bm.id=cm.book_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String notificationsSent = ""
        String notificationTitle
        String notificationBody
        String link=""
        SimpleDateFormat dateFormat = new SimpleDateFormat("h:mm a");
        results.each{ liveClasses ->

            if(NotificationDtl.findByResId(liveClasses.id)==null||"delete".equals(params.mode)){
                link = getDeepLink(liveClasses.chapter_id,liveClasses.id,liveClasses.res_type,liveClasses.book_id,liveClasses.site_id)
                notificationsSent += liveClasses.id+", "

                if("delete".equals(params.mode)){
                    notificationTitle = "Live session at "+dateFormat.format(liveClasses.test_start_date)+ " has been cancelled"
                    notificationBody = liveClasses.title+"\n"+liveClasses.name+" \n"+liveClasses.resource_name+"\n"
                }
                NotificationDtl notificationDtl = new NotificationDtl(title: notificationTitle,body:notificationBody,createdBy: "System",
                        siteId: liveClasses.site_id, link:"delete".equals(params.mode)?"":link,sendTo: ""+liveClasses.book_id,messageType:"book",resId: liveClasses.id)
                notificationDtl.save(flush: true, failOnError: true);

                dataNotificationService.sendNotificationToBook(notificationDtl,""+liveClasses.book_id)

                //now get the batches
                sql = "SELECT bbd.batch_id,cbd.name FROM wsuser.books_batch_dtl bbd,wsuser.course_batches_dtl cbd " +
                        "where bbd.book_id="+liveClasses.book_id+" and cbd.id=bbd.batch_id and cbd.status='active'"
                dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                sql1 = new Sql(dataSource)
                def batches = sql1.rows(sql)

                batches.each{ batch ->
                    notificationBody = liveClasses.title+" \n"+liveClasses.name+" \n"+liveClasses.resource_name+"\n ."

                    notificationDtl = new NotificationDtl(title: notificationTitle,body:notificationBody,createdBy: "System",
                            siteId: liveClasses.site_id, link:"delete".equals(params.mode)?"":link,sendTo: ""+batch.batch_id,messageType:"batch",resId: liveClasses.id )
                    notificationDtl.save(flush: true, failOnError: true);
                    dataNotificationService.sendNotificationToBatch(notificationDtl,""+batch.batch_id)
                }
                dataProviderService.getLatestNotifications(getSiteId(request))
            }

        }
        render "Notifications sent to following resIds "+notificationsSent
    }


    def getDeepLink(chapterId,Long resId,resType,bookId,siteId){
        def json = getDeepLinkDetails(chapterId,resId,resType,bookId,siteId)
        if(json.shortLink == null) {
            json = getDeepLinkDetails(chapterId,resId,resType,bookId,siteId)
            if(json.shortLink == null) {
                try {
                    userManagementService.sendMmail("<EMAIL>", "Wonderslate <<EMAIL>>", "Anirudha", "Deep link creation Failed",
                            "Error while generating deeplink Reference ResId is " + resId + " and BookId is " + bookId + " on server " + request.getServerName())
                    userManagementService.sendMmail("<EMAIL>", "Wonderslate <<EMAIL>>", "Sujan", "Deep link creation Failed",
                            "Error while generating deeplink Reference ResId is " + resId + "  and BookId is " + bookId + " on server " + request.getServerName())
                } catch (Exception e) {
                    println "sending deeplink email failed " + e.toString()
                }
                return json.shortLink
            }else{
                return json.shortLink
            }
        }
        else{
            return json.shortLink
        }

    }



    def getDeepLinkDetails(chapterId,Long resId,resType,bookId,siteId){
        SiteMst siteMst
        if(siteId!=null) {
            siteMst = dataProviderService.getSiteMst(siteId)
        }
        def firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
        def parameters="bookId="+bookId;

        if(chapterId!=null) parameters +="&chapterId="+chapterId;
        if(resId!=null) parameters +="&resId="+resId;
        if(resType!=null) parameters +="&resType="+resType;
        def params = "{" +
                "\"dynamicLinkInfo\": {" +
                " \"domainUriPrefix\":\""+siteMst.domainUriPrefix+"\"," +
                " \"androidInfo\": {" +
                "\"androidPackageName\":\""+siteMst.androidPackageName+"\"" +
                " }," +
                " \"iosInfo\":{\"iosBundleId\": \""+siteMst.iosBundleId+"\"" +
                "}," +
                "\"link\":\""+("true".equals(siteMst.appOnly)?siteMst.siteBaseUrl:siteMst.siteBaseUrl+"/wonderpublish/bookdtl?"+parameters+"\"") +
                "}," +
                "\"suffix\":{" +
                "\"option\":\"SHORT\"" +
                "}" +
                "}"

        RestBuilder rest = new RestBuilder()


        def resp = rest.post('https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey) {
            accept("application/json")
            contentType("application/json")
            body(params)
        }
        def json = resp.json

        if(json.shortLink != null && resId!=null) {
            ResourceDtl resourceDtl = ResourceDtl.findById(new Long(resId))
            resourceDtl.resDeepLink = json.shortLink
            resourceDtl.save(flush: true, failOnError: true);
        }

        return json
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER','ROLE_CUSTOMER_SUPPORT'])
    def userManagement(){

    }

    @Transactional
    def getUsers(){
        Integer siteId = getSiteId(request)
            String sql = "select u.name,u.email, u.mobile, u.id, u.username,u.state"+
                    " from user u where site_id="+siteId+"";
            if("mobile".equals(params.userMode)) {
                sql += " and  mobile='"+ params.userValue+"'";
            }
           else {
                sql +=  " and email='"+params.userValue+"'";
             }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List userList = results.each {user ->
            return [ name: user[0], email: user[1], mobile: user[2], id: user[3],username: user[4],old: user[5]]
        }
        def json = [status:userList?"OK":"Not present" ,userList: userList]
        render json as JSON
    }

    @Transactional
    def forceLogout() {
        User user = User.findById(params.userId)
        dataNotificationService.sendLogoutNotificationToUser(user.username,getSiteId(request),null)
        List appTokens = AuthenticationToken.findAllByUsername(user.username)
        appTokens.each{appToken ->
            appToken.delete(flush:true)
        }

        //remove the device Ids
        List deviceIds = DeviceInformation.findAllByUsername(user.username)
        deviceIds.each{device ->
            device.delete(flush:true)

        }
        def json = ['status':'deleted']
        render json as JSON
    }


    def quizissues() {
    }

    def exportQuizIssue(){
        String sql = "select qi.objective_mst_id,qi.issue, qi.issues_list, qi.username, DATE_FORMAT(DATE_ADD(qi.date_created, INTERVAL '5:30' HOUR_MINUTE),'%d-%m-%Y %H:%i:%S %p') date_created"+
                " from wslog.quiz_issues qi"+
                " where date(DATE_ADD(qi.date_created, INTERVAL '5:30' HOUR_MINUTE))=date(DATE_ADD(SYSDATE(),interval -1 day))";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        if(results!=null && results.size()>0) {
            List data = results.collect {comp ->
                sql = "select bm.title, cm.name, rd.resource_name"+
                        " from objective_mst om,resource_dtl rd,chapters_mst cm,books_mst bm "+
                        " where om.id="+comp[0]+" and om.quiz_id=rd.res_link and rd.chapter_id=cm.id and cm.book_id=bm.id";
                def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
                def sql2 = new Sql(dataSource1)
                def results1 = sql2.rows(sql)
                return [ title: results1[0].title, chapterDesc: results1[0].name, resourceName: results1[0].resource_name,  issue: comp[1], issuesList: comp[2],
                         username: comp[3], dateCreated: comp[4]
                ]
            }

            List headers = ["Book Name", "Chapter Name", "Quiz Name", "Quiz Issue", "Issue List", "Reported By", "Reported At"]
            List withProperties = ["title", "chapterDesc", "resourceName", "issue", "issuesList", "username", "dateCreated"]
            def fileName = "QuizIssuesReport_"+(new Random()).nextInt(9999999)+".xlsx"

            new XlsxExporter(grailsApplication.config.grails.basedir.path+'/tmp/'+fileName).fillHeader(headers).add(data, withProperties).save()

            mailManagementService.emailQuizIssueReport("Support Team", grailsApplication.config.grails.mail.default.from,
                    grailsApplication.config.grails.appServer.siteName, grailsApplication.config.grails.basedir.path+'/tmp/'+fileName)
        }

        render ""
    }
    def issueList() {
        String sql = "select qi.id,qi.objective_mst_id,qi.status," +
                " qi.issue, qi.issues_list, qi.username, DATE_FORMAT(DATE_ADD(qi.date_created, INTERVAL '5:30' HOUR_MINUTE),'%d-%m-%Y %H:%i:%S %p') date_created,qi.id" +
                " from quiz_issues qi";
        if(params.actionMode!=null && "active".equals(params.actionMode)){
            sql += " where  qi.status is null " ;
        }else {
            sql +=  " where  qi.status='completed' " ;
        }
        sql +=   " order by qi.id";


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List  quizlist =null

        if (results != null && results.size() > 0) {

            quizlist = results.collect { comp ->
                sql = "select bm.title, cm.name, rd.resource_name"+
                        " from objective_mst om,resource_dtl rd,chapters_mst cm,books_mst bm "+
                        " where om.id="+comp[1]+" and om.quiz_id=rd.res_link and rd.chapter_id=cm.id and cm.book_id=bm.id and bm.publisher_id='"+session["userdetails"].publisherId+"'";
                def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
                def sql2 = new Sql(dataSource1)
                def results1 = sql2.rows(sql)
                if (results1 != null && results1.size() > 0) {
                    return [id      : comp[0], title: results1[0].title, status: comp[2], resourceName: results1[0].resource_name, issues: comp[3], issuesLists: comp[4],
                            username: comp[5], dateCreated: comp[6]
                    ]
                }
            }
            while (quizlist.remove(null)) {
            }


        }
        def json = [status:quizlist ? "OK" : "Not present", quizlist: quizlist]
        render json as JSON
    }

    @Transactional
    def issueUpdate(){
        QuizIssues quiz=QuizIssues.findById(new Long(params.id))
        quiz.status="completed"
        quiz.save(failOnError: true, flush: true)
        def json = ["status":"success"]
        render json as JSON
    }

    @Transactional
    def smartbannerdltadd(){
        SmartbannerDtl smartbannerDtl
        if("test".equals(params.actionMode)){
            smartbannerDtl = new SmartbannerDtl(userName: springSecurityService != null && springSecurityService.currentUser != null?springSecurityService.currentUser.username:null, status: "click")
        }else {
            smartbannerDtl = new SmartbannerDtl(userName: springSecurityService != null && springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null, status: "closed")
        }
        smartbannerDtl.save(failOnError: true, flush: true)
        def json = ["status":"success"]
        render json as JSON
    }

    def appVersionManagement(){
        List sites = SiteMst.list([sort: "clientName", order: "asc"])
        [sites:sites]
    }

    @Transactional
    def getAppVersionDetails()
    {
        AppVersion appVersion = AppVersion.findByAppTypeAndSiteId(params.appType,new Long(params.siteId))
        def json = [status:appVersion!=null?"OK":"Not present",appVersion: appVersion]
        render json as JSON
    }

    @Transactional
    def updateAppVersion(){
        AppVersion appVersion = AppVersion.findByAppTypeAndSiteId(params.appType,new Long(params.siteId))
        if(appVersion==null)
        {
            appVersion =  new AppVersion(appType: params.appType,siteId: new Long(params.siteId),username:springSecurityService.currentUser.username )
        }

        appVersion.appVersion = params.appVersion
        appVersion.versionNo = new Integer(params.versionNo)
        appVersion.details = params.details
        appVersion.save(failOnError: true,flush: true)

        def json = ["status":"OK"]
        render json as JSON

    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def migrateuser(){
    }

    @Transactional
    def getUserAccess(){
        Integer siteId =  getSiteId(request)
        String username=siteId+"_"+params.userValue
        User user = User.findByUsername(username)
        def json
        if(user!=null){
        def temp = [ name: user.name, email:user.email, mobile:user.mobile, id:user.id ,commentsBlocked:user.commentsBlocked, username:user.username ]
         List userList = new ArrayList()
          userList.add(temp)
             json =[
                     status:"OK",userList: userList
            ]
        }else {
            json = [status:"Not present"]
        }
        render json as JSON
    }

    @Transactional
    def getUserRole(){
        List userList1;
        String sql=""
        if(params.userId!=null && !"".equals(params.userId)) {
            sql = "select ur.role_id,ur.user_id"+
                    " from user_role ur where  ur.user_id="+params.userId+"";
        }else{
            sql = "select ur.authority,ur.id"+
                    " from role ur where ur.authority not in ('ROLE_ALL_SALES')";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List userList = results.each {user ->
            return [ authority: user[0], id: user[1]]
        }
        if(params.userId!=null && !"".equals(params.userId)){
            userList1 = userList;
        }else{
            userList1 = userList.each {user ->
                return [ name:user[0],id:user[1]]
            }
        }
        def json = [status:userList?"OK":"Not present" ,userList: userList1]
        render json as JSON
    }

    @Transactional
    def deleteandUpdateUserRole(){
        if(params.userId!=null){
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            new Sql(dataSource).execute("DELETE from wsuser.user_role WHERE user_id IN ("+params.userId+")")
            String access =params.accessds;
            String [] accessrights=access.split(",")
            def accessId="";
            for(int i=0;i<accessrights.length;i++){
                accessId  =accessrights[i];
                if(accessId.length()>0) {
                    new Sql(dataSource).execute("INSERT INTO wsuser.user_role (role_id, user_id)  VALUES (" + accessId + "," + params.userId + ")");
                }
            }
        }
        def json = ["status":"OK"]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def userAccess(){

    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def unblockUser(){

    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def UnblockUserComments(){
        User user = null
        def status;
        if(params.username!=null){
            user = dataProviderService.getUserMst(params.username)
        }
        if(user!=null) {
            user.commentsBlocked = "false"
            user.save(failOnError: true, flush: true)
            status="OK";
        }
        def json = ["status":status]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER','ROLE_CLIENT_ORDER_MANAGER'])
    def deleteUserBooks(){

    }



    @Transactional
    def userBooksDetails() {
        List users;
        def userlist;
        Integer siteId = getSiteId(request)
        users = User.findAllByUsername( siteId + "_" + params.userValue)
        if(users.size()>0){
            userlist="OK"
        }
        String sql = "select bq.username,bq.book_id, bm.title " +
                " from wsuser.books_permission bq,books_mst bm" +
                " where bq.book_id=bm.id and bq.username='" + siteId + "_"+params.userValue+"'"+
                " order by bq.book_id";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List  booklist =null
        if (results != null && results.size() > 0) {
            booklist = results.collect { comp ->
                return [ username : comp[0], id   : comp[1], title: comp[2]

                ]
            }
        }

        def json = [status:booklist ? "OK" : "Not present", booklist: booklist,
                    userlist:userlist]
        render json as JSON
    }

    @Transactional
    def deleteBooksforUser() {
        Integer siteId =  getSiteId(request)
        BooksPermission.executeUpdate("delete BooksPermission where bookId=" + params.bookId+" and username='"+params.userName+"'")
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        new Sql(dataSource).execute("DELETE from chapter_access WHERE book_id IN ("+params.bookId+") and username='"+params.userName+"'")
        dataProviderService.getBooksListForUser(params.userName);
        if(siteId.intValue()==1 || siteId.intValue()==25 || siteId.intValue()==3) redisService.("userShelfBooks_"+params.userName) = null
        def json = ['status':'deleted']
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def liveTestRanks() {

    }
    @Transactional
    def affiliationLog(){
        new AffiliationLog(bookId: new Long(params.bookId),
                username:springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
                .save(flush: true, failOnError: true);

    }

    @Transactional
    def getLatestNotifications()
    {
        Integer siteId =  getSiteId(request)
        if(redisService.("newNotifications_"+siteId)==null) dataProviderService.getLatestNotifications(siteId)

        //if no new notifications is created in last two days
        if(redisService.("newNotifications_"+siteId)==null) redisService.("newNotifications_"+siteId) = "NothingNew"

        if(redisService.("userBatchIds_"+springSecurityService.currentUser.username)==null) dataProviderService.getUserBatchesIds(springSecurityService.currentUser.username)
        if(redisService.(springSecurityService.currentUser.username+"_"+"booksList")==null) {
            dataProviderService.getBooksListForUser()
        }
        def json = ['latestNotifications':redisService.("newNotifications_"+siteId),
                    latestNotificationId:redisService.("latestNotificationId_"+siteId),
                    userBatches:redisService.("userBatchIds_"+springSecurityService.currentUser.username),
                    'bookIds' : redisService.(springSecurityService.currentUser.username+"_"+"bookIds")]
        render json as JSON
    }

    def addUserGradeDtl(){

    }

    def getUserGradesDtl(){

    }
    @Transactional
    def login(){
        boolean autologin=false
        String username,winCode,loginId
        String password
        String authenticationTokenString
        Integer siteId = getSiteId(request)
        def json
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            password=jsonObj.password
            username=jsonObj.username

        } else {
            password=params.password
            username=params.username

        }

       if("true".equals(params.autoLogin)){
         loginId=params.loginId
         byte[] decodedBytes = Base64.getDecoder().decode(loginId);
         String decodedString = new String(decodedBytes);
         winCode  = decodedString.substring(3,decodedString.length()-3)
         username = User.findByWin(winCode).username
         if(username!=null) autologin=true
        }

            if(!username.startsWith(""+siteId+"_")) username = ""+siteId+"_"+username

        User user = User.findByUsername(username)
        if(user==null) println("username not found")
        if (user!=null&&(autologin ||springSecurityService.passwordEncoder.isPasswordValid(user.password, password, null))) {
            try {
                springSecurityService.reauthenticate(user.username)
                //valid user

                //first do the force logout of already logged in users
                UUID gfg = UUID.randomUUID();
                 authenticationTokenString = gfg.toString()
                if ("web".equals(params.source)) {
                    //web login

                    //later add the simultaneous login check here
                    def authentication = SecurityContextHolder.context.authentication
                    rememberMeServices.loginSuccess(request, response, authentication)
                    userManagementService.registerUserLogin(username, session.getId())
                    session["userdetails"] = user
                } else {
                    //mobile login
                    dataNotificationService.sendLogoutNotificationToUser(user.username, getSiteId(request), authenticationTokenString)
                    List appTokens = AuthenticationToken.findAllByUsername(user.username)
                    appTokens.each { appToken ->
                        appToken.delete(flush: true)
                    }

                    //remove the device Ids
                    List deviceIds = DeviceInformation.findAllByUsername(user.username)
                    deviceIds.each { device ->
                        device.delete(flush: true)

                    }

                    AuthenticationToken authenticationToken = new AuthenticationToken(username: user.username, token: authenticationTokenString)
                    authenticationToken.save(failOnError: true, flush: true)
                }
                UserLog log = new UserLog(username: user.username, action: "login")
                log.save(failOnError: true, flush: true)
            } catch (Exception e) {
                   println " login  failed " + e.toString()
                 }
             json = ["username":user.username,"access_token":authenticationTokenString,
                        "roles":springSecurityService.getPrincipal().authorities*.authority,
                        "status":"ok"]
        }else{
            //password fail
             json = ["status":"Failed"]

        }
        render json as JSON

    }

    @Transactional
    def appLogin(){
        String username
        String password
        String authenticationTokenString
        Integer siteId = getSiteId(request)

        def json
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            password=jsonObj.password
            username=jsonObj.username

        } else {
            password=params.password
            username=params.username

        }

        if(!username.startsWith(""+siteId+"_")) username = ""+siteId+"_"+username

        User user = User.findByUsername(username)

        if (user!=null&&springSecurityService.passwordEncoder.isPasswordValid(user.password, password, null)) {
            try {
                springSecurityService.reauthenticate(user.username)
                //valid user

                //first do the force logout of already logged in users
                UUID gfg = UUID.randomUUID();
                authenticationTokenString = gfg.toString()

                    //mobile login
                     dataNotificationService.sendLogoutNotificationToUser(user.username, getSiteId(request), authenticationTokenString)
                    List appTokens = AuthenticationToken.findAllByUsername(user.username)
                    appTokens.each { appToken ->
                        appToken.delete(flush: true)
                    }

                    //remove the device Ids
                    List deviceIds = DeviceInformation.findAllByUsername(user.username)
                    deviceIds.each { device ->
                        device.delete(flush: true)

                    }

                    AuthenticationToken authenticationToken = new AuthenticationToken(username: user.username, token: authenticationTokenString)
                    authenticationToken.save(failOnError: true, flush: true)

                UserLog log = new UserLog(username: user.username, action: "login")
                log.save(failOnError: true, flush: true)
            } catch (Exception e) {
                println " login  failed " + e.toString()
            }

                String ipAddress  = params.ipAddress
                List instituteDetails = userManagementService.getInstitutesForUser(siteId,ipAddress)
                SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                String subject = null
                if("true".equals(params.subject)) subject="true"

                if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

                if(redisService.("catalogStructure_"+siteId)==null||redisService.("catalogStructure_subject_"+siteId)==null) {
                    String siteIdList = siteId.toString()


                    if (siteId.intValue() == 1) {
                        if (redisService.("siteIdList_" + siteId) == null) {
                            dataProviderService.getSiteIdList(siteId)
                        }

                        siteIdList = redisService.("siteIdList_" + siteId)
                    }
                    dataProviderService.getDefaultCategoryStructure(siteId,siteIdList,subject)
                }

                def catalogStructure = redisService.("catalogStructure_"+siteId)
                if(subject!=null) catalogStructure = redisService.("catalogStructure_subject_"+siteId)

                def profileDetails =[
                        'name': user.name,
                        'email': ("<EMAIL>".equals(user.email)?"":user.email),
                        'mobile': ("mobile".equals(user.mobile)?"":user.mobile),
                        'profilePic':user.profilepic,
                        'id':user.id,
                        'state':user.state,
                        'district':user.district,
                        'schoolOrCollege':user.school,
                        'country':user.country,
                        'pincode':user.pincode,
                        'who':"student"
                ]
                json = ["username": user.username, "access_token": authenticationTokenString,
                        "roles"   : springSecurityService.getPrincipal().authorities*.authority,
                        'institutes' : instituteDetails ? instituteDetails : "Nothing present",
                        'userSelectedPreference':userManagementService.getUserGrades(user.username),
                        'profileDetails':profileDetails,
                        'defaultCategoryStructure': catalogStructure,
                        'classes':groupsService.getMyGroupsList(params),
                        "status"  : "ok"]

        }else{
            //password fail
            json = ["status":"Failed"]

        }
        render json as JSON

    }

    def getLatestNotificationId(){
        Integer siteId = getSiteId(request)
        if(redisService.("latestNotificationId_"+siteId)==null) dataProviderService.getLatestNotifications(siteId)
        def json = [latestNotificationId: redisService.("latestNotificationId_"+siteId)]
        render json as JSON
    }

    @Transactional
    def getMainAndFeaturedCategories(){
        Integer siteId = getSiteId(request)
        if(params.level==null) {
            if (redisService.("maincategories_" + siteId) == null) dataProviderService.getMainCategories(siteId,null)
        }
        else{
            if (redisService.("maincategories_" + siteId+"_"+params.level.replaceAll("\\s+","")) == null) dataProviderService.getMainCategories(siteId,params.level)
        }
        if(redisService.("featuredcategories_"+siteId)==null) dataProviderService.getFeaturedCategories(siteId)
        def mainCategories
        if(params.level==null)  mainCategories = redisService.("maincategories_"+siteId)
        else   mainCategories = redisService.("maincategories_"+siteId+"_"+params.level.replaceAll("\\s+",""))

        def featuredCategories = redisService.("featuredcategories_"+siteId)
        def json = [mainCategories: mainCategories, featuredCategories: featuredCategories]
        render json as JSON
    }

    def getIntroVideos(){
        if(redisService.("introVideos"+siteId)==null) dataProviderService.getIntroVideos()
        def json = [videos: redisService.("introVideos")]
        render json as JSON
    }

    def getDetailsForDeepLink(){
        def bookDetails
        def bookId = params.bookId
        def resId = params.resId
        def resourceDetails
        if(resId!=null){
            if(redisService.("deepLinkResourceDtl_" + resId)){
                dataProviderService.getSingleResourceDetails(resId)
            }
            resourceDetails = dataProviderService.getSingleResourceDetails(resId)

        }
        if(bookId==null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))

            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            bookId = ""+chaptersMst.bookId
        }
        if (redisService.("deepLinkChapterDetails_" + bookId) == null) {
            dataProviderService.getBookDetails(bookId)
        }
        bookDetails = redisService.("deepLinkChapterDetails_" + bookId)

        def json = ['resourceDetails':resourceDetails,'bookDetails':bookDetails]
        render json as JSON
    }

    Integer getSiteId(request){
        Integer siteId = new Integer(1)
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }

    @Secured(['ROLE_NOTIFICATION']) @Transactional
    def notificationManagement(){}

    @Secured(['ROLE_NOTIFICATION']) @Transactional
    def notificationDetails(){
        Integer siteId = getSiteId(request)
        def sento = "";
        def sqlCount = "select count(nd.id)" +
                " from wscomm.notification_dtl nd" +
                " where nd.message_type='" + params.pageType + "'" +
                " AND nd.site_id="+siteId;
        if (params."search[value]" != null && params."search[value]" != "") {
            sqlCount += " AND (nd.title  LIKE '%" + params."search[value]" + "%' OR nd.body  LIKE '%" + params."search[value]" + "%')";
        }
        def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1count = new Sql(dataSourceCount)
        def resultsCount = sql1count.rows(sqlCount)
        def count = resultsCount.get(0).values();
        def sql = "select nd.id,nd.title,nd.body,nd.created_by,DATE_ADD(nd.date_created,INTERVAL '5:30' HOUR_MINUTE) date_created,COALESCE(nd.send_to,'') send_to,nd.status" +
                " from wscomm.notification_dtl nd" +
                " where nd.message_type='" + params.pageType + "'" +
                " AND nd.site_id="+siteId;
        if (params."search[value]" != null && params."search[value]" != "") {
            sql += " AND (nd.title  LIKE '%" + params."search[value]" + "%' OR nd.body  LIKE '%" + params."search[value]" + "%')";
        }
        sql += " order by nd.id Desc limit " + params.start + " , " + params.length + "";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def date
        List resources = results.collect { res ->
            def send=res.send_to+"".replaceAll("\\s", "");
            if("book".equals(params.pageType)) {
                sento="";
                String[] book = send.split(",")
                for (int i = 0; i < book.length; i++) {
                    BooksMst booksMst = BooksMst.findById(new Long((book[i]+"").replaceAll("\\s", "")))
                    if(booksMst!=null) sento += booksMst.title + ",";
                }
                if(sento!="") sento= sento.substring(0, sento.length() - 1);
            }else if("batch".equals(params.pageType)){
                sento="";
                String[] batch = send.split(",")
                for (int i = 0; i < batch.length; i++) {
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long((batch[i]+"").replaceAll("\\s", "")))
                    if(courseBatchesDtl!=null) sento += courseBatchesDtl.name + ",";
                }
                if(sento!="") sento= sento.substring(0, sento.length() - 1);
            }else{
                sento="";
                sento= res.send_to+"".replaceAll("\\s", "");
            }

            date = res.date_created
            if (date != "" && date != null) {
                date = ( new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")).format(date)
            }
            String sentby=res.created_by+"";
            if (sentby.contains("_")) {
                sentby=sentby.split("_")[1];
            } else {
                sentby=res.created_by+"";
            }
            return [id: res.id, title: (""+res.title).replaceAll('"',"") ,body:(""+res.body).replaceAll('"',""),sentby:sentby,date:date,sendto:sento,status:res.status]
        }
        def json = [data: resources, recordsTotal: count, draw: params.draw, recordsFiltered: count]
        render json as JSON
    }


    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def packageBooksReport() {
        Integer siteId = getSiteId(request)
        if (params.packageCourseId != "" && params.packageCourseId != null) {
            def send=params.packageCourseId;
            String[] book = send.split(",")
            List resources = []
            for (int i = 0; i < book.length; i++) {
                BooksMst booksMst=BooksMst.findById(new Long( book[i]));
                def sql = "select bm.package_book_ids,bm.title,bm.id" +
                        " from books_mst bm" +
                        " where FIND_IN_SET(" + book[i] + ",bm.package_book_ids)"+
                        " and bm.site_id="+siteId+"  order by bm.id Desc " ;
                def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource1)
                def results = sql1.rows(sql)
                results.each { books ->
                    resources.add([mainBookTitle: books.title, mainBookId: books.id, packageBooktitle: booksMst.title,packagebookId:booksMst.id])
                }
            }
            if("true".equals(params.download)) {
                List headers
                List withProperties
                headers = ["Package Course Id","Package Course Title","Main Course Id","Main Course Title"]
                withProperties = ["packagebookId", "packageBooktitle","mainBookId","mainBookTitle"]
                def fileName = "PackageCourseDetails_"  + params.packageCourseId + ".xlsx";

                new WebXlsxExporter().with {
                    setResponseHeaders(response, fileName)
                    fillHeader(headers)
                    add(resources, withProperties)
                    save(response.outputStream)
                }
            }else {
                def json = [data: resources, recordsTotal: resources.size(), draw: params.draw, recordsFiltered: resources.size()]
                render json as JSON
            }


        }
    }

    @Transactional
    def addContactForm(){
        Integer siteId = getSiteId(request)
        ContactFormDtl contactFormDtl = new ContactFormDtl(name:params.name,email: params.email,
                siteId: siteId,mobile: params.phone,comment:params.comment,copy:params.copy)
        contactFormDtl.save(flush: true, failOnError: true);
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        try {
            if(siteId.intValue()==23 && contactFormDtl.email!=null) {
                userManagementService.sendContactUsEtexts(contactFormDtl.name, contactFormDtl.email, contactFormDtl.mobile, contactFormDtl.comment, contactFormDtl.copy,siteName,siteId)
            }else if (siteId.intValue()==24 && contactFormDtl.email!=null){
                userManagementService.sendContactUsEbouquet(contactFormDtl.name, contactFormDtl.email, contactFormDtl.mobile, contactFormDtl.comment, contactFormDtl.copy,siteName,siteId)
            }

        } catch (Exception e) {
            println "sending contact email failed " + e.toString()
        }
        if(siteId.intValue()==23) {
            redirect(controller: 'etexts', action: 'contact')
        }else if (siteId.intValue()==24 && !"yes".equals(params.noredirect)){
            redirect(controller: 'ebouquet', action: 'contact')
        }else if(siteId.intValue()==24 && "yes".equals(params.noredirect)){
            redirect(controller: 'ebouquet', action: 'decision')
        }


    }

    @Transactional
    def addFeedbackForm(){
        Integer siteId = getSiteId(request)
        FeedbackDtl feedbackDtl = new FeedbackDtl(search:params.search,readingexperience: params.reading,
                mylibrary:params.library,notes: params.notes,
                products:params.products,dashboard: params.dashboard,
                experience:params.browsing,review: params.review,
                expectation:params.looking,requirement: params.requirements,
                improvement:params.feature_improvements,name: params.first_name,
                siteId: siteId,email: params.email_id,institutionName:params.institute_name)
        feedbackDtl.save(flush: true, failOnError: true);

        try {
            userManagementService.sendFeedbackEtexts(feedbackDtl.search,feedbackDtl.readingexperience, feedbackDtl.mylibrary,
                    feedbackDtl.notes, feedbackDtl.products,feedbackDtl.dashboard,feedbackDtl.experience,feedbackDtl.review
                    ,feedbackDtl.expectation,feedbackDtl.requirement,feedbackDtl.improvement,feedbackDtl.name,feedbackDtl.email,feedbackDtl.institutionName
            )
        } catch (Exception e) {
            println "sending feedback failed " + e.toString()
        }
        redirect(controller: 'etexts', action: 'feedback')
    }

    @Transactional
    def addRequestDemoForm(){
        Integer siteId = getSiteId(request)
        ContactFormDtl contactFormDtl = new ContactFormDtl(name:params.name,email: params.email,
                siteId: siteId,mobile: params.phone,comment:params.comment,copy:params.copy,companyName:params.companyName,jobTitle:params.jobTitle )
        contactFormDtl.save(flush: true, failOnError: true);
        try {
                userManagementService.sendRequestDemo(contactFormDtl.name, contactFormDtl.email, contactFormDtl.mobile, contactFormDtl.comment,contactFormDtl.companyName,contactFormDtl.jobTitle)
        } catch (Exception e) {
            println "sending request email failed " + e.toString()
        }
        redirect(controller: 'ebouquet', action: 'requestDemo')
    }



    def getServerTime(){
        def json = ["serverTime":Calendar.getInstance().getTime()]
        render json as JSON
    }

    @Transactional
    def updateFlashCardTime(){
        FlashCardTimeLog flashCardTimeLog = FlashCardTimeLog.findByUsernameAndResId(springSecurityService.currentUser.username,new Long(params.resId))
        if(flashCardTimeLog==null){
            flashCardTimeLog = new FlashCardTimeLog(resId: new Long(params.resId),username: springSecurityService.currentUser.username,time: params.timeTaken,dateUpdated: new Date())
        }else{
            flashCardTimeLog.time = params.timeTaken
            flashCardTimeLog.dateUpdated = new Date()
        }
        flashCardTimeLog.save(flush: true, failOnError: true)
        def json = ["status":"OK"]
        render json as JSON
    }

    @Transactional
    def updateFastestFlashCardTime(){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        resourceDtl.flashCardFastTime = params.timeTaken
        resourceDtl.fastTimeUsername = springSecurityService.currentUser.username
        resourceDtl.save(flush: true, failOnError: true)
        def json = ["status":"OK"]
        render json as JSON
    }

    @Transactional
    def getUserFastestFlashCardTime(){
        FlashCardTimeLog flashCardTimeLog = FlashCardTimeLog.findByUsernameAndResId(springSecurityService.currentUser.username,new Long(params.resId))
        def json = ["timeTaken":null]
        if(flashCardTimeLog!=null){
            json = ["timeTaken":flashCardTimeLog.time]

        }
        render json as JSON

    }



    @Transactional
    def enquiryForm(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    @Secured(['ROLE_ENQUIRY_SALES']) @Transactional
    def enquiryDetails(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    @Secured(['ROLE_ENQUIRY_SALES']) @Transactional
    def downloadEnquiryFormData(){
        if(params.fromDate!=null && params.fromDate!="" && params.toDate!=null && params.toDate!=""){
            String sql =
                    " SELECT id, DATE_FORMAT(date_created,'%d/%m/%Y') date_created, email, mobile,name, school_name FROM wscomm.contact_form_dtl where site_id=1 AND " +
                            " DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.fromDate+"','%d-%m-%Y') AND " +
                            " DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.toDate+"','%d-%m-%Y') "
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            List data = results.collect {
                return [
                        id: it.id,
                        dateCreated: it.date_created,
                        email: it.email,
                        mobile: it.mobile,
                        name: it.name,
                        schoolName: it.school_name
                ]
            }
            List headers = ["Name", "School Name", "Email", "Mobile", "Date Created" ]
            List withProperties = ["name", "schoolName", "email", "mobile", "dateCreated"]
            def fileName = "Digital_Library_Enquiry_Data_"+params.fromDate+"_"+params.toDate+"_"+(new Random()).nextInt(9999999)+".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response,fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
    }
    @Transactional
    def addEnquiryFormRecord(){
        ContactFormDtl dtl = new ContactFormDtl(
                name: params.name,
                schoolName: params.schoolName,
                email: params.email,
                mobile: params.mobile,
                dateCreated: new Date(),
                siteId: getSiteId(request),
                comment: params.comment
        )
        dtl.save()
        if(params.email!=null && !params.email.equals("")) {
            if(userManagementService.validateEmail(params.email,(Integer)session["siteId"])) {
                try {
                    userManagementService.sendEnquiryFormEmail(params.email, params.name)
                } catch (Exception e) {
                    println "sending digital library enquiry email failed " + e.toString()
                }
            }
        }
        def json = [
                status: 'ok'
        ]
        render json as JSON
    }

    @Transactional
    def affiliationFormRecord(){
                try {
                    userManagementService.sendAffiliationEmail(params.name,params.email,params.mobile, params.state)
                } catch (Exception e) {
                    println "sending affiliation email failed " + e.toString()
                }
        def json = [
                status: 'ok'
        ]
        render json as JSON
    }



    @Transactional
    def updateBookViewApp() {
    // now save booksviewdtl
    Integer siteId = getSiteId(request)
    BooksViewDtl booksViewDtl
    if (siteId.intValue() == 24) {
        int instituteId
        String ipAddress = utilService.getIPAddressOfClient(request)
        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress, siteId)
        if (instituteIPAddress != null) {
            instituteId = instituteIPAddress.institute_id
            booksViewDtl = new BooksViewDtl(bookId: new Long(params.bookId), viewSource:  "mobile", viewType: "library", username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                    siteId: getSiteId(request), instituteId: instituteId)
        } else {
            String sql = "SELECT im.id FROM wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.batch_user_dtl bud" +
                    " WHERE im.id = cbd.conducted_by" +
                    "  AND bud.batch_id = cbd.id" +
                    "   AND bud.username='" + springSecurityService.currentUser.username + "'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.each { result ->
                instituteId = result[0]
            }
            booksViewDtl = new BooksViewDtl(bookId: new Long(params.bookId), viewSource: "mobile", viewType: "library", username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                    siteId: getSiteId(request), instituteId: instituteId)
        }
    }
    booksViewDtl.save(flush: true, failOnError: true)
    def json = [
            status: 'Ok'
    ]
    render json as JSON
  }

    @Secured(['ROLE_USER']) @Transactional
    def addFeedback(){
        FeedbackMst feedbackMst = new FeedbackMst(feedback: params.feedback, stars: params.stars!=null?new Integer(params.stars):null, source:params.source,username:springSecurityService.currentUser.username)
        feedbackMst.save(flush: true, failOnError: true)
        def json = ["status":"ok"]
        render json as JSON
    }


    @Transactional
    def getBookandResDeepLinkById() {
        def status
        def deeplink
        if ("resource".equals(params.createFor)) {
            if (ResourceDtl.findById(new Long(params.resId)).resDeepLink == null) {
                deeplink=getDeepLink(params.chapterId,new Long(params.resId),params.resType,params.bookId,getSiteId(request));
                if(deeplink!="null" && deeplink!=null) {
                    ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
                    resourceDtl.resDeepLink = deeplink
                    resourceDtl.save()
                    status = "created"
                }else{
                    status = "error"
                }
            }else{
                deeplink= ResourceDtl.findById(new Long(params.resId)).resDeepLink
                status="exist"
            }
        }else if("book".equals(params.createFor)){
            if (BooksMst.findById(new Long(params.bookId)).bookDeepLink == null) {
                deeplink=getDeepLink(null,null,null,params.bookId,getSiteId(request));
                if(deeplink!="null" && deeplink!=null) {
                    BooksMst.executeUpdate("update BooksMst set bookDeepLink='" + deeplink + "' where id=" + params.bookId)
                    BooksMst.wsuser.executeUpdate("update BooksMst set bookDeepLink='" + deeplink + "' where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set bookDeepLink='" + deeplink + "' where id=" + params.bookId)
                    status = "created"
                }else{
                    status = "error"
                }
            }else{
                deeplink= BooksMst.findById(new Long(params.bookId)).bookDeepLink
                status="exist"
            }
        }
        def json = [status:status ,deeplink: deeplink]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def stopRevision(){
        logsService.stopRevision(new Integer(params.logId))
        def json = ['status':'stopped']
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getRevisionListByUser(){

        def json = ["revisionLog":logsService.getRevisionListByUser(springSecurityService.currentUser.username)]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def addUserLoginLog(){
        logsService.addUserLoginLog(new Integer(params.siteId),params.source,springSecurityService.currentUser.username)
        def json = ['status':'updated']
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def storeSageTermsDtl(){
        def json = logsService.storeSageTermsDtl(session,params,request)
        render json as JSON
    }



    def convertBookImageToWebPbyBookId(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        BufferedImage image = ImageIO.read(new File("upload/books/"+booksMst.id+"/"+booksMst.coverImage));
        String coverImage=booksMst.coverImage
        if(coverImage!=null) coverImage= coverImage.substring(0, coverImage.indexOf("."))
        // Encode it as webp using default settings and save it as webp file
        ImageIO.write(image, "webp", new File("upload/books/"+booksMst.id+"/processed/"+coverImage+".webp"));
    }


    def convertBookImageToWebPbySiteId(){
        List books =BooksMst.findAllBySiteIdAndCoverImageIsNotNull(new Integer(params.siteId))
        books.each {
            File uploadDir = new File("upload/books/" + it.id)
            if (uploadDir.exists()) {
                File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                if (uploadDir1.exists()) {
                    try {
                        BufferedImage image = ImageIO.read(new File("upload/books/" + it.id + "/" + it.coverImage));
                        String coverImage = it.coverImage
                        if (coverImage != null) coverImage = coverImage.substring(0, coverImage.indexOf("."))
                        // Encode it as webp using default settings and save it as webp file
                        ImageIO.write(image, "webp", new File("upload/books/" + it.id + "/processed/" + coverImage + ".webp"));
                    } catch (Exception e) {
                        println "Exception in  covertBookImageToWebPbySiteId  " + e.toString()
                    }
                }
            }
        }
    }




    def deleteImagesBySiteId(){
        List books =BooksMst.findAllBySiteId(new Integer(params.siteId))
        books.each {
            File extractDir = new File("upload/books/"+it.id)
            if (extractDir.exists()) {
                deleteDirectory(extractDir.getAbsolutePath(),it.coverImage)
            }
        }
        render ""
    }

    def deleteImagesByBookId(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        File extractDir = new File("upload/books/"+booksMst.id)
        if (extractDir.exists()) {
            deleteDirectory(extractDir.getAbsolutePath(),booksMst.coverImage)
        }
        render ""
    }


    def deleteProcessedImagesByBookId(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        File extractDir = new File("upload/books/"+booksMst.id+"/processed")
        if (extractDir.exists()) {
            deleteProcessedDirectory(extractDir.getAbsolutePath(),booksMst.coverImage)
        }
        render ""
    }

    def deleteProcessedImagesBySiteId(){
        List books =BooksMst.findAllBySiteId(new Integer(params.siteId))
        books.each {
            File extractDir = new File("upload/books/"+it.id+"/processed")
            if (extractDir.exists()) {
                deleteProcessedDirectory(extractDir.getAbsolutePath(),it.coverImage)
            }
        }
        render ""
    }


    def deleteProcessedDirectory(String dirStr,String coverImage) {
        try {
            String picFileNameThumbnail
            String picFileNamePassport
            String picFileNameWebp
            File dir = new File(dirStr)
            for (File file : dir.listFiles()) {
                if (!file.isDirectory()) {
                    picFileNameThumbnail = coverImage.substring(0, coverImage.indexOf(".")) + '_' + "passport" + coverImage.substring(coverImage.indexOf("."))
                    picFileNamePassport = coverImage.substring(0, coverImage.indexOf(".")) + '_' + "thumbnail" + coverImage.substring(coverImage.indexOf("."))
                    picFileNameWebp = coverImage.substring(0, coverImage.indexOf(".")) + ".webp"
                    if (!picFileNameThumbnail.equals(file.name) && !picFileNamePassport.equals(file.name) &&  !picFileNameWebp.equals(file.name)) {
                        file.delete();
                    }

                }
            }
        } catch (Exception e) {
            println "Exception in  image deletion  " + e.toString()
        }
    }

    def deleteDirectory(String dirStr,String coverImage) {
        try {
            File dir = new File(dirStr)
            for (File file : dir.listFiles()) {
                if (!file.isDirectory()) {
                    if (!coverImage.equals(file.name)) {
                        file.delete();
                    }
                }
            }
        } catch (Exception e) {
            println "Exception in  image deletion  " + e.toString()
        }
    }

    def convertBannerImageToWebPbyBannerId(){
        BannersMst bannersMst = BannersMst.findById(new Long(params.bannerId))
        BufferedImage image = ImageIO.read(new File("upload/banner/"+bannersMst.id+"/"+bannersMst.imagePath));
        String bannerImage=bannersMst.imagePath
        if(bannerImage!=null) bannerImage= bannerImage.substring(0, bannerImage.indexOf("."))
        // Encode it as webp using default settings and save it as webp file
        ImageIO.write(image, "webp", new File("upload/banner/"+bannersMst.id+"/"+bannerImage+".webp"));
    }



    def convertBannerImageToWebPbySiteId(){
        List banners =BannersMst.findAllBySiteId(new Integer(params.siteId))
        banners.each {
            File uploadDir = new File("upload/banner/" + it.id)
            if (uploadDir.exists()) {
                    try {
                        BufferedImage image = ImageIO.read(new File("upload/banner/" + it.id + "/" + it.imagePath));
                        String bannerImage = it.imagePath
                        if (bannerImage != null) bannerImage = bannerImage.substring(0, bannerImage.indexOf("."))
                        // Encode it as webp using default settings and save it as webp file
                        ImageIO.write(image, "webp",  new File("upload/banner/"+it.id+"/"+bannerImage+".webp"));
                    } catch (Exception e) {
                        println "Exception in  covertBannerImageToWebPbySiteId  " + e.toString()
                    }

            }
        }
    }


    def convertPublisherBannerImageToWebPbySiteId(){
        List banners =BannersMst.findAllBySiteId(new Integer(params.siteId))
        banners.each {
            File uploadDir = new File("upload/banner/" + it.imagePath)
            if (uploadDir.exists()) {
                try {
                    BufferedImage image = ImageIO.read(new File("upload/banner/" + it.imagePath));
                    String bannerImage = it.imagePath
                    if (bannerImage != null) bannerImage = bannerImage.substring(0, bannerImage.indexOf("."))
                    // Encode it as webp using default settings and save it as webp file
                    ImageIO.write(image, "webp",  new File("upload/banner/"+bannerImage+".webp"));
                } catch (Exception e) {
                    println "Exception in  convertPublisherBannerImageToWebPbySiteId  " + e.toString()
                }

            }
        }
    }


    def convertBookImageToWebPbySiteIdPaginatedPublished(){
        def sql ="select bm.id,bm.cover_image " +
                " from books_mst bm" +
                " where site_id="+params.siteId+"  and bm.cover_image is not null  and bm.status='published'"+
                " order by bm.id desc limit " + params.limit + "";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each {
            File uploadDir = new File("upload/books/" + it.id)
            if (uploadDir.exists()) {
                File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                if (uploadDir1.exists()) {
                    try {
                        BufferedImage image = ImageIO.read(new File("upload/books/" + it.id + "/" + it.cover_image));
                        String coverImage = it.cover_image
                        if (coverImage != null) coverImage = coverImage.substring(0, coverImage.indexOf("."))
                        // Encode it as webp using default settings and save it as webp file
                        ImageIO.write(image, "webp", new File("upload/books/" + it.id + "/processed/" + coverImage + ".webp"));
                    } catch (Exception e) {
                        println "Exception in  convertBookImageToWebPbySiteIdPaginatedPublished  " + e.toString()
                    }
                }
            }
        }
    }



    def convertBookImageToWebPbySiteIdPaginatedUnPublished(){
        def sql ="select bm.id,bm.cover_image " +
                " from books_mst bm" +
                " where site_id="+params.siteId+"  and bm.cover_image is not null  and bm.status is null"+
                " order by bm.id desc limit " + params.limit + "";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each {
            File uploadDir = new File("upload/books/" + it.id)
            if (uploadDir.exists()) {
                File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                if (uploadDir1.exists()) {
                    try {
                        BufferedImage image = ImageIO.read(new File("upload/books/" + it.id + "/" + it.cover_image));
                        String coverImage = it.cover_image
                        if (coverImage != null) coverImage = coverImage.substring(0, coverImage.indexOf("."))
                        // Encode it as webp using default settings and save it as webp file
                        ImageIO.write(image, "webp", new File("upload/books/" + it.id + "/processed/" + coverImage + ".webp"));
                    } catch (Exception e) {
                        println "Exception in  convertBookImageToWebPbySiteIdPaginatedunPublished  " + e.toString()
                    }
                }
            }
        }
    }

    @Transactional
    def createAffLog(){

        AffliationLinkLog affliationLinkLog = new AffliationLinkLog(username: params.username!=null?params.username:null,bookId:new Integer(params.bookId),
        source:params.source,linkType: params.linkType,productLink: params.productLink,siteId: new Integer(params.siteId),bookType: params.bookType)
        affliationLinkLog.save(flush: true, failOnError: true);

        def json = ['affId':affliationLinkLog.id]
        render json as JSON
    }
    @Transactional
    def updateAffLog(){
        AffliationLinkLog affliationLinkLog = AffliationLinkLog.findById(new Integer(params.affId))
        if(affliationLinkLog!=null){
            affliationLinkLog.linkType=params.linkType
            affliationLinkLog.save(flush: true, failOnError: true);

        }

        def json = ['result':'success']
        render json as JSON
    }

    @Transactional
    def extensionLogin(){
        response.setHeader('Access-Control-Allow-Origin', '*')
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        def requestBody = request.JSON
        String username
        String password
        String authenticationTokenString
        Integer siteId = getSiteId(request)
        def json
        if (requestBody!=null) {
            def jsonObj = request.JSON
            password=jsonObj.password
            username=jsonObj.username

        }

        if(!username.startsWith(""+siteId+"_")) username = ""+siteId+"_"+username
        User user = User.findByUsername(username)
        if (user!=null&&springSecurityService.passwordEncoder.isPasswordValid(user.password, password, null)) {
            try {
                springSecurityService.reauthenticate(user.username)
                //valid user
                //first do the force logout of already logged in users
                UUID gfg = UUID.randomUUID();
                authenticationTokenString = gfg.toString()

                //mobile login
                dataNotificationService.sendLogoutNotificationToUser(user.username, getSiteId(request), authenticationTokenString)
                List appTokens = AuthenticationToken.findAllByUsername(user.username)
                appTokens.each { appToken ->
                    appToken.delete(flush: true)
                }

                AuthenticationToken authenticationToken = new AuthenticationToken(username: user.username, token: authenticationTokenString)
                authenticationToken.save(failOnError: true, flush: true)

                UserLog log = new UserLog(username: user.username, action: "login")
                log.save(failOnError: true, flush: true)
            } catch (Exception e) {
                println " login  failed " + e.toString()
            }

            def profileDetails =[
                    'name': user.name,
                    'email': ("<EMAIL>".equals(user.email)?"":user.email),
                    'mobile': ("mobile".equals(user.mobile)?"":user.mobile),
                    'profilePic':user.profilepic,
                    'id':user.id,
                    'state':user.state,
                    'district':user.district,
                    'schoolOrCollege':user.school,
                    'country':user.country,
                    'pincode':user.pincode,
                    'who':"student"
            ]
            json = ["username": user.username, "access_token": authenticationTokenString,
                    'userSelectedPreference':userManagementService.getUserGrades(user.username),
                    'profileDetails':profileDetails,
                    "status"  : "ok"]
        }else{
            //password fail
            json = ["status":"Failed"]
        }
        render json as JSON
    }
}
