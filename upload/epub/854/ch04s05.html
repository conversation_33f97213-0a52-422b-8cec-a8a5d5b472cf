<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Using plugin extensions with SketchUp</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Using plugin extensions with SketchUp"><div class="titlepage"><div><div><h1 class="title"><a id="ch04lvl1sec54"/>Using plugin extensions with SketchUp</h1></div></div></div><p>SketchUp has a lot of easy and powerful features, but sometimes, there are hard routes in the modeling process that can be made simpler. Sometimes, using a plugin, the task can be made easier. A few recipes back,<a id="id264" class="indexterm"/> we learned how to use add-ons from the Extension Warehouse.</p><p>In this recipe, we'll learn <a id="id265" class="indexterm"/>how to install plugins from another site, SketchUcation. Then, we'll refine our toy block from the previous recipe and improve it until it's a perfect copy of the original, using the RoundCorner plugin.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch04lvl2sec121"/>Getting ready</h2></div></div></div><p>Go to <a class="ulink" href="http://sketchucation.com/">http://sketchucation.com/</a> and follow the instructions to join this site. Don't worry; it's free. Once you become a member, follow this link: <a class="ulink" href="http://sketchucation.com/forums/viewtopic.php?f=323&amp;t=20485">http://sketchucation.com/forums/viewtopic.php?f=323&amp;t=20485</a>. From here, you'll be given precise instructions on where and how to install the RoundCorner plugin and its dependencies, <span class="strong"><strong>LibFredo6 4.9</strong></span> or higher and <span class="strong"><strong>000-_AdditionalPluginFolders</strong></span>. Carefully follow Fredo6's installation instructions.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch04lvl2sec122"/>How to do it...</h2></div></div></div><p>Open your saved <code class="literal">.skp</code> file of the toy block, which you made in the previous recipe. Then, proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">
We'll start by beveling the two dowels on the block. There should be a floating toolbox for RoundCorner at the top-left corner of your workspace. Choose the <span class="strong"><strong>Bevel edges and corners</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_04_14.jpg" alt="How to do it..."/></span>], and a menu will appear at the top.
</li><li class="listitem">
Select the <span class="strong"><strong>Extend selection to curve</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_04_15.jpg" alt="How to do it..."/></span>].
</li><li class="listitem">Select both the top ends of the dowels. In order to calculate the size of the bevel, measure the diameter of the truncated portion of the dowel (in this case, it is <code class="literal">18</code> mm). Subtract this amount from the dowel's full diameter (<code class="literal">27</code> - <code class="literal">18</code> = <code class="literal">9</code> mm). Enter <a id="id266" class="indexterm"/>half of this difference<a id="id267" class="indexterm"/> (<code class="literal">4.5</code> mm) for the offset value in the tool menu. It should look similar to the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_04_16.jpg" alt="How to do it..."/></div></li><li class="listitem">The cursor will become a green check mark. Left-click to execute the bevel.</li><li class="listitem">
Select the <span class="strong"><strong>Round corners in 3D</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_04_17.jpg" alt="How to do it..."/></span>].
</li><li class="listitem">
From the RoundCorner menu, select the <span class="strong"><strong>Extend selection to all connected edges</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_04_18.jpg" alt="How to do it..."/></span>].
</li><li class="listitem">Place the cursor on any edge of the block portion and select an area. Enter roughly <code class="literal">1.6</code> for the <span class="strong"><strong>Offset value</strong></span> option in the <span class="strong"><strong>Tools</strong></span> menu. This value represents 1.6 mm, and it's determined roughly by a visual guess.</li><li class="listitem">Select the icon for <span class="strong"><strong>Extend selection to curve</strong></span>. Left-click on the red guide surrounding the indentations on top of the dowels and the perimeter of the dowel's footprint.<a id="id268" class="indexterm"/> These areas should not be<a id="id269" class="indexterm"/> rounded; otherwise, this tool will remove the selection. In the left-hand side image of the following screenshot, these areas are shown by the red arrows. The image to the right shows only the edges of the block selected.<div class="mediaobject"><img src="graphics/9888OS_04_19.jpg" alt="How to do it..."/></div></li><li class="listitem">Left-click to execute. Your block should look similar to the following image:<div class="mediaobject"><img src="graphics/9888OS_04_20.jpg" alt="How to do it..."/></div></li><li class="listitem">Go to <span class="strong"><strong>File</strong></span> and click on <span class="strong"><strong>Save</strong></span> (<span class="emphasis"><em>Ctrl</em></span> + <span class="emphasis"><em>S</em></span>). Save the shape as a SketchUp model (<code class="literal">.skp</code>). Export it as a <code class="literal">.stl</code> file.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch04lvl2sec123"/>How it works...</h2></div></div></div><p>SketchUp Make works with the addition of plugins based on Ruby scripts. This has allowed users to write a variety of scripts that can be useful, and many are shared on forums such as SketchUcation. When RoundCorner was installed, it created the <span class="strong"><strong>Fredo6 Collection</strong></span> selection under <span class="strong"><strong>Tools</strong></span> in<a id="id270" class="indexterm"/> the menu. From here, a variety of tasks, which mirrors the pop-up toolbox, can be chosen. If the pop-up<a id="id271" class="indexterm"/> toolbox for RoundCorner is closed and you want it back, it can be found by choosing <span class="strong"><strong>View</strong></span> in the menu and then selecting <span class="strong"><strong>Toolbars</strong></span>. From the pop-up, <span class="strong"><strong>Toolbars</strong></span>, find <span class="strong"><strong>RoundCorner</strong></span> from the list and check mark it.</p></div></div></body></html>
