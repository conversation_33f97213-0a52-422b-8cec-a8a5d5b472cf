grails.servlet.version = "3.0" // Change depending on target container compliance (2.5 or 3.0)
grails.project.class.dir = "target/classes"
grails.project.test.class.dir = "target/test-classes"
grails.project.test.reports.dir = "target/test-reports"
grails.project.work.dir = "target/work"
grails.project.target.level = 1.6
grails.project.source.level = 1.6
//grails.project.war.file = "target/${appName}-${appVersion}.war"
grails.project.war.file = "target/${appName}.war"

grails.project.fork = [
    // configure settings for compilation JVM, note that if you alter the Groovy version forked compilation is required
    //  compile: [maxMemory: 256, minMemory: 64, debug: false, maxPerm: 256, daemon:true],

    // configure settings for the test-app JVM, uses the daemon by default
    test: [maxMemory: 768, minMemory: 64, debug: false, maxPerm: 256, daemon:true],
    // configure settings for the run-app JVM
    run: [maxMemory: 768, minMemory: 64, debug: false, maxPerm: 256, forkReserve:false],
    // configure settings for the run-war JVM
    war: [maxMemory: 768, minMemory: 64, debug: false, maxPerm: 256, forkReserve:false],
    // configure settings for the Console UI JVM
    console: [maxMemory: 768, minMemory: 64, debug: false, maxPerm: 256]
]

grails.project.dependency.resolver = "maven" // or ivy
grails.project.dependency.resolution = {
    // inherit Grails' default dependencies
    inherits("global") {
        // specify dependency exclusions here; for example, uncomment this to disable ehcache:
        // excludes 'ehcache'
    }
    log "error" // log level of Ivy resolver, either 'error', 'warn', 'info', 'debug' or 'verbose'
    checksums true // Whether to verify checksums on resolve
    legacyResolve false // whether to do a secondary resolve on plugin installation, not advised and here for backwards compatibility

    repositories {
        inherits true // Whether to inherit repository definitions from plugins

        grailsPlugins()
        grailsHome()
        mavenLocal()
        grailsCentral()
        mavenCentral()
        //uncomment these (or add new ones) to enable remote dependency resolution from public Maven repositories
        //mavenRepo "http://repository.codehaus.org"
        //mavenRepo "http://download.java.net/maven/2/"
        //mavenRepo "http://repository.jboss.com/maven2/"
    }

    dependencies {
        // specify dependencies here under either 'build', 'compile', 'runtime', 'test' or 'provided' scopes e.g.
        // runtime 'mysql:mysql-connector-java:5.1.29'
        // runtime 'org.postgresql:postgresql:9.3-1101-jdbc41'
        test "org.grails:grails-datastore-test-support:1.0.2-grails-2.4"
        runtime 'net.sourceforge.jexcelapi:jxl:2.6.12'
        compile 'org.springframework.social:spring-social-core:1.0.1.RELEASE'
        compile 'org.springframework.social:spring-social-facebook:1.0.1.RELEASE' 
        compile 'org.apache.commons:commons-lang3:3.1'
        compile 'commons-io:commons-io:2.4'
		compile "com.razorpay:razorpay-java:1.0.0"
		//runtime "org.grails.plugins:session-timeout-ui:1.0"
    }

    plugins {
        // plugins for the build system only
        build ":tomcat:********" // or ":tomcat:8.0.20"

        // plugins for the compile step
        compile "org.grails.plugins:wordpress:0.2.5"
        compile "org.grails.plugins:php:0.1.5"
        compile ":scaffolding:2.1.2"
        compile ':cache:1.1.8'
        compile ":asset-pipeline:2.1.5"
        compile ':spring-security-core:2.0-RC4'
        compile ":spring-security-rest:latest.release", {
            exclude 'spring-security-core'
			exclude 'pac4j-cas'
			exclude 'bcprov-jdk15on'			
        }

        compile ':spring-security-oauth-facebook:0.1'
        compile ':spring-security-oauth-google:0.3.1'          
        
        //compile ':pdf:0.6'
        compile ':rendering:1.0.0'        
        compile ':html-validator:0.3'

        // plugins needed at runtime but not for compilation
        runtime ":hibernate4:*******" // or ":hibernate:*********"
        runtime ":database-migration:1.4.0"
        runtime ":jquery:1.11.1"

        compile ":hd-image-utils:1.1"
		//compile ":ckeditor:*******"

        // Uncomment these to enable additional asset-pipeline capabilities
        //compile ":sass-asset-pipeline:1.9.0"
        //compile ":less-asset-pipeline:1.10.0"
        //compile ":coffee-asset-pipeline:1.8.0"
        //compile ":handlebars-asset-pipeline:*******"
		
		compile ":mail:1.0.7"		
		compile ":joda-time:1.5"
		compile ':quartz:1.0.1'
		compile ':tooltip:0.8'
		
		//compile ':session-timeout-ui:1.0'		
		
		//compile "org.grails.plugins:sound-manager:0.4"		
		
		//compile 'org.apache.pdfbox:pdfbox:2.0.6'		
    }
}
