<ul class="navbar-nav right-menu d-none d-md-flex align-items-center mt-2 mt-md-0">
    <li class="nav-item active">
        <a class="nav-link" href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">DOWNLOAD APP</a>
    </li>
    <sec:ifNotLoggedIn>
        <li class="nav-item">
            <a class="nav-link loginButton" onclick="loginOpen()">LOGIN</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" onclick="signUpOpen()">SIGN UP</a>
        </li>
    </sec:ifNotLoggedIn>
    <sec:ifLoggedIn>
        <li class="nav-item notification">
            <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
        </li>
        <li class="nav-item dropdown">
            <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                <%}%>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <div class="media p-3">
                    <a href="/creation/userProfile">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                        <%}%>
                        <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                    </a>
                    <div class="media-body">
                        <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                        <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                    </div>
                </div>
                <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
            </div>
        </li>
    </sec:ifLoggedIn>
</ul>