body{
  overflow-x: hidden;
}
::-webkit-scrollbar {
  display: none;
}
.digital-library{
  overflow-x: visible;
}
.main-section{
  background: url("../../images/wslibrary/bgOrange.svg");
}
.edw-header{
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 9999;

  .logo-img{
    width: 240px;
    @media screen and (max-width: 768px){
      width: 200px;
    }
  }
}
.edw-banner__header{
  color: #fff;
}
.banner-img{
  @media screen and (max-width: 768px){
    width: 340px;
  }
}
.edw-form__section{
  background: #f4f4f4;
  padding: 2.2rem;
  width: 470px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 30%);

  @media screen and (max-width: 768px){
    width: auto;
  }

  h5{
    font-size: 18px;
    font-weight: 700;
    color: #202124;
    line-height: 24px;
    width: 100%;
    margin-top: 0;
    margin-bottom: 15px;
  }
}
input.form-control:focus{
  border-color: #333!important;
}
.btn-join{
  background: #F58D20;
  color: #fff;
}
.edw-descriptive__banners{
  min-height: 400px;
  .edw-banner-wrapper{
    .banner-content__wrapper{
      width: 370px;
      @media screen and (max-width: 768px){
        width: auto;
      }
    }
    @media screen and (max-width: 768px){
      flex-direction: column;
    }
  }
  &-header{
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 0 0 1rem;
    align-items: center;
    @media screen and (max-width: 768px){
      justify-content: flex-start;
    }
    h2{
      font-size: 24px;
      font-weight: 700;
      color: #5e5e5e;
    }
  }

  .edw-all__banners{
    height: 400px;
    img{
      width: 400px;
      @media screen and (max-width: 768px){
        width: 100%;
      }
    }
  }

}
.odd{
  //background: #F58D20;
  background: url("../../images/wslibrary/bgOrange.svg");
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px #0000001a;

  h2,p{
    color: #fff;
  }
}

.even{
  background: #fff;
  //background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1200' height='400' preserveAspectRatio='none' viewBox='0 0 1200 400'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1138%26quot%3b)' fill='none'%3e%3crect width='1200' height='400' x='0' y='0' fill='rgba(227%2c 219%2c 219%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c371.427C87.884%2c384.585%2c181.909%2c447.166%2c259.23%2c403.37C335.981%2c359.896%2c329.581%2c247.41%2c363.037%2c165.793C394.9%2c88.061%2c464.22%2c18.121%2c451.166%2c-64.868C438.13%2c-147.745%2c361.53%2c-203.899%2c296.541%2c-256.955C239.915%2c-303.183%2c173.561%2c-331.871%2c102.601%2c-349.428C34.136%2c-366.367%2c-37.104%2c-376.694%2c-104.59%2c-356.2C-171.74%2c-335.808%2c-230.984%2c-292.63%2c-271.984%2c-235.675C-311.05%2c-181.407%2c-315.135%2c-112.816%2c-327.147%2c-47.037C-339.655%2c21.46%2c-363.614%2c90.238%2c-343.68%2c156.953C-322.401%2c228.169%2c-276.78%2c293.212%2c-213.724%2c332.562C-151.024%2c371.69%2c-73.092%2c360.484%2c0%2c371.427' fill='%23d9cfcf'%3e%3c/path%3e%3cpath d='M1200 841.779C1290.864 854.0699999999999 1384.637 854.9200000000001 1468.424 817.6759999999999 1559.993 776.973 1647.345 714.448 1690.218 623.875 1733.298 532.864 1733.1480000000001 423.226 1700.173 328.086 1669.729 240.248 1579.3519999999999 194.282 1517.324 125.03699999999998 1456.601 57.249000000000024 1427.104-50.45600000000002 1339.381-74.68700000000001 1251.651-98.91899999999998 1172.493-16.428999999999974 1084.13 5.382000000000005 990.196 28.567999999999984 879.207-4.196000000000026 804.5350000000001 57.327999999999975 727.919 120.45400000000001 688.725 228.031 693.559 327.185 698.158 421.513 777.297 490.499 829.49 569.206 873.196 635.115 910.81 704.001 974.513 750.865 1041.142 799.8820000000001 1118.03 830.691 1200 841.779' fill='%23ede8e8'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1138'%3e%3crect width='1200' height='400' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
  background: url("../../images/eduwonder/drawer_bg.png");
  box-shadow: 0 4px 10px #0000001a;
  border-radius: 10px;
  padding: 2rem;

}

.digital-library .price-layer .card.pink-bg{
  background: url("../../images/wslibrary/bgOrange.svg");
}
.brands{
  h2{
    font-size: 24px;
    font-weight: 700;
    color: #5e5e5e;
    padding: 3rem 0 0 3rem;
  }
  .brandlogos{
    display: grid;
    grid-gap: 2rem;
    grid-template-columns: repeat(5,1fr);
    align-items: center;
    @media screen and (max-width: 768px){
      grid-template-columns: repeat(1,1fr);
      grid-gap: 1.5rem;
      display: none;
    }
    @media screen and (max-width: 900px){
      display: none;
    }
    img{
      width: 200px;
      display:flex;
      margin: 0 auto;
    }
  }
}

.brandList-mob{
  display: none;
  width: 100%;
  padding: 2rem;
  img{
    width: 100%;
  }
  @media screen and (max-width: 768px){
    display: block;
  }
  @media screen and (max-width: 900px){
    display: block;
  }
}
.applogos{
  margin: 0 auto;
}
.mobile-footer-nav{
  display: none !important;
}
.brandsList{
  width: 100%;
  img{
    width: 100%;
  }
}

.title-name{
  position: relative;
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
  &:before{
    content: "";
    position: absolute;
    background: #F58D20;
    height: 3px;
    width: 100px;
    bottom: -10px;
    margin-left: 50px;
  }
}
.title-name-1{
  position: relative;
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
  &:before{
    content: "";
    position: absolute;
    background: #F58D20;
    height: 3px;
    width: 60px;
    bottom: -10px;
    margin-left: 20px;
  }
}
.btn{
  box-shadow: none!important;
}

.form-control:focus{
  box-shadow:none;
  border-color: #F58D20;
}

.digital-library-footer{
  min-height: 180px !important;
}
.contactus{
  font-size: 1.2rem !important;
  text-align: start !important;
}
.mobile-no{
  font-size: 1.1rem !important;
}