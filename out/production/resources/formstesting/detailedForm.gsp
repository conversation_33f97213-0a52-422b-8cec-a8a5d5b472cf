<!DOCTYPE html>
<html>
<head>
    <title>Arivupro</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

    <asset:stylesheet src="forms/main.css"/>

    <asset:stylesheet src="forms/dashboard_businessdetails.css"/>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-lKuwvrZot6UHsBSfcMvOkWwlCMgc0TaWr+30HWe3a4ltaBwTZhyTEggF5tJv8tbt" crossorigin="anonymous">
    <asset:stylesheet src="forms/font/flaticon.css"/>

    <!-- Latest compiled and minified CSS -->

    <!-- <link rel="stylesheet" href="css/font-awesome.4.7.min.css"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

</head>
<body>
<div data-ng-controller="headerCtrl">
    <header class="main-header">
        <div class="skip">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <ul class="skip list-inline">
                            <li><a tabindex="-1" class="accessible" href="#">Skip to Main Content</a></li>
                            <li class="high-low"><i class="fa fa-adjust"></i></li>
                            <li class="fresize f-up">A<sup>+</sup></li>
                            <li class="fresize f-down">A<sup>-</sup></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row branding">
                <div class="col-xs-12">
                    <a href="#" title="Goods and Services Tax Home">
                        <img class="logo" src="${assetPath(src: 'emblem.png')}" alt="Goods and Services Tax Home">
                    </a>
                    <h1 class="site-title"><a href="#">Goods and Services Tax</a></h1>
                    <ul class="list-inline mlinks">
                        <!----><li ng-if="!udata">
                        <a target="_self" href="#"><i class="fa fa-sign-in"></i> Login</a>
                    </li><!---->
                    <!---->
                    <!---->
                    </ul>
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="sr-only">Toggle navigation</span>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <nav class="navbar navbar-default collapsed">
        <div class="container">
            <div id="main" class="navbar-collapse collapse">
                <ul class="nav navbar-nav">
                    <li class="active">
                        <a class="nav_home" href="#">Dashboard</a>
                    </li>
                    <li class="dropdown drpdwn">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false" target="_self">Services <span class="caret"></span></a>
                        <ul class="dropdown-menu smenu" role="menu">
                            <li class="has-sub">
                                <a href="#" target="_self" class="ng-hide">Registration</a>
                                <ul class="isubmenu serv">
                                    <li>
                                        <a href="#" target="_self">New Registration</a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav><!---->
<!----></ng-include>
</div>
<div class="content-wrapper">
    <div class="container">
        <div class="mypage">

            <div class="row">
                <div class="col-xs-10">
                    <div>
                        <ol class="breadcrumb">
                            <li>
                                <a href="#">Dashboard</a>
                            </li>
                            <li>
                                <a href="#"></a>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="lang dropdown">
                        <span class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">English</span>
                        <ul class="dropdown-menu lang-dpdwn">
                            <li>
                                <a>English</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="table-custom">
                        <table class="yellowtable">
                            <tr>
                                <td class="td">Application Type</td>
                                <td class="td">Due Date to Complete</td>
                                <td class="td">Last Modified</td>
                                <td class="td">Profile</td>
                            </tr>
                            <tr>
                                <td class="td">New Registration</td>
                                <td class="td">09/02/2018</td>
                                <td class="td">25/01/2018</td>
                                <td class="td">4%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="content-pane">
                <div style="">
                    <div>
                        <div>
                            <div class="row">
                                <ul class="nav nav-tabs progress-tab">
                                    <li class="active step"><a href="#b-details" data-toggle="tab"><i class="flaticon-briefcase"></i>Business Details<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#partner" data-toggle="tab"><i class="flaticon-user"></i>Pramoters / Partnes<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#author-sign" data-toggle="tab"><i class="flaticon-add-user"></i>Authorised Signatory<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#author-rep" data-toggle="tab"><i class="flaticon-mic"></i>Authorised Representative<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#pob" data-toggle="tab"><i class="flaticon-marker"></i>Principal Place of Business<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#aob" data-toggle="tab"><i class="flaticon-route"></i>Additinal Places of Business<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#goodservice" data-toggle="tab"><i class="flaticon-boxes"></i>Goods and Services<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#bankaccount" data-toggle="tab"><i class="flaticon-rupee"></i>Bank Accounts<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#statespecific" data-toggle="tab"><i class="flaticon-personal-profile"></i>State Specific Information<i class="update fa"></i></a></li>
                                    <li class="step"><a href="#verification" data-toggle="tab"><i class="flaticon-accept-circular-button-outline"></i>Verification<i class="update fa"></i></a></li>
                                </ul>
                            </div>
                            <form id="accountForm" method="post" class="form-horizontal">
                                <div class="tab-content">
                                    <div class="tab-pane active" id="b-details">
                                        <div>
                                            <p class="mand-text mandpos">indicates mandatory fields</p>
                                        </div>

                                        <div>
                                            <p class="details">Details of your Bussiness</p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="col-md-4 col-xs-4 fleft">
                                                <p>Legal Name of the Business</p>
                                                <p style="font-weight: bold;">OLD BOYS ASSOCIATION SAINIK</p>
                                            </div>

                                            <div class="col-md-4 col-xs-4 fleft">
                                                <p>Permanent Account number</p>
                                                <p style="font-weight: bold;">AAAAO3660R</p>
                                            </div>
                                        </div>


                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Trade Name</label>
                                                <input type="text" id="tradename" name="tradename" placeholder="" class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-6 col-sm-8 col-xs-12">
                                                <label class="reg m-cir">Constitution of Business (Select
                                                Appropriate)</label>
                                                <select class="fntextbox" id="constitutionofbusiness" name="business_const">
                                                    <option value="">Select</option>
                                                    <option value="proprietorship">Proprietorship</option>
                                                    <option value="foreigncompany">Foreign Company</option>
                                                    <option value="fllp">Foreign Limited Liability Partnership</option>
                                                    <option value="govtdept">Government Department</option>
                                                    <option value="huf">Hindu Undivided Family</option>
                                                    <option value="llp">Limited Liability Partnership</option>
                                                    <option value="local">Local Authority</option>
                                                    <option value="others">Others</option>
                                                    <option value="partnership">Partnership</option>
                                                    <option value="plc">Private Limited Company</option>
                                                    <option value="psu">Public Sector Undertaking</option>
                                                    <option value="scta">Society/Club/Trust/AOP</option>
                                                    <option value="sbody">Statutory Body</option>
                                                    <option value="others">Unlimited Company</option>
                                                </select>
                                            </div>

                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="col-md-4 col-xs-4 fleft">
                                                <p class="valign">Name of the State</p>
                                                <p>Karnataka</p>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Distrit</label>
                                                <select class="fntextbox" id="dashdistrict" name="district">
                                                    <option value="">Select</option>
                                                    <option value="dist1">Bangalore</option>
                                                    <option value="dist2">Mysore</option>
                                                    <option value="dist3">Bellari</option>
                                                    <option value="dist4">Bagalkot</option>
                                                    <option value="dist5">shimoga</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">
                                            <div class="col-md-4 col-xs-4 fleft">
                                                <p class="valign">State Jurisdiction</p>
                                                <p>VAT Sub Office</p>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-8 col-xs-12">
                                                <label class="reg m-cir">Sector / Circle / Ward / Charge / Unit</label>
                                                <select class="fntextbox" id="scwcu" name="sector">
                                                    <option value="">Select</option>
                                                    <option value="sect1">sect1</option>
                                                    <option value="sect2">sect2</option>
                                                    <option value="sect3">sect3</option>
                                                    <option value="sect4">sect4</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="col-md-12">
                                                <p class="valign">Center Jurisdiction (<i
                                                        class="fas fa-info-circle"></i> Refer the <a href="#" class="textcolor"> link <i
                                                        class="fas fa-external-link-square-alt"></i></a> for center
                                                Jurisdiction)</p>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Commissionerate</label>
                                                <select class="fntextbox" id="Commissionerate" name="commission">
                                                    <option value="">Select</option>
                                                    <option value="">Agra</option>
                                                    <option value="Ahamedabad-south">Ahamedabad-south</option>
                                                    <option value="Ahamedabad-north">Ahamedabad-north</option>
                                                    <option value="Bengaluru-north">Bengaluru-north</option>
                                                    <option value="Bengaluru-south">Bengaluru-south</option>

                                                </select>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Division</label>
                                                <select class="fntextbox" id="Division" name="centerDivision">
                                                    <option value="">Select</option>
                                                    <option value="Bengaluru-I">Bengaluru-I</option>
                                                    <option value="Bengaluru-II">Bengaluru-II</option>
                                                </select>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Range</label>
                                                <select class="fntextbox" id="Range" name="centerRange">
                                                    <option value="">Select</option>
                                                    <option value="Bengaluru-I">Bengaluru-IA</option>
                                                    <option value="Bengaluru-IB">Bengaluru-IB</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4">
                                                <p class="valign">Are you applying for registration as a casual taxable
                                                person? <i class="fas fa-info-circle"></i></p>
                                                <label class="switch">
                                                    <input type="checkbox" id="togBtn1">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                            <div id="datepickerhs">

                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1" id="taxable_person_table">

                                        </div>
                                        <div id=tog1hide>
                                            <div class="col-md-12 col-xs-12 bgcolor1">
                                                <div class="fleftnbg col-md-12">
                                                    <p class="valign">Option For Composition <i
                                                            class="fas fa-info-circle"></i></p>
                                                    <label class="switch">
                                                        <input type="checkbox" id="togBtn2">
                                                        <div class="slider round"></div>
                                                    </label>
                                                </div>
                                                <div class="fleftnbg" id="declaration_check_box">

                                                </div>
                                            </div>

                                            <div class="col-md-12 col-xs-12">
                                                <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                    <label class="reg m-cir">Reason to obtain registration <i
                                                            class="fas fa-info-circle"></i></label>
                                                    <select class="fntextbox" id="roor" name="reg_type">
                                                        <option value="">Select</option>
                                                        <option value="threshold">Crossing the Threshold</option>
                                                        <option value="interstate">Inter-State supply</option>
                                                        <option value="goods">Liability to pay as recipient of goods or services</option>
                                                    </select>
                                                </div>
                                                <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                    <label class="reg m-cir">Date of commencement of business</label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon" id="dp1">From</span>
                                                        <input type="date" class="form-control" name="commencedate_from"
                                                               value="">
                                                        <span class="input-group-addon" id="dptale1"><i
                                                                class="far fa-calendar-alt"></i></span>
                                                    </div>
                                                </div>
                                                <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                    <label class="reg m-cir">Date on which liability to register
                                                    arises</label>
                                                    <div class="input-group">
                                                        <input type="date" class="form-control" name="commencedate_to"
                                                               value="">
                                                        <span class="input-group-addon" id="dptale2"><i
                                                                class="far fa-calendar-alt"></i></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12 col-xs-12 bgcolor1">
                                                <div class="col-md-12 col-sm-12 col-xs-12">
                                                    <p class="details valign">Indicates Existing Registration </p>
                                                </div>
                                                <table class="table table-bordered" id="myTable">
                                                    <td><select class="form-control fxtextbox" data-codeapi-inputkey="gender" data-codeapi-defaultvalue="s"><option value="s">Select</option><option value="gst">GSTID</option><option value="tid">Temproary ID</option><option value="reg1">Registration Number Under Value Added Tax</option></select></td>
                                                    <td><input type="number" class="form-control fntextbox" data-codeapi-inputkey="age" data-codeapi-defaultvalue=""></td>
                                                    <td><input type="text" class="form-control fntextbox" data-codeapi-inputkey="name" placeholder="dd/mm/yyyy"></td>
                                                    <td> <input type="button" value="Add" class="btn btn-primary"></td></tr></table>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-cloud-upload-alt"></i> Document Upload</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 fleftng bgcolor topborder valign">
                                            <div>
                                                <p class="m-cir">Proof of details of authorised signatory</p>
                                                <div class="col-md-8 col-sm-8 col-xs-12">
                                                    <select class="fntextbox" id="Authorized_signatory_p3">
                                                        <option>Select</option>
                                                        <option>Letter of Authorisation</option>
                                                        <option>Copy of resolution passed by BoD / Managing Committee</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 col-sm-6 col-xs-12 fleftnbg">
                                                    <p><i class="fas fa-info-circle"></i> File with PDF of JPEG format is only allowed.</p>
                                                    <p><i class="fas fa-info-circle"></i> Maximum file size for upload
                                                    is 1 MB</p>
                                                    <div>
                                                        <input type="file" name="pic" accept="image/*">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        %{--<div class="btnright">--}%
                                            %{--<button type="button" class="btn btn-sm btn-default addbtn lstbtn"--}%
                                                    %{--id="bashboard_bd_back">BACK--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-primary btn-sm addbtn lstbtn"--}%
                                                    %{--id="bashboard_bd_continue">--}%
                                                %{--SAVE & CONTINUE--}%
                                            %{--</button>--}%
                                        %{--</div>--}%
                                    </div>
                                    <div class="tab-pane" id="partner">
                                        <div>
                                            <p class="mand-text">indicates mandatory fields</p>
                                        </div>

                                        <div>
                                            <p class="details">Details of priorities</p>
                                        </div>
                                        <hr>
                                        <div>
                                            <p class="details"><i class="fas fa-user"></i> Personal information</p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor">

                                            <div class="col-md-12 col-xs-12 valign">
                                                <label class="reg">Name of Person</label>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">First Name</label>
                                                <input type="text" id="firstname"
                                                       placeholder="First Name" class="fntextbox" name="firstName_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Middle Name</label>
                                                <input type="text" id="middlename"
                                                       placeholder="Middle Name" class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Last Name</label>
                                                <input type="text" id="firstname"
                                                       placeholder="Last Name" class="fntextbox">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">

                                            <div class="col-md-12 col-xs-12 valign">
                                                <label class="reg">Name of Father</label>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir" >First Name</label>
                                                <input type="text" id="ffirstname"
                                                       placeholder="First Name" class="fntextbox" name="fathersFirstName_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Middle Name</label>
                                                <input type="text" id="fmiddlename"
                                                       placeholder="Middle Name" class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Last Name</label>
                                                <input type="text" id="flastname"
                                                       placeholder="Last Name" class="fntextbox">
                                            </div>

                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir" >Date of Birth</label>
                                                <div class="input-group">
                                                    <input type="date" id="dob" placeholder="dd/mm/yyyy" class="form-control" name="date_p2">
                                                    <span class="input-group-addon" id="dob"><i
                                                            class="far fa-calendar-alt"></i></span>
                                                </div>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg"><i class="fas fa-mobile-alt"></i> Mobile
                                                Number</label>
                                                <input type="text" id="mobile_number" name="mobileNumber_p2"
                                                       placeholder="Mobile Number" class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="far fa-envelope-open"></i> Email
                                                Address</label>
                                                <input type="text" id="email_address" class="fntextbox"
                                                       name="emailAddress_p2" placeholder="email address">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">

                                            <div class="col-md-12 valign">
                                                <label class="reg m-cir">Gender</label>
                                            </div>

                                            <div class="fleftnbg col-md-6 col-xs-6" style="display: inline;">

                                                    <input type="radio" name="gender_p2" id="gender_p2m" value="male" style="display:none;">
                                                    <label for="gender_p2m"><i class="fas fa-male iconview"></i> Male</label>

                                                    <input type="radio" name="gender_p2" id="gender_p2f" value="female" style="display:none;">
                                                    <label for="gender_p2f"><i class="fas fa-female iconview"></i> Female</label>

                                                <input type="radio" name="gender_p2" id="gender_p2o" value="other" style="display:none;">
                                                    <label class="" for="gender_p2o"><img src="${assetPath(src: 'other.svg')}" class="others" > Others</label>


                                            </div>
                                            <div class="fleftnbg col-md-6 col-xs-6">
                                                <label><i class="fas fa-phone iconrotate"></i> Telephone Number (with
                                                STD Code)</label><br>
                                                <div class="col-lg-4 col-md-4 col-xs-4 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_std_pi" class="std fntextbox"
                                                           placeholder="STD">
                                                </div>
                                                <div class="col-lg-8 col-md-8 col-xs-8 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_pi" name="teltphone_pi" class="fntextbox"
                                                           placeholder="Enter Telephone Number">
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-12">
                                                    <p class="details"><i class="fas fa-id-card"></i> Identity
                                                    information</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor bottomborder">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Designation / Status</label>
                                                <input type="text" id="designation" placeholder="Enter designation"
                                                       class="fntextbox" name="designation_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Director Identification Number</label>
                                                <input type="text" id="din" placeholder="Enter DIN number"
                                                       class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-4">
                                                <p>Are you a citizen of India?</p>
                                                <label class="switch">
                                                    <input type="checkbox" id="citizen_p2" checked="checked">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>


                                        </div>

                                        <div class="col-md-12 col-xs-12">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir" id="pan_label_p2">Permanent Account Number</label>
                                                <input type="text" id="pan_num_2"
                                                       placeholder="Enter Permanent Account Number(PAN)"
                                                       class="fntextbox" name="PAN_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg" id="passport_label_p2">Passport Number(In case of Foreigner)</label>
                                                <input type="text" id="passport_num_p2" placeholder="Enter Passport number"
                                                       class="fntextbox" name="passport_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Aadhaar Number <i
                                                        class="fas fa-info-circle"></i></label>
                                                <input type="text" id="aaadhaar_num" placeholder="Enter Aadhaar Number"
                                                       class="fntextbox">
                                            </div>

                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-envelope"></i> Residential Address</p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Building No. / Flat No.</label>
                                                <input type="text" id="building_num"
                                                       placeholder="Enter Building No./ Flat No. / Door No."
                                                       class="fntextbox" name="buildingNo_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Floor No.</label>
                                                <input type="text" id="floor_num" placeholder="Enter Floor no"
                                                       class="fntextbox">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Name of the Premises / Building</label>
                                                <input type="text" id="name_building"
                                                       placeholder="Name of the Premises / Building" class="fntextbox">
                                            </div>

                                        </div>

                                        <div class="col-md-12 col-xs-12">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir" >Road / Street</label>
                                                <input type="text" id="road_street"
                                                       placeholder="Enter Road/ Street / Lane" class="fntextbox" name="road_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">City / Town / Locality / Village</label>
                                                <input type="text" id="city_town"
                                                       placeholder="Enter / Locality / Area / Village"
                                                       class="fntextbox" name="city_p2">
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Country</label>
                                                <select class="fntextbox" id="country" name="country_p2">
                                                    <option value="">Select</option>
                                                    <option value="India">India</option>
                                                    <option value="Australia">Australia</option>
                                                </select>
                                            </div>

                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1 bottomborder">

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">State</label>
                                                <select class="fntextbox" id="state" name="state_p2">
                                                    <option>Select</option>
                                                    <option value="Karanataka">Karanataka</option>
                                                    <option value="Tamilnadu">Tamilnadu</option>
                                                    <option value="kerala">Kerala</option>
                                                </select>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">District</label>
                                                <select class="fntextbox" id="district" name="district_p2">
                                                    <option value="">Select</option>
                                                    <option value="bangalore">bangalore</option>
                                                    <option value="ballari">ballari</option>
                                                </select>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">PIN Code</label>
                                                <input type="text" id="pincode" placeholder="Enter PIN NO."
                                                       class="fntextbox" name="pinCode_p2">
                                            </div>

                                        </div>

                                        <div>
                                            <p class="details"><i class="fas fa-cloud-upload-alt"></i> Document Upload
                                            </p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor bottomborder">

                                            <div id="duploads">

                                                <div class="col-md-6 col-sm-6 col-xs-12 fleftnbg">
                                                    <p class="m-cir">Upload Photograoh (of person whose information has
                                                    been given above)</p>
                                                    <p><i class="fas fa-info-circle"></i> Only JPEG file format is
                                                    allowed</p>
                                                    <p><i class="fas fa-info-circle"></i> Maximum file size for upload
                                                    is 100 KB</p>

                                                    <div>

                                                        <input type="file" name="pic" accept="image/*" name="documentUpload_p2">
                                                    </div>

                                                </div>

                                                <div class="fleftnbg col-md-6 col-sm-6 col-xs-12 text_align_center">
                                                    <button class="btn btn-primary" id="take_pic"><i
                                                            class="fas fa-camera"></i> TAKE PICTURE
                                                    </button>
                                                    <p><i class="fas fa-info-circle"></i> You can use your device camera
                                                    to take selfie photograph</p>
                                                </div>

                                            </div>

                                            <div class="col-md-12" id="duploadh">

                                                <div class="col-md-6">
                                                    <span><i class="far fa-file-image imguploadview"></i><span>
                                                        <p>Photo</p>
                                                </div>

                                                <div class="textright col-md-6">
                                                    <button type="button" class="btn btn-primary btn-danger btn-sm"><i
                                                            class="fas fa-trash-alt"></i> DELETE
                                                    </button>
                                                </div>

                                            </div>
                                        </div>

                                        <div>
                                            <p class="details">Other information</p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="col-md-12 col-xs-12">
                                                <p>Also Authorized Signatory</p>
                                                <label class="switch">
                                                    <input type="checkbox" id="citizen">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </div>

                                        %{--<div class="btnright">--}%
                                            %{--<button type="button" class="btn btn-sm btn-default addbtn lstbtn"--}%
                                                    %{--onclick="tab_click(event, 'tab1');tab2_back();">Back--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-sm btn-default addbtn lstbtn">SHOW--}%
                                            %{--LIST--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-primary btn-sm addbtn lstbtn disabled">--}%
                                                %{--ADD NEW--}%
                                            %{--</button>--}%
                                            %{--<button type="" class="btn btn-primary btn-sm addbtn lstbtn"--}%
                                                    %{--onclick="tab_click(event, 'tab3');tab2_continue();">SAVE & CONTINUE--}%
                                            %{--</button>--}%
                                        %{--</div>--}%
                                    </div>
                                    <div class="tab-pane" id="author-sign">
                                        <div>
                                            <p class="mand-text">indicates mandatory fields</p>
                                        </div>
                                        <div>
                                            <p class="details">Details of Authorized Signatory</p>
                                        </div>
                                        <hr>
                                        <div>
                                            <p class="ascbox"><input type="checkbox" class="bolder"
                                                                     id="declaration_checkbox_promoter"> Primary
                                            Authorized Signatory</p>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-user"></i> Personal information</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="col-md-12 col-xs-12 valign">
                                                <label class="reg">Name of Person</label>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">First Name</label>
                                                <input type="text" id="firstname_p3" name="firstname_p3"
                                                       placeholder="First Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Middle Name</label>
                                                <input type="text" id="middlename_p3" name="middlename_p3"
                                                       placeholder="Middle Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Last Name</label>
                                                <input type="text" id="lastname_p3" name="lastname_p3"
                                                       placeholder="Last Name" class="fntextbox">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">

                                            <div class="col-md-12 col-xs-12 valign">
                                                <label class="reg">Name of Father</label>
                                            </div>

                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">First Name</label>
                                                <input type="text" id="ffirstname_p3" name="ffirstname_p3"
                                                       placeholder="First Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Middle Name</label>
                                                <input type="text" id="fmiddlename_p3" name="fmiddlename_p3"
                                                       placeholder="Middle Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Last Name</label>
                                                <input type="text" id="flastname_p3" name="flastname_p3"
                                                       placeholder="Last Name" class="fntextbox">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Date of Birth</label>
                                                <div class="input-group">
                                                    <input type="date" id="date_p3" placeholder="dd/mm/yyyy" class="form-control"
                                                           name="date_p3">
                                                    <span class="input-group-addon" id="date_p3s"><i
                                                            class="far fa-calendar-alt"></i></span>
                                                </div>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="fas fa-mobile-alt"></i> Mobile
                                                Number</label>
                                                <input type="text" id="mobile_number" name="mobileNumber_p3"
                                                       placeholder="Mobile Number" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="far fa-envelope-open"></i> Email
                                                Address</label>
                                                <input type="text" id="email_address" class="fntextbox"
                                                       name="emailAddress_p3" placeholder="email address">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bottomborder">
                                            <div class="col-md-12 valign">
                                                <label class="reg m-cir">Gender</label>
                                            </div>
                                            <div>
                                                <div class="fleftnbg col-md-6 col-xs-6" style="display: inline;">
                                                    <input type="radio" name="gender_p3" id="gender_p3m" style="display: none;">
                                                    <label for=gender_p3m ><i class="fas fa-male iconview"></i> Male</label>
                                                    <input type="radio" name="gender_p3" id="gender_p3f" style="display: none;">
                                                    <label for=gender_p3f ><i class="fas fa-female iconview"></i> Female</label>
                                                    <input type="radio" name="gender_p3" id="gender_p3o" style="display: none;">
                                                    <label for=gender_p3o ><img src="${assetPath(src: 'other.svg')}" class="others" >Others</label>
                                                </div>
                                            </div>
                                            <div class="fleftnbg col-md-6 col-xs-6">
                                                <label><i class="fas fa-phone iconrotate"></i> Telephone Number (with
                                                STD Code)</label><br>
                                                <div class="col-lg-4 col-md-4 col-xs-4 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_std_pi" class="std fntextbox"
                                                           placeholder="STD">
                                                </div>
                                                <div class="col-lg-8 col-md-8 col-xs-8 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_pi" name="teltphone_pi" class="fntextbox"
                                                           placeholder="Enter Telephone Number">
                                                </div>
                                            </div>

                                        </div>
                                        <div>
                                            <p class="details valign"><i class="fas fa-id-card"></i> Identity information</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Designation / Status</label>
                                                <input type="text" id="designation" placeholder="Enter designation"
                                                       class="fntextbox" name="designation_p3">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Director Identification Number</label>
                                                <input type="text" id="din" placeholder="Enter DIN number"
                                                       class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4">
                                                <p>Are you a citizen of India?</p>
                                                <label class="switch">
                                                    <input type="checkbox" id="citizen_p3" name="citizen_p3" checked="checked">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </div>


                                        <div class="col-md-12 col-xs-12 bottomborder">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir" id="pan_label_p3">Permanent Account Number</label>
                                                <input type="text" id="pan_num_p3" placeholder="Enter Permanent Account Number(PAN)" class="fntextbox" name="PAN_p3">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg" id="passport_label_p3">Passport Number(In case of Foreigner)</label>
                                                <input type="text" id="passport_num_p3" placeholder="Enter Passport number" class="fntextbox" name="passport_p3">
                                                <small class="help-block hidden" id="perror_p3" data-bv-validator="notEmpty" data-bv-for="PAN_p2" data-bv-result="INVALID" style="">PAN number is required</small>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Aadhaar Number <i
                                                        class="fas fa-info-circle"></i></label>
                                                <input type="text" id="aaadhaar_num_p3" placeholder="Enter Aadhaar Number"
                                                       class="fntextbox">
                                            </div>
                                            <hr>
                                        </div>
                                        <div>
                                            <p class="details valign"><i class="fas fa-envelope"></i> Residential Address</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Building No. / Flat No.</label>
                                                <input type="text" id="building_num"
                                                       placeholder="Enter Building No./ Flat No. / Door No."
                                                       class="fntextbox" name="buildingNo_p3">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Floor No.</label>
                                                <input type="text" id="floor_num" placeholder="Enter Floor no"
                                                       class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Name of the Premises / Building</label>
                                                <input type="text" id="name_building"
                                                       placeholder="Name of the Premises / Building" class="fntextbox">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Road / Street</label>
                                                <input type="text" id="road_street"
                                                       placeholder="Enter Road/ Street / Lane" class="fntextbox" name="road_p3">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">City / Town / Locality / Village</label>
                                                <input type="text" id="city_town"
                                                       placeholder="Enter / Locality / Area / Village"
                                                       class="fntextbox" name="city_p3">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Country</label>
                                                <select class="fntextbox" id="country" name="country_p3">
                                                    <option value="">Select</option>
                                                    <option value="india">india</option>
                                                    <option value="spain">Spain</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor1 bottomborder">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">State</label>
                                                <select class="fntextbox" id="state" name="state_p3">
                                                    <option value="">Select</option>
                                                    <option value="kar">karnataka</option>
                                                    <option value="pun">punjab</option>
                                                </select>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">District</label>
                                                <select class="fntextbox" id="district" name="district_p3">
                                                    <option value="">Select</option>
                                                    <option value="ballari">karnataka</option>
                                                    <option value="bangalore">punjab</option>
                                                </select>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">PIN Code</label>
                                                <input type="text" id="pincode" placeholder="Enter PIN NO."
                                                       class="fntextbox" name="PIN_p3">
                                            </div>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-cloud-upload-alt"></i> Document Upload</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 fleftng bgcolor topborder valign">
                                            <div>
                                                <p class="m-cir">Proof of details of authorised signatory</p>
                                                <div class="col-md-8 col-sm-8 col-xs-12">
                                                    <select class="fntextbox" id="Authorized_signatory_p3" name="signatory_upload_p3">
                                                        <option value="">Select</option>
                                                        <option value="Loa">Letter of Authorisation</option>
                                                        <option value="cor">Copy of resolution passed by BoD / Managing Committee</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 col-sm-6 col-xs-12 fleftnbg">
                                                    <p><i class="fas fa-info-circle"></i> File with PDF of JPEG format is only allowed.</p>
                                                    <p><i class="fas fa-info-circle"></i> Maximum file size for upload
                                                    is 1 MB</p>
                                                    <div>
                                                        <input type="file" name="pic" accept="image/*">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 ">
                                            <div id="duploads">
                                                <div class="col-md-6 col-sm-6 col-xs-12 fleftnbg">
                                                    <p class="m-cir">Upload Photograoh (of person whose information has
                                                    been given above)</p>
                                                    <p><i class="fas fa-info-circle"></i> Only JPEG file format is
                                                    allowed</p>
                                                    <p><i class="fas fa-info-circle"></i> Maximum file size for upload
                                                    is 100 KB</p>
                                                    <div>
                                                        <input type="file" name="pic" accept="image/*">
                                                    </div>
                                                </div>
                                                <div class="fleftnbg col-md-6 col-sm-6 col-xs-12 text_align_center">
                                                    <button class="btn btn-primary" id="take_pic"><i
                                                            class="fas fa-camera"></i> TAKE PICTURE
                                                    </button>
                                                    <p><i class="fas fa-info-circle"></i>You can use your device camera
                                                    to take selfie photograph</p>
                                                </div>
                                            </div>

                                            <div class="col-md-12" id="duploadh">
                                                <div class="col-md-6 col-sm-6">
                                                    <span><i class="far fa-file-image imguploadview"></i><span>
                                                        <p>Photo</p>
                                                </div>
                                                <div class="textright col-md-6 col-sm-6">
                                                    <button type="button" class="btn btn-primary btn-danger btn-sm"><i
                                                            class="fas fa-trash-alt"></i> DELETE
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        %{--<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 text-right">--}%
                                            %{--<button type="button" class="btn btn-sm btn-default"--}%
                                                    %{--onclick="tab_click(event, 'tab2');tab3_back();">Back--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-sm btn-default">SHOW LIST</button>--}%
                                            %{--<button type="button" class="btn btn-primary btn-sm disabled">ADD NEW--}%
                                            %{--</button>--}%
                                            %{--<button type="" class="btn btn-primary btn-sm"--}%
                                                    %{--onclick="tab_click(event, 'tab4');tab3_continue();">SAVE & CONTINUE--}%
                                            %{--</button>--}%
                                        %{--</div>--}%
                                    </div>
                                    <div id="author-rep" class="tab-pane">
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <p class="mand-text">indicates mandatory fields</p>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <p class="details">Details of Authorized Representative</p>
                                        </div>
                                        <div class="col-md-12 bgcolor">
                                            <div class="fleftnbg col-md-6">
                                                <p>Do you have any Authorized Representative?</p>
                                                <label class="switch">
                                                    <input type="checkbox" id="citizen_p4">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </div>
                                        <div id="p4_show">
                                        </div>
                                    </div>

                                    <div id="pob" class="tab-pane">

                                        <div>
                                            <p class="mand-text">indicates mandatory fields</p>
                                        </div>
                                        <div>
                                            <p class="details">Details of Pricipal Place of Business</p>
                                        </div>
                                        <hr>
                                        <div>
                                            <p class="details"><i class="fas fa-envelope"></i> Address</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Building No. / Flat No.</label>
                                                <input type="text" id="building_num_pb"
                                                       placeholder="Enter Building No./ Flat No. / Door No."
                                                       class="fntextbox" name="buildingNo_p5">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Floor No.</label>
                                                <input type="text" id="floor_num_pb" placeholder="Enter Floor no"
                                                       class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Name of the Premises / Building</label>
                                                <input type="text" id="name_building_pb"
                                                       placeholder="Name of the Premises / Building" class="fntextbox">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Road / Street</label>
                                                <input type="text" id="road_street_pb"
                                                       placeholder="Enter Road/ Street / Lane" class="fntextbox" name="road_p5">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">City / Town / Locality / Village</label>
                                                <input type="text" id="city_town_pb"
                                                       placeholder="Enter / Locality / Area / Village" class="fntextbox" name="city_p5">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="col-md-4 col-xs-4 fleftnbg">
                                                <p class="valign">Name of the State</p>
                                                <p>Karnataka</p>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">District</label>
                                                <select class="fntextbox" id="dashdistrict_pb" name="district_p5">
                                                    <option value="">Select</option>
                                                    <option value="Bangalore">Bangalore</option>
                                                    <option value="Mysore">Mysore</option>
                                                </select>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">PIN Code</label>
                                                <input type="text" id="pincode_pb" placeholder="Enter PIN NO."
                                                       class="fntextbox" name="pin_p5">
                                            </div>
                                        </div>
                                        <div class="fleftnbg col-md-12 col-sm-12 col-xs-12 bottomborder">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Latitude</label>
                                                <input type="text" id="latitude" placeholder="Latitude" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Longitude</label>
                                                <input type="text" id="longitude" placeholder="Longitude" class="fntextbox">
                                            </div>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-id-card"></i> Contact information</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="far fa-envelope-open"></i> Email Address</label>
                                                <input type="text" id="email_address_pb" class="fntextbox"
                                                       name="Email_Address" placeholder="email address">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label><i class="fas fa-phone iconrotate"></i> Telephone Number (with STD
                                                Code)</label><br>
                                                <div class="col-lg-4 col-md-4 col-xs-4 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_std_pb" class="std fntextbox" placeholder="STD">
                                                </div>
                                                <div class="col-lg-8 col-md-8 col-xs-8 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_pb" name="teltphone_pi" class="fntextbox"
                                                           placeholder="Enter Telephone Number">
                                                </div>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg"><i class="fas fa-mobile-alt"></i> Mobile Number</label>
                                                <input type="text" id="mobile_number_pb" name="mobile_number"
                                                       placeholder="Mobile Number" class="fntextbox">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg"><i class="fas fa-fax"></i>Office FAX Number (with STD Code)</label>
                                                <input type="text" id="email_address_pb" class="fntextbox"
                                                       name="Email_Address" placeholder="email address">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor bottomborder">
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <p class="details m-cir"><i class="fas fa-mobile"></i> Nature of possession
                                                and premise</p>
                                                <label>Please Select</label>
                                                <select class="fntextbox" id="nature_possession" name="nature_p5">
                                                    <option value="">Select</option>
                                                    <option value="ad">abcd</option>
                                                </select>
                                            </div>
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <p class="details"><i class="fas fa-cloud-upload-alt"></i> Document Upload
                                                </p>
                                                <label>Proof of Principal Place of Bisiness</label>
                                                <select class="fntextbox" id="proof_principal_business" name="documentUpload_p5">
                                                    <option value="">Select</option>
                                                </select>
                                                <p><i class="fas fa-info-circle"></i> Only JPEG file format is allowed</p>
                                                <p><i class="fas fa-info-circle"></i> Maximum file size for upload is 100 KB
                                                </p>
                                                <input type="file" accept="file_extension|image/*">
                                            </div>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-truck iconrotate"></i> Nature of Business
                                            Activity carried out and above mentioned premises</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="bwarehouse_p5" name="businessActivity_p5" value="bw"> Bonded Warehouse</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="eou_stp_p5" name="businessActivity_p5" value="ese"> EOU / STP / EHTP</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="export_p5" name="businessActivity_p5" value="exp"> Export</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="factory_p5" name="businessActivity_p5" value="f/m"> Factory / Manufacturing</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="import_p5" name="businessActivity_p5" value="import"> Import</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="supplier_p5" name="businessActivity_p5" value="sos">Supplier of Services</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="leasing_p5" name="businessActivity_p5" value="lb"> Leasing Business</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="office_p5" name="businessActivity_p5" value="o/so"> Office / Sale Office</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="recipient_p5" name="businessActivity_p5" value="roos"> Recipient of or Services</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="Retail_p5" name="businessActivity_p5" value="rb"> Retail Business</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="warehousse_depot_p5" name="businessActivity_p5" value="w/d"> Warehouse / Depot</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="wholesale_p5" name="businessActivity_p5" value="wb"> Wholesale Business</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="works_contact_p5" name="businessActivity_p5" value="wc"> Works Contact</p>
                                            </div>
                                            <div class="fleft col-md-4 col-sm-4 col-xs-6">
                                                <p><input type="checkbox" id="others_p5" name="businessActivity_p5" value="ops"> Others (Please Specify)</p>
                                            </div>
                                            <div id="p5_show"></div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="col-md-12 col-xs-12">
                                                <p>Have Additional Place of Business</p>
                                                <label class="switch">
                                                    <input type="checkbox" id="citizen">
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </div>

                                    </div>
                                    <div id="aob" class="tab-pane">
                                        <div style="display: block !important;">
                                            <div>
                                                <p class="details valign">Details of Additional places of your Business</p>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Number of additional places</label>
                                                <input type="text" id="businessPlaces_p6" placeholder="Enter designation" class="fntextbox" name="add_places">
                                            </div>
                                            <div class="alert alert-warning col-md-12 col-xs-12" role="alert"
                                                 id="warning_message_add_business">
                                                <p><b><i class="fas fa-info-circle"></i>Important! If you need to add
                                                details on additional places of business:</b><br>1. Go to <b>Principal
                                                Place of Business tab.</b><br>2. Select Yes for <b>Have Additional
                                                Places of Business</b></p>
                                            </div>

                                        </div>
                                    </div>
                                    <div id="goodservice" class="tab-pane">
                                        <div class="row">
                                            <div class="col-md-12 col-sm-12 col-sm-12">
                                                <div class="tab" role="tabpanel">
                                                    <!-- Nav tabs -->
                                                    <ul class="nav nav-tabs" role="tablist">
                                                        <li role="presentation" class="active"><a href="#Section1"
                                                                                                  aria-controls="home"
                                                                                                  role="tab"
                                                                                                  data-toggle="tab">Goods</a>
                                                        </li>
                                                        <li role="presentation"><a href="#Section2" aria-controls="profile"
                                                                                   role="tab" data-toggle="tab">Services</a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes -->
                                                    <div class="tab-content tabs">
                                                        <div role="tabpanel" class="tab-pane in active" id="Section1">
                                                            <div>
                                                                <P class="details">Deatils of Goods / Commodities supplied
                                                                by the Business</P>
                                                                <hr>
                                                            </div>
                                                            <div class="col-md-12 col-xs-12 col-sm-12 bottomborder">
                                                                <p>Please specify top 5 Commodities</p>
                                                            </div>
                                                            <div class="col-md-12 col-xs-12 col-sm-12">
                                                                <div class="col-md-6">
                                                                    <label>Search HSN Chapter by name or Code</label>
                                                                    <input type="text" placeholder="Search HSN Chapter"
                                                                           class="fntextbox" name="chapter_code">
                                                                </div>
                                                            </div>
                                                            
                                                        </div>
                                                        <div role="tabpanel" class="tab-pane" id="Section2">
                                                            <div>
                                                                <P class="details">Deatils of Services offered by the
                                                                Business</P>
                                                                <hr>
                                                            </div>
                                                            <div class="col-md-12 mol-sm-12 col-xs-12 bottomborder">
                                                                <p>Please specify top 5 Services</p>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="col-md-6">
                                                                    <label>Search by Name or Code</label>
                                                                    <input type="text" placeholder="Search SAC"
                                                                           class="fntextbox">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="bankaccount" class="tab-pane">

                                        <div>
                                            <p class="details valign">Details of Bank Account(s)</p>
                                        </div>
                                        <div>
                                            <p class="mand-text mandpos">indicates mandatory fields</p>
                                        </div>
                                        <hr>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Total Number of Bank Accounts Maintained</label>
                                                <input type="text" id="no_bankacc" class="fntextbox"
                                                       placeholder="Enter Number of Bank Accounts" name="no_accounts">
                                            </div>
                                            <div class="alert alert-warning col-md-12 col-xs-12" role="alert"
                                                 id="warning_message_bank_acc">
                                                <p>NO records are added for Bank Accounts. Add at least one record to
                                                proceed</p>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-university"></i> Details of Bank Account</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Account number</label>
                                                <input type="text" id="acc_no" class="fntextbox"
                                                       placeholder="Enter Account Number" name="acc_no">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Type of Account</label>
                                                <select class="fntextbox" id="state" name="type_account">
                                                    <option>Select</option>
                                                    <option value="savings">Savings</option>
                                                    <option value="current">Current</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-6 col-xs-12">
                                                <label class="reg m-cir">Account number</label>
                                                <div class="fleft" style="padding: 0px;">
                                                    <input type="text" id="acc_no" class="fntextboxbtn"
                                                           placeholder="Enter Account Number">
                                                </div>
                                                <div class="fleft" style="padding: 0px;">
                                                    <button type="button" class="btnalign">GET ADDRESS</button>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fleftnbg col-md-4 col-sm-6 col-xs-12">
                                                    <div class="alert alert-warning" role="alert"
                                                         id="warning_message_bank_acc1">
                                                        <p><i class="fas fa-info-circle"></i> Don't know your IFSC? Click
                                                            <a>here</a> to find your Bank</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1 fleftnbg">
                                            <label>Branch and Address of the Bank</label>
                                            <p>HDFC Bank,Hosur Main Road,Bhommana Halli </p>
                                        </div>
                                        <div>
                                            <p class="details"><i class="fas fa-cloud-upload-alt"></i> Document Upload</p>
                                        </div>

                                        <div class="col-md-12 col-xs-12 bgcolor bottomborder">
                                            <div id="duploads">
                                                <div class="col-md-6 col-sm-6 col-xs-12 fleftnbg">
                                                    <p class="m-cir">Upload Photograoh (of person whose information has been
                                                    given above)</p>
                                                    <p><i class="fas fa-info-circle"></i> Only JPEG file format is allowed
                                                    </p>
                                                    <p><i class="fas fa-info-circle"></i> Maximum file size for upload is
                                                    100 KB</p>
                                                    <div>
                                                        <input type="file" name="pic" accept="image/*">
                                                    </div>
                                                </div>
                                                <div class="fleftnbg col-md-6 col-sm-6 col-xs-12 text_align_center">
                                                    <button class="btn btn-primary" id="take_pic"><i
                                                            class="fas fa-camera"></i> TAKE PICTURE
                                                    </button>
                                                    <p><i class="fas fa-info-circle"></i> You can use your device camera to
                                                    take selfie photograph</p>
                                                </div>
                                            </div>
                                            <div class="col-md-12" id="duploadh">
                                                <div class="col-md-6">
                                                    <span><i class="far fa-file-image imguploadview"></i><span>
                                                        <p>Photo</p>
                                                </div>
                                                <div class="textright col-md-6">
                                                    <button type="button" class="btn btn-primary btn-danger btn-sm"><i
                                                            class="fas fa-trash-alt"></i> DELETE
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div id="statespecific" class="tab-pane">
                                        <div>
                                            <p class="details valign">State Specific Information</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg">Professional Tax Employee code (EC) No.</label>
                                                <input type="text" id="ec_code"
                                                       placeholder="Enter Professional Tax EC Number" class="fntextbox" name="">
                                            </div>
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg">Professional Tax registration certificate (RC)
                                                No.</label>
                                                <input type="text" id="rc_code"
                                                       placeholder="Enter Professional Tax RC Number" class="fntextbox">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg">State Excise Lisence No.</label>
                                                <input type="text" id="excise_lisence"
                                                       placeholder="Enter State Excise Lisence Number" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg">Name of the person in whose name Excise Lisence Number
                                                held</label>
                                                <input type="text" id="ec_code"
                                                       placeholder="Enter Name of the person in whose name Excise Lisence Number held"
                                                       class="fntextbox">
                                            </div>
                                        </div>
                                    </div>
                                    <div id="verification" class="tab-pane">
                                        <div>
                                            <p class="mand-text">indicates mandatory fields</p>
                                        </div>
                                        <div>
                                            <p class="details valign"><i class="fas fa-thumbs-up"></i> Verification</p>
                                        </div>
                                        <div class="fleftnbg col-md-12 col-xs-12 bgcolor">
                                            <p><input type="checkbox" name="verification_declaration"> I hereby solemnly
                                            affirm and declare that the information given herei above is true and
                                            correcct to the best of my knowledge and belief and nothing has been
                                            concealed therefrom.</p>
                                        </div>
                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg m-cir">Name of the signatory</label>
                                                <select class="fntextbox" id="name_signatory_verification" name="signature">
                                                    <option>Select</option>
                                                    <option value="Person1">Person1</option>
                                                </select>
                                            </div>
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg m-cir">Place</label>
                                                <input type="text" id="place_verification" placeholder="Enter Place"
                                                       class="fntextbox" name="%{--place_verify--}%">
                                            </div>
                                        </div>
                                        <div class=" col-md-12 col-xs-12 bgcolor">
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg m-cir">Designation / Status</label>
                                            </div>
                                            <div class="fleftnbg col-md-6 col-sm-6 col-xs-12">
                                                <label class="reg m-cir">Date</label><br>
                                                <span>25/01/2018</span>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <p><i class="fas fa-info-circle"></i> DSC is compulsory for Companies & LLP</p>
                                        </div>
                                        <div class="col-md-12">
                                            <p><i class="fas fa-info-circle"></i> <a class="linkcolor">Facing problem using
                                            DSC? Click here for help</a></p>
                                        </div>
                                        %{--<div class="btnright">--}%
                                            %{--<button type="button" class="btn btn-sm btn-default addbtn lstbtn"--}%
                                                    %{--onclick="tab_click(event, 'tab9');tab10_back();">Back--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-sm btn-primary addbtn lstbtn"--}%
                                                    %{--data-toggle="modal" data-target="#myModal">SUBMIT WITH DSC--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-primary btn-sm addbtn lstbtn"--}%
                                                    %{--data-toggle="modal" data-target="#myModal">SUBMIT WITH E-SIGNATURE--}%
                                            %{--</button>--}%
                                            %{--<button type="button" class="btn btn-primary btn-sm addbtn lstbtn"--}%
                                                    %{--data-toggle="modal" data-target="#myModal">SUBMIT WITH EVC--}%
                                            %{--</button>--}%
                                        %{--</div>--}%
                                        <div class="modal fade" id="myModal" role="dialog">
                                            <div class="modal-dialog">

                                                <!-- Modal content-->
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">OTP Verification</h4>
                                                    </div>

                                                    <div class="fleftnbg col-md-12 col-sm-12 col-xs-12 bottomborder">
                                                        <div>
                                                            <label class="reg m-cir">Please enter OTP</label>
                                                            <input type="text" id="place_verification"
                                                                   placeholder="Enter Place" class="fntextbox">
                                                        </div>
                                                        <br>
                                                        <div class="fleftnbg alert alert-success col-md-12 col-xs-12"
                                                             role="alert" id="success_message_verification">
                                                            <p>NO records are added for Bank Accounts. Add at least one
                                                            record to proceed</p>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <div class="btnright">
                                                            <button type="button"
                                                                    class="btn btn-sm btn-default addbtn lstbtn"
                                                                    data-dismiss="modal">Close
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-primary btn-sm addbtn lstbtn">VALIDATE
                                                            OTP
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="btnright">
                                    <button type="button" class="btn btn-sm btn-default addbtn lstbtn"
                                    id="bashboard_bd_back">BACK
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-sm addbtn lstbtn"
                                    id="bashboard_bd_continue">
                                    SAVE & CONTINUE
                                    </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<footer ng-controller="footerCtrl">
    <div class="expbtn">
        <i ng-click="fexpand()" ng-class="{'fa-angle-up': expanded, 'fa-angle-down': !expanded}" class="fa fa-angle-down" title="Expand/Collapse Footer"></i>
    </div>
    <div class="ifooter " id="demo">
        <!----><ng-include src="'/pages/common/footer.html'"><!----><div class="f1" data-ng-if="!expanded">
        <div class="container">
            <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">About GST</a>
                    <ul>
                        <!--<li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/vision">Vision and Mission</a></li>
                      <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/citizencharter">Citizen Charter</a></li>-->
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/council" href="//www.gst.gov.in/about/gst/council">GST Council Structure</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">GST History</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policies</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/hyperlink" href="//www.gst.gov.in/policies/hyperlink">Hyperlink Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/disclaimer" href="//www.gst.gov.in/policies/disclaimer">Disclaimer</a></li>
                    </ul>

                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/relatedsites" href="//www.gst.gov.in/help/relatedsites">Related Sites</a>
                    <ul>
                        <li><a data-popup="true" data-ng-href="http://www.cbec.gov.in/" href="http://www.cbec.gov.in/">Central
                        Board of Excise and Customs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/statevat" href="//www.gst.gov.in/help/statevat">State
                        Tax Websites</a></li>
                        <li><a data-popup="true" data-ng-href="//india.gov.in/" href="//india.gov.in/">National
                        Portal</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12 help no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help" href="//www.gst.gov.in/help">Help</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/system/" href="//www.gst.gov.in/system/">System
                        Requirements</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/helpmodules/" href="//www.gst.gov.in/help/helpmodules/">User Manuals, Videos and FAQs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/docadvisor/" href="//www.gst.gov.in/docadvisor/">Documents Required for Registration</a></li>
                        <li><a data-popup="true" data-ng-href="https://www.youtube.com/c/GoodsandServicesTaxNetwork" href="https://www.youtube.com/c/GoodsandServicesTaxNetwork">GST Media</a></li>
                        <li><a class="disabled" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/sitemap" href="//www.gst.gov.in/sitemap">Site Map</a></li>
                        <!--<li><a target="_blank" data-ng-href="{{servers.GST_SERVICES_R1_URL}}/services/track-provisional-id-status">Track Provisional ID</a></li>-->
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 cont no-mobile scl">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/contact" href="//www.gst.gov.in/contact">Contact Us</a>
                    <ul>
                        <li>
                            <span class="contact">Help Desk Number: <br>0120-4888999</span>
                        </li>
                        <!-- <li>
                          <a data-popup="true" data-ng-if="!udata" data-ng-href="//www.entrust.com/ssl-certificates/" target="_blank" data-ng-click="seal()"><img src="{{servers.GST_CONTENT_R1_URL}}\uiassets\images\entrustSeal.png" alt="Loading..." ></a>
                        </li> -->
                        <li>
                            <span class="contact">Log/Track Your Issue:<br><a href="https://selfservice.gstsystem.in/" title="Grievance Redressal Portal for GST" target="_blank">Grievance Redressal Portal for GST</a></span>
                        </li>
                        <li class="social">
                            <a data-popup="true" href="//www.facebook.com/Goods-and-Services-Tax-1674179706229522/?fref=ts" title="Facebook"><i class="fa fa-facebook-square"></i>.</a>
                            <a data-popup="true" href="//www.youtube.com/channel/UCFYpOk92qurlO5t-Z_y-bOQ" title="Youtube"><i class="fa fa-youtube-play"></i>.</a>
                            <a data-popup="true" href="//twitter.com/askGSTech"><i class="fa fa-twitter" title="Twitter"></i>.</a>
                            <a data-popup="true" href="//www.linkedin.com/company/13204281?trk=tyah&amp;trkInfo=clickedVertical%3Acompany%2CclickedEntityId%3A13204281%2Cidx%3A4-2-9%2CtarId%3A1478268606810%2Ctas%3AGoods%20and%20Services%20" title="Linkedin"><i class="fa fa-linkedin"></i>.</a>
                        </li>
                        <!---->
                    </ul>
                </div>
            </div>
        </div>
    </div><!---->
        <div class="f2">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p>© 2016-17 Goods and Services Tax Network</p>
                        <p>Site Last Updated on 23-05-2018</p>
                        <p>Designed &amp; Developed by GSTN</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="f3">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p class="site">Site best viewed at 1024 x 768 resolution in Internet Explorer 10+, Google Chrome 49+, Firefox 45+ and Safari 6+</p>
                    </div>
                </div>
            </div>
        </div>
    </ng-include>
    </div>
</footer>

<script type="text/template" id="date-template">
    <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
        <label class="reg m-cir">Date of commencement of business</label>
    <div class="input-group">
        <span class="input-group-addon">From</span>
        <input type="date" class="form-control" name="date_Bstart" value="">
        <span class="input-group-addon"><i class="far fa-calendar-alt"></i></span>
    </div>
    </div>
    <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
        <label class="reg m-cir">Date on which liability to register
    arises</label>
    <div class="input-group">
        <input type="date" class="form-control" name="date_Bend" value="">
        <span class="input-group-addon"><i class="far fa-calendar-alt"></i></span>
    </div>
    </div>
</script>
<script type="text/template" id="tax-table">
<div class="col-md-12 col-xs-12 fleft">
    <p class="valign">Estimated supplies and Estimated Net Tax Liability</p>
    <div class="table-custom">
        <table class="table table-bordered">
            <tr>
                <th class="th"><p>Type of Tax</p></th>
                <th class="th"><p>Turnover(Rs.)</p></th>
                <th class="th"><p>Net Tax Liablity(Rs.)</p></th>
            </tr>
            <tr>
                <td>Integrated Tax</td>
                <td><input type="text" id="Integrated_tax_turnover"
                           name="Integrated_tax_turnover"
                           placeholder="Enter Integrated Tax"
                           class="fntextbox"></td>
                <td><input type="text" id="Integrated_tax_nettax"
                           name="Integrated_tax_nettax"
                           placeholder="Enter Integrated Tax"
                           class="fntextbox"></td>
            </tr>
            <tr>
                <td>Central Tax</td>
                <td><input type="text" id="central_tax_turnover"
                           name="central_tax_turnover"
                           placeholder="Enter Central Tax"
                           class="fntextbox"></td>
                <td><input type="text" id="central_tax_nettax"
                           name="central_tax_nettax"
                           placeholder="Enter Central Tax"
                           class="fntextbox"></td>
            </tr>
            <tr>
                <td>State Tax/UT Tax</td>
                <td><input type="text" id="statetax/uttax_turnover"
                           name="statetax/uttax_turnover"
                           placeholder="Enter State Tax/UT Tax"
                           class="fntextbox"></td>
                <td><input type="text" id="statetax/uttax_nattax"
                           name="statetax/uttax_nettax"
                           placeholder="Enter State Tax/UT Tax"
                           class="fntextbox"></td>
            </tr>
            <tr>
                <td>Cess</td>
                <td><input type="text" id="cess_turnover"
                           name="cess_turnover" placeholder="Enter CESS"
                           class="fntextbox"></td>
                <td><input type="text" id="cess_nettax" name="cess_nettax"
                           placeholder="Enter CESS" class="fntextbox"></td>
            </tr>
        </table>
    </div>
    <div class="alert alert-warning col-md-12 col-xs-12" role="alert"
         id="warning_message">
        <p><i style="font-weight:bold;">Warning!</i>As a taxable person,
        period of registration and Net Tax Liability (IGST,CGST,SGST and
        Cess) values are non-editable once generate the Challan. Please
        wait for 24 hours befor creating another challan,if payment
        failed and bank account is debited.</p>
    </div>
    <button class="generate_challan" id="generate_challan" type="">
        Generate Challan
    </button>
</div>

</script>

<script type="text/template" id="composition-condition">
<p><input type="checkbox" class="" id="declaration_checkbox"> I
hereby declare that the aforesaid bussiness shall abide by the
conditions and restrictions specified in the Act or Rules for
opting to pay tax under the composition levy</p>
</script>
<script type="text/templete" id="p4_hidden">
<div>
                                            <div class="col-md-12">
                                                <div class="col-md-4">
                                                    <div>
                                                        <label class="reg">Type of Authorised Representative </label>
                                                    </div>
                                                    <div class="fleft" style="display: inline;" id="p4_radio">
                                                        <input type="radio" name="p4_radiot" id="p4_radio1" style="display: none;">
                                                        <label for="p4_radio1" class="reg">GST Practitioner</label>
                                                    </div>
                                                    <div class="fleft">
                                                        <input type="radio" name="p4_radiot" id="p4_radio2" style="display: none;">
                                                        <label for="p4_radio2" class="reg">Other</label>
                                                    </div>
                                                </div>

                                                <div class="fleftnbg col-md-4">
                                                    <div>
                                                        <label class="reg m-cir">Enrollment ID</label>
                                                    </div>
                                                    <div style="display: flex">
                                                        <div class="">
                                                            <input type="textbox" name="enrollmentId_p4" class="fntextbox">
                                                        </div>
                                                        <div>
                                                            <button type="button" class="btn1 btn-sm btn-primary p4_btn">Search</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="col-md-12 col-xs-12 valign">
                                                <label class="reg">Name of Person</label>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">First Name</label>
                                                <input type="text" id="firstname_p4" name="firstName_p4"
                                                       placeholder="First Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Middle Name</label>
                                                <input type="text" id="middlename_p4" name="middlename_p4"
                                                       placeholder="Middle Name" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Last Name</label>
                                                <input type="text" id="lastname_p4" name="firstname_p4"
                                                       placeholder="Last Name" class="fntextbox">
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-xs-12">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Designation / Status</label>
                                                <input type="text" id="designation_p4" placeholder="Enter designation"
                                                       class="fntextbox" name="designation_p4">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="fas fa-mobile-alt"></i> Mobile
                                                Number</label>
                                                <input type="text" id="mobile_number_p4" name="mobileNumber_p4"
                                                       placeholder="Mobile Number" class="fntextbox">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir"><i class="far fa-envelope-open"></i> Email
                                                Address</label>
                                                <input type="text" id="email_address_p4" class="fntextbox"
                                                       name="emailAddress_p4" placeholder="email address">
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg m-cir">Permanent Account Number</label>
                                                <input type="text" id="pan_num_p4" placeholder="Enter Permanent Account Number(PAN)" class="fntextbox" name="PAN_p4">
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg">Aadhaar Number </label>
                                                <input type="text" id="aaadhaar_num_p4" placeholder="Enter Aadhaar Number"
                                                       class="fntextbox">
                                                <p><i class="fas fa-info-circle"></i> If you provide your Aadhaar here, (other than companies/LLP) you can sign your forms/returns using e-Sign based on Aadhaar without requirement of Digital Signature.</p>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-xs-12 bgcolor1">
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg"><i class="fas fa-phone iconrotate"></i> Telephone Number (with STD
                                                Code)</label><br>
                                                <div class="col-lg-4 col-md-4 col-xs-4 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_std_pb" class="std fntextbox" placeholder="STD">
                                                </div>
                                                <div class="col-lg-8 col-md-8 col-xs-8 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_pb" name="teltphone_pi" class="fntextbox"
                                                           placeholder="Telephone Number">
                                                </div>
                                            </div>
                                            <div class="fleftnbg col-md-4 col-sm-4 col-xs-12">
                                                <label class="reg"><i class="fas fa-fax"></i>Office FAX Number (with STD Code)</label><br>
                                                <div class="col-lg-4 col-md-4 col-xs-4 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_std_pb" class="std fntextbox" placeholder="STD">
                                                </div>
                                                <div class="col-lg-8 col-md-8 col-xs-8 fleftnbg" style="padding: 0px;">
                                                    <input id="telephone_pb" name="teltphone_pi" class="fntextbox"
                                                           placeholder="Fax Number">
                                                </div>
                                            </div>
                                        </div>

                                    </div>
</script>
<script type="text/templete" id="p5_hidden"><div class="col-md-6 col-xs-12 ">
                                                <label class="reg">Please Specify</label>
                                                <input type=text name=p5_specify_others class="fntextbox">
                                            </div>
</script>

<asset:javascript src="jquery-1.11.2.min.js"/>

<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.bootstrapvalidator/0.5.2/js/bootstrapValidator.min.js"></script>
<script src="https://use.fontawesome.com/39bc9f0c7e.js"></script>
<asset:javascript src="gst_dashboard_valid.js"/>
<asset:javascript src="to-dolist.js"/>
<asset:javascript src="jquery.dynamicTable-1.0.0.js"/>
</body>
</html>