<g:render template="/wonderpublish/loginChecker"></g:render>
<%if(request.getParameter("mode")==null||"".equals(request.getParameter("mode"))){%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
  $('link[data-role="baseline"]').attr('href', '');
</script>
<style>
p{
  font-size: 14px;
}

  h3{
    text-align: center;
    color: #f7931e;
  }
  .container{
    margin-top: 2rem;
  }
  .col-md-10{
    flex: 0 0 100%;
    max-width: 100%;
  }
</style>
<div class="container main-shadow">
  <g:render template="tandc"></g:render>
</div>    
<%if(request.getParameter("mode")==null||"".equals(request.getParameter("mode"))){%>
  <g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
  <asset:javascript src="analytics.js"/>
<% } %>
</body>
</html>